package cloud.demand.lab.modules.operation_view;

import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CDBHealthActualService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthOverviewService;
import cloud.demand.lab.modules.operation_view.operation_view.service.InventoryHealthGenService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandGenService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyOnTheWayReportService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.EqualUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.pugwoo.wooutils.string.StringTools;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class taskTest {
    @Autowired
    InventoryHealthGenService inventoryHealthGenService;

    @Autowired
    CDBHealthActualService cdbHealthActualService;

    @Autowired
    SupplyAndDemandGenService supplyAndDemandGenService;

    @Autowired
    SupplyOnTheWayReportService supplyOnTheWayReportService;

    @Autowired
    InventoryHealthOverviewService inventoryHealthOverviewService;

    @Autowired
    DBHelper planDBHelper;

    @Autowired
    DBHelper cdCommonDbHelper;

    @Test
    public void test1() {
        LocalDate yesterday = DateUtils.yesterday();
        String statTime = DateUtils.format(yesterday, "yyyyMMdd");
        inventoryHealthGenService.genLeisureAndBusySoldOutData(statTime);
    }
    @Test
    public void test2() {
        String statTime = "2024-10-01";
        inventoryHealthGenService.genInventoryHealthMonthData(statTime);
    }

    @Test
    public void test4() {
        CDBHealthActualReq req = new CDBHealthActualReq();
        req.setStart("2024-12-05");
        req.setEnd("2024-12-06");
        req.setZoneCategory(Arrays.asList("PRINCIPAL"));
        List<CDBHealthActualData> all = cdbHealthActualService.queryCDBHealthActualReport(req);
        System.out.println(all);
    }

    @Test
    public void test5()  {

        inventoryHealthOverviewService.sendInventoryHealthMail();

    }

    @Test
    public void test6() {
        List<StaticZoneDO> all = planDBHelper.getAll(StaticZoneDO.class);
        all = all.stream().filter(o -> o.getRegion().equals("gz")).collect(Collectors.toList());
        List<StaticZoneDO> commonAll = cdCommonDbHelper.getAll(StaticZoneDO.class);
        if (ListUtils.isNotEmpty(all) && all.size() >= commonAll.size()) {
            //metric库中的static_zone表不为空，同时数据条数不少于common的static_zone表
            EqualUtils equalUtils = new EqualUtils().ignoreListOrder(true);
            if (!equalUtils.isEqual(all, commonAll)) {
                cdCommonDbHelper.executeRaw("delete from static_zone");
                cdCommonDbHelper.insertBatchWithoutReturnId(all);
            }else {
                System.out.println("两边数据完全相同");
            }
        }else {
            System.out.println("出错");
        }
    }

    @Test
    public void test7() {
        LocalDate startDate = DateUtils.parseLocalDate("2025-06-01");
        LocalDate endDate = DateUtils.parseLocalDate("2025-08-05");
        while(!startDate.isAfter(endDate)) {
            inventoryHealthGenService.genCBSSafeInventoryData(DateUtils.formatDate(startDate));
            startDate = startDate.plusDays(1);
        }
    }

    @Test
    public void test8() {
        inventoryHealthGenService.genCBSInventoryAdjustData("2025-06-01");
    }
}
