package cloud.demand.lab.modules.longterm.cdb.service.impl;


import cloud.demand.lab.modules.longterm.cdb.service.impl.LinearFitService.DataPoint;
import cloud.demand.lab.modules.longterm.cdb.service.impl.LinearFitService.FitResult;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class LinearFitServiceTest {

    @Resource
    private LinearFitService linearFitService;

    @Test
    void performLinearFit() {

        // 示例数据
        List<DataPoint> data = ListUtils.of(
                new DataPoint(LocalDate.of(2024, 1, 1), 100),
                new DataPoint(LocalDate.of(2024, 2, 1), 110),
                new DataPoint(LocalDate.of(2024, 3, 1), 120)
        );

        // 获取拟合结果
        FitResult fitResult =   linearFitService.performLinearFit(data);
        System.out.println(JSON.toJsonFormatted(fitResult));

        // 输出拟合结果
        System.out.println("Slope: " + fitResult.getSlope());
        System.out.println("Intercept: " + fitResult.getIntercept());
        System.out.println("R²: " + fitResult.getRSquared());

        // 使用预测函数进行预测
        LocalDate futureDate = LocalDate.of(2024, 4, 1);
        double predictedValue = fitResult.getPredictionFunction().apply(futureDate);
        System.out.println("Predicted value for " + futureDate + ": " + predictedValue);

    }
}