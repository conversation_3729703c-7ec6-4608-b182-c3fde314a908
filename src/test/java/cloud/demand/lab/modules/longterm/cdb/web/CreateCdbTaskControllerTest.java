package cloud.demand.lab.modules.longterm.cdb.web;

import cloud.demand.lab.PrintUtil;
import cloud.demand.lab.modules.longterm.cdb.web.req.CreatePredictCdbTaskReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.CreatePredictCdbTaskReq.InputArgsDTO;
import cloud.demand.lab.modules.longterm.cdb.web.req.CreatePredictCdbTaskReq.NotPddInputArgsDTO;
import cloud.demand.lab.modules.longterm.cdb.web.req.CreatePredictCdbTaskReq.PddInputArgsDTO;
import cloud.demand.lab.modules.longterm.cdb.web.req.CreatePredictCdbTaskReq.ReplaceDeviceInfo;
import cloud.demand.lab.modules.longterm.cdb.web.req.CreatePredictCdbTaskReq.ReplaceInputArgsDTO;
import cloud.demand.lab.modules.longterm.cdb.web.req.PredictByHistoryTrendReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryCdbCategoryForCreateReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryCmdbServerHistoryReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryHistoryAndForecastTrendReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryReplaceDeviceYearlyReq;
import cloud.demand.lab.modules.longterm.cdb.web.resp.CreatePredictCdbTaskResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.PredictByHistoryTrendResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryCdbCategoryForCreateResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryCmdbServerHistoryResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryHistoryAndForecastTrendResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryReplaceDeviceYearlyResp;
import cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.nutz.lang.Lang;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class CreateCdbTaskControllerTest {

    @Resource
    CreateCdbTaskController controller;

    @Test
    void queryCdbCategoryForCreate() {

        QueryCdbCategoryForCreateResp resp =
                controller.queryCdbCategoryForCreate(new QueryCdbCategoryForCreateReq());
        System.out.println(JSON.toJsonFormatted(resp));
    }


    @Test
    void predictByHistoryTrend() {
        PredictByHistoryTrendReq req = new PredictByHistoryTrendReq();
        req.setCategoryId(1L);
        System.out.println(JSON.toJsonFormatted(req));
        PredictByHistoryTrendResp resp = controller.predictByHistoryTrend(req);
        System.out.println(JSON.toJsonFormatted(resp));
    }

    @Test
    void queryCdbCategoryForCreateReq() {

        QueryHistoryAndForecastTrendReq req = new QueryHistoryAndForecastTrendReq();
        req.setCategoryId(1L);

        PddInputArgsDTO pddInputArgsDTO = new PddInputArgsDTO();
        pddInputArgsDTO.setStrategyType("MIDDLE");
        pddInputArgsDTO.setEndDate("2025-12-31");
        pddInputArgsDTO.setInstanceNumGrowthRate(BigDecimal.valueOf(0.05));
        pddInputArgsDTO.setDateName("dateName");

        PddInputArgsDTO clone1 = JSON.clone(pddInputArgsDTO);
        clone1.setEndDate("2026-06-30");
        clone1.setInstanceNumGrowthRate(BigDecimal.valueOf(0.1));

        PddInputArgsDTO clone2 = JSON.clone(pddInputArgsDTO);
        clone2.setEndDate("2026-12-31");
        clone2.setInstanceNumGrowthRate(BigDecimal.valueOf(0.1));

        req.setPddInputArgs(Lang.list(pddInputArgsDTO, clone1, clone2));

        NotPddInputArgsDTO notPddInputArgsDTO = new NotPddInputArgsDTO();
        notPddInputArgsDTO.setStrategyType("MIDDLE");
        notPddInputArgsDTO.setEndDate("2025-12-31");
        notPddInputArgsDTO.setScaleGrowthRate(BigDecimal.valueOf(0.05));
        notPddInputArgsDTO.setDateName("dateName");

        NotPddInputArgsDTO clone3 = JSON.clone(notPddInputArgsDTO);
        clone1.setEndDate("2026-06-30");
        clone1.setInstanceNumGrowthRate(BigDecimal.valueOf(0.1));

        NotPddInputArgsDTO clone4 = JSON.clone(notPddInputArgsDTO);
        clone2.setEndDate("2026-12-31");
        clone2.setInstanceNumGrowthRate(BigDecimal.valueOf(0.1));

        req.setNotPddInputArgs(Lang.list(notPddInputArgsDTO, clone3, clone4));


        System.out.println(JSON.toJsonFormatted(req));

        QueryHistoryAndForecastTrendResp resp = controller.queryHistoryAndForecastTrend(req);
        System.out.println(JSON.toJsonFormatted(resp));

    }

    @Test
    void queryCmdbServerHistory() {

        QueryCmdbServerHistoryReq req = new QueryCmdbServerHistoryReq();
        req.setDataSliceDate(LocalDate.of(2025, 1, 1));
        QueryCmdbServerHistoryResp resp = controller.queryCmdbServerHistory(req);
        System.out.println(JSON.toJsonFormatted(resp));

        req = new QueryCmdbServerHistoryReq();
        req.setDataSliceDate(LocalDate.of(2025, 1, 1));
        req.setDataDiffDate(LocalDate.of(2024, 1, 1));


        System.out.println(JSON.toJsonFormatted(req));
        resp = controller.queryCmdbServerHistory(req);
        System.out.println(JSON.toJsonFormatted(resp));


    }

    @Test
    void testCreatePredictTask() {

        CreatePredictCdbTaskReq req = new CreatePredictCdbTaskReq();

        req.setCategoryId(1L);
        req.setIsEnable(true);
        req.setMemPerDevice(1024);
        req.setInstanceNumPerDevice(14);

        List<PddInputArgsDTO> pddArgs = getPddArgs();
        List<NotPddInputArgsDTO> notPddArgs = getNotPddArgs();
        List<ReplaceInputArgsDTO> replaceArgs = getReplaceArgs();

        req.setPddInputArgs(pddArgs);
        req.setNotPddInputArgs(notPddArgs);
        req.setReplaceInputArgs(replaceArgs);

        System.out.println(JSON.toJsonFormatted(req));
        CreatePredictCdbTaskResp predictTask = controller.createPredictTask(req);
        System.out.println(JSON.toJsonFormatted(predictTask));


    }

    private static List<ReplaceInputArgsDTO> getReplaceArgs() {
        List<InputArgsDTO> middleInputArgsDTO = getStrategyType(StrategyTypeEnum.MIDDLE.getCode());
        List<InputArgsDTO> cautiousInputArgsDTO = getStrategyType(StrategyTypeEnum.CAUTIOUS.getCode());
        List<InputArgsDTO> extremeInputArgsDTO = getStrategyType(StrategyTypeEnum.EXTREME.getCode());

        List<ReplaceInputArgsDTO> notPddArgs = new ArrayList<>();
        notPddArgs.addAll(getReplaceByInputArgsDTO(middleInputArgsDTO));
        notPddArgs.addAll(getReplaceByInputArgsDTO(cautiousInputArgsDTO));
        notPddArgs.addAll(getReplaceByInputArgsDTO(extremeInputArgsDTO));
        return notPddArgs;
    }

    private static List<ReplaceInputArgsDTO> getReplaceByInputArgsDTO(List<InputArgsDTO> middleInputArgsDTO) {
        List<ReplaceInputArgsDTO> replaceArgs = new ArrayList<>();
        for (InputArgsDTO inputArgsDTO : middleInputArgsDTO) {
            ReplaceInputArgsDTO replaceInputArgsDTO = new ReplaceInputArgsDTO();
            BeanUtils.copyProperties(inputArgsDTO, replaceInputArgsDTO);

            replaceArgs.add(replaceInputArgsDTO);
            List<ReplaceDeviceInfo> replaceDeviceInfos = new ArrayList<>();
            replaceInputArgsDTO.setReplaceDeviceInfos(replaceDeviceInfos);
            ReplaceDeviceInfo replaceDeviceInfo = new ReplaceDeviceInfo();
            replaceDeviceInfo.setReplaceDeviceMemNum(100000);
            replaceDeviceInfo.setReplaceDeviceNum(1000);
            replaceDeviceInfo.setReplaceDeviceType("TC-5000-TEST");
            replaceDeviceInfos.add(replaceDeviceInfo);
        }
        return replaceArgs;
    }

    private static List<NotPddInputArgsDTO> getNotPddArgs() {
        List<InputArgsDTO> middleInputArgsDTO = getStrategyType(StrategyTypeEnum.MIDDLE.getCode());
        List<InputArgsDTO> cautiousInputArgsDTO = getStrategyType(StrategyTypeEnum.CAUTIOUS.getCode());
        List<InputArgsDTO> extremeInputArgsDTO = getStrategyType(StrategyTypeEnum.EXTREME.getCode());

        List<NotPddInputArgsDTO> notPddArgs = new ArrayList<>();
        notPddArgs.addAll(getNotPddByInputArgsDTO(middleInputArgsDTO));
        notPddArgs.addAll(getNotPddByInputArgsDTO(cautiousInputArgsDTO));
        notPddArgs.addAll(getNotPddByInputArgsDTO(extremeInputArgsDTO));
        return notPddArgs;
    }


    private static List<NotPddInputArgsDTO> getNotPddByInputArgsDTO(List<InputArgsDTO> middleInputArgsDTO) {
        List<NotPddInputArgsDTO> pddArgs = new ArrayList<>();

        for (InputArgsDTO inputArgsDTO : middleInputArgsDTO) {
            NotPddInputArgsDTO notPddInputArgsDTO = new NotPddInputArgsDTO();
            BeanUtils.copyProperties(inputArgsDTO, notPddInputArgsDTO);
            notPddInputArgsDTO.setScaleGrowthRate(BigDecimal.valueOf(0.05));
            notPddInputArgsDTO.setNetGrowthMem(20000);            pddArgs.add(notPddInputArgsDTO);
        }
        return pddArgs;
    }



    private static List<PddInputArgsDTO> getPddArgs() {
        List<InputArgsDTO> middleInputArgsDTO = getStrategyType(StrategyTypeEnum.MIDDLE.getCode());
        List<InputArgsDTO> cautiousInputArgsDTO = getStrategyType(StrategyTypeEnum.CAUTIOUS.getCode());
        List<InputArgsDTO> extremeInputArgsDTO = getStrategyType(StrategyTypeEnum.EXTREME.getCode());
        List<PddInputArgsDTO> pddArgs = getByInputArgsDTO(middleInputArgsDTO);
        pddArgs.addAll(getByInputArgsDTO(cautiousInputArgsDTO));
        pddArgs.addAll(getByInputArgsDTO(extremeInputArgsDTO));
        return pddArgs;
    }

    private static List<PddInputArgsDTO> getByInputArgsDTO(List<InputArgsDTO> middleInputArgsDTO) {
        List<PddInputArgsDTO> pddArgs = new ArrayList<>();

        for (InputArgsDTO inputArgsDTO : middleInputArgsDTO) {
            PddInputArgsDTO pddInputArgsDTO = new PddInputArgsDTO();
            BeanUtils.copyProperties(inputArgsDTO, pddInputArgsDTO);
            pddInputArgsDTO.setInstanceNumGrowthRate(BigDecimal.valueOf(0.05));
            pddInputArgsDTO.setNetGrowthInstanceNum(100);
            pddArgs.add(pddInputArgsDTO);
        }
        return pddArgs;
    }

    private static List<InputArgsDTO> getStrategyType(String middle) {
        List<InputArgsDTO> ret = new ArrayList<>();
        ret.add(getStrategyType1(middle));
        ret.add(getStrategyType2(middle));
        ret.add(getStrategyType3(middle));
        return ret;
    }


    private static InputArgsDTO getStrategyType1(String middle) {
        InputArgsDTO inputArgsDTO = getStrategyTypeNoDate(middle);
        inputArgsDTO.setStartDate("2025-07-01");
        inputArgsDTO.setEndDate("2025-12-31");
        return inputArgsDTO;
    }

    private static InputArgsDTO getStrategyType2(String middle) {
        InputArgsDTO inputArgsDTO = getStrategyTypeNoDate(middle);
        inputArgsDTO.setStartDate("2026-01-01");
        inputArgsDTO.setEndDate("2026-06-30");
        return inputArgsDTO;
    }

    private static InputArgsDTO getStrategyType3(String middle) {
        InputArgsDTO inputArgsDTO = getStrategyTypeNoDate(middle);
        inputArgsDTO.setStartDate("2026-07-01");
        inputArgsDTO.setEndDate("2026-12-31");
        return inputArgsDTO;
    }


    private static InputArgsDTO getStrategyTypeNoDate(String middle) {
        InputArgsDTO inputArgsDTO = new InputArgsDTO();
        inputArgsDTO.setStrategyType(middle);
        inputArgsDTO.setDateName("testDateName");
        inputArgsDTO.setNote("test note");
        return inputArgsDTO;
    }


    @Test
    void queryReplaceDeviceYearly() {

        List<ReplaceInputArgsDTO> replaceArgs = getReplaceArgs();
        QueryReplaceDeviceYearlyReq req = new QueryReplaceDeviceYearlyReq();
        req.setCategoryId(1L);
        req.setReplaceInputArgs(replaceArgs);

        PrintUtil.printFormatJson(req);
        QueryReplaceDeviceYearlyResp resp = controller.queryReplaceDeviceYearly(req);
        PrintUtil.printFormatJson(resp);

    }

    @Test
    void queryCdbInfoDict() {

        QueryCdbDeviceTypeDictResp resp = controller.queryCdbInfoDict();
        PrintUtil.printFormatJson(resp);

    }
}