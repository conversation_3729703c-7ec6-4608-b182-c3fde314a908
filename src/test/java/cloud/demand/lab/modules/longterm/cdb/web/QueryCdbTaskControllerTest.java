package cloud.demand.lab.modules.longterm.cdb.web;


import cloud.demand.lab.PrintUtil;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryHistoryRegionScaleReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryPredictTaskInfoReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.QueryPurchaseYearlyReq;
import cloud.demand.lab.modules.longterm.cdb.web.req.TaskIdReq;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryHistoryAndForecastTrendResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryHistoryRegionScaleResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryPredictTaskInfoResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryPurchaseYearlyResp;
import cloud.demand.lab.modules.longterm.cdb.web.resp.QueryReplaceDeviceYearlyResp;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class QueryCdbTaskControllerTest {
    @Resource
    QueryCdbTaskController queryCdbTaskController;

    @Test
    void queryCategoryAndTaskList() {
        QueryCategoryAndTaskListResp resp = queryCdbTaskController.queryCategoryAndTaskList();
        PrintUtil.printFormatJson(resp);
    }

    @Test
    void queryPredictTaskInfo() {
        QueryPredictTaskInfoReq req = new QueryPredictTaskInfoReq();
        req.setTaskId(12L);

        PrintUtil.printFormatJson(req);
        QueryPredictTaskInfoResp resp = queryCdbTaskController.queryPredictTaskInfo(req);
        PrintUtil.printFormatJson(resp);

    }

    @Test
    void queryPurchaseYearly() {

        QueryPurchaseYearlyReq req = new QueryPurchaseYearlyReq();
        req.setTaskId(12L);

        PrintUtil.printFormatJson(req);
        QueryPurchaseYearlyResp resp = queryCdbTaskController.queryPurchaseYearly(req);
        PrintUtil.printFormatJson(resp);

    }

    @Test
    void queryHistoryAndForecastTrendFromTask() {

        TaskIdReq taskIdReq = new TaskIdReq();
        taskIdReq.setTaskId(11L);


        PrintUtil.printFormatJson(taskIdReq);
        QueryHistoryAndForecastTrendResp resp = queryCdbTaskController.queryHistoryAndForecastTrendFromTask(taskIdReq);
        PrintUtil.printFormatJson(resp);
    }

    @Test
    void queryHistoryRegionScale() {

        QueryHistoryRegionScaleReq req = new QueryHistoryRegionScaleReq();
        req.setTaskId(5L);

        PrintUtil.printFormatJson(req);
        QueryHistoryRegionScaleResp resp = queryCdbTaskController.queryHistoryRegionScale(req);
        PrintUtil.printFormatJson(resp);

    }

    @Test
    void queryReplaceDeviceYearlyFromTask() {

        TaskIdReq taskIdReq = new TaskIdReq();
        taskIdReq.setTaskId(12L);


        PrintUtil.printFormatJson(taskIdReq);
        QueryReplaceDeviceYearlyResp resp = queryCdbTaskController.queryReplaceDeviceYearlyFromTask(taskIdReq);
        PrintUtil.printFormatJson(resp);

    }
}