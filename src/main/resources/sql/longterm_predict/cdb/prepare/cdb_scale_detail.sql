with detail as (
    select stat_time ,customer_short_name,region_name,customhouse_title,service_num,cur_service_mem
    from std_crp.dwd_txy_cdb_scale_df
    where region_name != '(空值)'
)
select stat_time
     ,region_name,customhouse_title
     , if(customer_short_name  in ('拼多多','拼多多保供'),'拼多多','非拼多多') as customer_type
     ,sum(service_num) as cdb_num
     ,sum(cur_service_mem) as cdb_mem
from detail
where 1=1
group by stat_time,region_name,customhouse_title,customer_type