
select
    concat( erp_actual_year,'-', erp_actual_month,'-01')  as stat_time,
    device_type,
    quota_campus_name,
    count(1) as device_num
from cubes.demandMarket
where DAY = (select max (DAY) from cubes.demandMarket)
  and erp_actual_year>= '2021'
  and quota_plan_product_name in ('腾讯云-CDB', '自研上云-CDB')

  and ${CATEGORY_CONDITION}
  and ${DATE_RANGE}

group by stat_time,device_type,quota_campus_name
