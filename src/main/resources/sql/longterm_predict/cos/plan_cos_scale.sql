select DATE_FORMAT(STR_TO_DATE(data_date, '%Y%m%d'), '%Y-%m-%d') as date,
       (case when cloud_product_id_1=5 then '内部' -- 自研COS一定是内部
             when category_name_2 like '%内部%' then '内部'
             else '外部' end) as scope,
       sum(amount) as value
from end_to_end_provide.cloud_end_to_end_utilization
where
    ${CONDITION}
group by data_date,category_name_2
order by data_date,category_name_2