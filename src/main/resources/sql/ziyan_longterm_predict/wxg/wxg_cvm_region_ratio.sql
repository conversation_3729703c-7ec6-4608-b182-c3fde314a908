select DAY as stat_time ,area as customhouse_title,region_name,sum(cpu) as cur_core
FROM cubes.yunti_servers_market
WHERE
    instance_status = 'RUNNING'
  AND custom_bg_name = 'WXG微信事业群'
  AND DAY IN (
    SELECT addMonths(toDate('2020-01-01'), number) AS first_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    ) and (DAY=:predictStart or DAY=:oneYearAgo)
  and (region_name like '%华南%' or region_name like '%华东%' or region_name like '%香港%' or region_name like '%新加坡%')  -- 业务要求只弄四个地域，其他占比太少
GROUP BY DAY,area,region_name