-- buffer退回
SELECT  sum(core_amount) as demand_return_total,
        year_of_week as year
FROM yunti_demand.yunti_return_plan_cvm_item
WHERE (cvm_amount > 0 OR core_amount > 0 OR applied_cvm_amount > 0 OR applied_core_amount > 0)
  AND deleted = 0
  AND bg_name IN ('WXG微信事业群')
  AND project_name = 'buffer项目'
  AND (year_of_week > :current_year OR (year_of_week = :current_year AND month_of_week >= 1))
  AND (year_of_week < :third_year OR (year_of_week = :third_year AND month_of_week <= 12))
group by year_of_week;