with raw as (select DAY,b.CvmInstanceGroup,sum(cpu) as total_core
from cubes.yunti_servers_market a global left join sharedb.bas_obs_cloud_cvm_type b on a.instance_type=b.CvmInstanceModel
where instance_status = 'RUNNING' and custom_bg_name='WXG微信事业群'
group by DAY,b.CvmInstanceGroup)

select DAY as day,sum(total_core) as sum_core from raw 
where CvmInstanceGroup in ('标准型','大数据型','高IO型','高密度热存储','计算型','内存型')
group by DAY
order by DAY desc