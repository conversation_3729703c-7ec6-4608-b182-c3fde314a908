SELECT
    SUM(core_amount) as not_finished_sum,
    SUM(applied_core_amount) as finished_sum,  -- 需求执行净需求
    year
FROM yunti_demand.yunti_demand_cvm_item
WHERE deleted = 0
  AND dept_id NOT IN (32, 1129)                             -- 排除运营资源中心和算力平台
  AND project_name not in ('2025机房裁撤', '2026机房裁撤', '轻量云徙') -- 剔除2025/2026机房裁撤和轻量云徙
  and instance_family not IN ('GPU型','NPU型')
  AND bg_name = 'WXG微信事业群'
  AND (
    (year_of_week > :current_year OR (year_of_week = :current_year AND month_of_week >= 1))
  AND
    (year_of_week < :third_year OR (year_of_week = :third_year AND month_of_week <= 12))
    )
  AND (
    core_amount != 0 OR cvm_amount != 0 OR all_disk_amount != 0 OR ram_amount != 0
   OR applied_cvm_amount != 0 OR applied_core_amount != 0
   OR applied_ram_amount != 0 OR applied_disk_amount != 0
    )
group by year;