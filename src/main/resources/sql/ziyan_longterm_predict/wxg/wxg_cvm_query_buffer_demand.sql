SELECT
    SUM(core_amount) as not_finished_sum,
    year
FROM yunti_demand.yunti_demand_cvm_item
WHERE deleted = 0
  AND dept_id NOT IN (32, 1129)
  AND project_name = 'buffer项目'
  AND bg_name = 'WXG微信事业群'
  AND (
    (year_of_week > :current_year OR (year_of_week = :current_year AND month_of_week >= 1))
  AND
    (year_of_week < :third_year OR (year_of_week = :third_year AND month_of_week <= 12))
    )
  AND (
    core_amount != 0 OR cvm_amount != 0 OR all_disk_amount != 0 OR ram_amount != 0
   OR applied_cvm_amount != 0 OR applied_core_amount != 0
   OR applied_ram_amount != 0 OR applied_disk_amount != 0
    )
group by year;