with raw as (select DAY,b.CvmInstanceGroup,sum(cpu) as total_core,instance_type,CvmInstanceTypeCode,area
from cubes.yunti_servers_market a global left join sharedb.bas_obs_cloud_cvm_type b on a.instance_type=b.CvmInstanceModel
where
    ${CONDITION}
group by DAY,b.CvmInstanceGroup,instance_type,CvmInstanceTypeCode,area)

select DAY as stat_time,sum(total_core) as cur_core ,CvmInstanceGroup as instance_family,CvmInstanceTypeCode as instance_type,area as customhouse_title
from raw
where CvmInstanceGroup in ('标准型','大数据型','高IO型','高密度热存储','计算型','内存型')
group by DAY,instance_type,CvmInstanceGroup,CvmInstanceTypeCode,area
order by DAY desc;