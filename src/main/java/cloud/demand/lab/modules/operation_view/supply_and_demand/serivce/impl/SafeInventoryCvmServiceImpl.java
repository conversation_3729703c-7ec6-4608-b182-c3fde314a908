package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryDisassembleService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.InstanceCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ZoneCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SafeInventoryService;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18 15:05
 */
@Service
public class SafeInventoryCvmServiceImpl implements SafeInventoryService {

    @Resource
    private DictService dictService;

    @Resource
    private InventoryDisassembleService inventoryDisassembleService;

    @Resource
    private FillerService fillerService;

    @Override
    public List<SupplyDemandHedgingItemVO> queryActSafeInventory(SupplyDemandHedgingReq req) {
        InventoryDisassembleReq inventoryDisassembleReq = buildInventoryDisassembleReq(req);

        List<String> statTime = ListUtils.newArrayList(inventoryDisassembleReq.getStart(), inventoryDisassembleReq.getEnd());
        List<InventoryDisassembleResp.Item> inventoryDisassembleItemList = inventoryDisassembleService.queryInventoryDisassembleReport(inventoryDisassembleReq).getData();

        inventoryDisassembleItemList = inventoryDisassembleItemList.stream()
                .filter(item -> ListUtils.contains(statTime, o -> StringUtils.equals(o, item.getStatTime())))
                .collect(Collectors.toList());

        List<SupplyDemandHedgingItemVO> itemList = ListUtils.transform(inventoryDisassembleItemList, SupplyDemandHedgingItemVO::transform);

        fillerService.fill(itemList);
        List<String> dims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "type"));

        List<SupplyDemandHedgingItemVO> retList = SupplyDemandHedgingItemVO.mergeList(itemList, dims);
        //设置实际安全库存
        retList.forEach(item -> {
            item.setAmount(item.getSafeInventoryCalc().getActualSafeInventory());
        });
        return retList;
    }

    @Override
    public String getProductCategory() {
        return ProductCategoryEnum.CVM.getName();
    }

    public InventoryDisassembleReq buildInventoryDisassembleReq(SupplyDemandHedgingReq req) {
        InventoryDisassembleReq ret = new InventoryDisassembleReq();
        List<String> statTimeList = ListUtils.newArrayList();
        if (BooleanUtils.isTrue(req.isOverView())) {
            statTimeList.add(req.getStatTime());
        }
        if (StringUtils.isNotBlank(req.getStartStatTime())) {
            statTimeList.add(req.getStartStatTime());
        }
        if (StringUtils.isNotBlank(req.getEndStatTime())) {
            statTimeList.add(req.getEndStatTime());
        }
        if (statTimeList.size() == 1) {
            ret.setStart(statTimeList.get(0));
            ret.setEnd(statTimeList.get(0));
        } else if (statTimeList.size() >= 2) {
            ret.setStart(statTimeList.get(0));
            ret.setEnd(statTimeList.get(1));
        } else {
            return ret;
        }

        List<String> zoneCategory = ListUtils.transform(req.getZoneCategory(), item -> ZoneCategoryEnum.getTypeByTypeName(item));
        ret.setZoneCategory(zoneCategory);
        List<String> instanceCategory = ListUtils.transform(req.getInstanceCategory(), item -> InstanceCategoryEnum.getTypeByTypeName(item));
        ret.setInstanceCategory(instanceCategory);
        //国家转机型
        List<String> instanceTypeList = ListUtils.newArrayList();
        if (ListUtils.isNotEmpty(req.getInstanceGroup())) {
            Map<String, List<String>> instanceGroupMap = dictService.queryGroupToInstanceType();
            for (String instanceGroup : req.getInstanceGroup()) {
                instanceTypeList.addAll(instanceGroupMap.getOrDefault(instanceGroup, ListUtils.newArrayList()));
            }
        }
        if (ListUtils.isNotEmpty(instanceTypeList)) {
            if (ListUtils.isNotEmpty(req.getInstanceType())) {
                ret.setInstanceType(ListUtils.intersection(req.getInstanceType(), instanceTypeList));
            } else {
                ret.setInstanceType(instanceTypeList);
            }
        } else {
            ret.setInstanceType(req.getInstanceType());
        }

        ret.setCustomhouseTitle(req.getCustomhouseTitle());
        //国家转地域
        List<String> countryNameRegionList = ListUtils.newArrayList();
        if (ListUtils.isNotEmpty(req.getCountryName())) {
            Map<String, List<String>> countryRegionMap = dictService.getCountry2RegionMapping();
            for (String countryName : req.getCountryName()) {
                countryNameRegionList.addAll(countryRegionMap.getOrDefault(countryName, ListUtils.newArrayList()));
            }
        }
        ret.setAreaName(req.getAreaName());
        //地域+国家转的地域的交集
        if (ListUtils.isNotEmpty(countryNameRegionList)) {
            if (ListUtils.isNotEmpty(req.getRegionName())) {
                ret.setRegionName(ListUtils.intersection(req.getRegionName(), countryNameRegionList));
            } else {
                ret.setRegionName(countryNameRegionList);
            }
        } else {
            ret.setRegionName(req.getRegionName());
        }

        ret.setZoneName(req.getZoneName());
        ret.setTurnoverType(ListUtils.newArrayList("用户预扣"));

        return ret;
    }
}
