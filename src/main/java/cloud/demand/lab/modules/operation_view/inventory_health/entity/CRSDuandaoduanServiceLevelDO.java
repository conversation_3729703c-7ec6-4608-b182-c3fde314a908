package cloud.demand.lab.modules.operation_view.inventory_health.entity;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("t_duandaoduan_service_level")
public class CRSDuandaoduanServiceLevelDO {

    /** 地域名称<br/>Column: [regionname] */
    @Column(value = "regionname")
    private String regionname;

    /** 可用区名称<br/>Column: [zonename] */
    @Column(value = "zonename")
    private String zonename;

    /** 可用区总容量，单位G<br/>Column: [maxSellMem] */
    @Column(value = "maxSellMem")
    private Integer maxSellMem;

    /** 可用区总容量，单位G<br/>Column: [minRestMemG] */
    @Column(value = "minRestMemG")
    private Integer minRestMemG;

    /** 可用区剩余内存容量，单位G<br/>Column: [X1RealRestMem] */
    @Column(value = "X1RealRestMem")
    private Integer x1RealRestMem;

    /** 线上物理机设备数量<br/>Column: [machineCnt] */
    @Column(value = "machineCnt")
    private Integer machineCnt;

    /** 当天发货小于15分钟的实例数量<br/>Column: [Less15MinCnt] */
    @Column(value = "Less15MinCnt")
    private Integer less15MinCnt;

    /** 当天发货实例总数<br/>Column: [totalCnt] */
    @Column(value = "totalCnt")
    private Integer totalCnt;

    /** 发货速度合格百分比=Less15MinCnt/totalCnt,99.99格式,一天内啥都没发货的，填--<br/>Column: [fahuoOKPct] */
    @Column(value = "fahuoOKPct")
    private String fahuoOKPct;

    @Column(value = "update_time")
    private LocalDate updateTime;
}
