package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.*;

import java.util.List;

public interface CBSHealthActualService {

    List<CBSHealthActualData> queryCBSHealthActualTrendReport(CBSHealthActualReq req);

    DownloadBean exportCBSHealthActualTrendReport(CBSHealthActualReq req);

    List<CBSAdjustInventoryData> queryCBSAdjustInventoryReport(CBSAdjustInventoryReportReq req);

    void adjustCBSInventory(CBSAdjustInventoryReq req);

    DownloadBean exportCBSAdjustInventoryReport(CBSAdjustInventoryReportReq req);

    List<CBSAdjustVersionData> getVersion();

}
