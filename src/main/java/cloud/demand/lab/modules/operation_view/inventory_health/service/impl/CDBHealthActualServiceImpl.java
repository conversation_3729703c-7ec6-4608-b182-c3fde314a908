package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.TxyRegionInfoDTO;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CRSHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.TDSQLHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCdbServiceLevelDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCrsServiceLevelDataDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsTdsqlServiceLevelDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CDBHealthActualService;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class CDBHealthActualServiceImpl implements CDBHealthActualService {

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    DBHelper demandDBHelper;

    @Resource
    DictService dictService;

    @Resource
    CvmPlanService cvmPlanService;

    @Override
    public List<CDBHealthActualData> queryCDBHealthActualReport(CDBHealthActualReq req) {
        //通过时间维度获取时间map

        Map<String, String> dateMap = new HashMap<>();
        Map<String, Integer> countMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                countMap = getWeekCountMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                countMap = getMonthCountMap(req.getStart(), req.getEnd());
                break;
        }

        WhereSQL condition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        //获取数据
        List<DwsCdbServiceLevelDataDO> all = ckcldDBHelper.getAll(DwsCdbServiceLevelDataDO.class, condition.getSQL(),
                condition.getParams());

        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList, o -> o.getZoneName(),
                o -> o);
        //数据处理
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsCdbServiceLevelDataDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName()), o -> o);
        List<CDBHealthActualData> result = new ArrayList<>();
        for (Entry<String, List<DwsCdbServiceLevelDataDO>> entry : mapList.entrySet()) {
            List<DwsCdbServiceLevelDataDO> value = entry.getValue();
            CDBHealthActualData item = new CDBHealthActualData();
            item.setStatTime(dateMap.get(value.get(0).getStatTime()));
            item.setZoneName(value.get(0).getZoneName());
            item.setRegionName(value.get(0).getRegionName());
            item.setAreaName(value.get(0).getAreaName());
            item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(item.getZoneName());
            if (configItem != null) {
                item.setZoneCategory(configItem.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            //周和月进行平均操作
            int count = 1;
            if (req.getDateType().equals("week") || req.getDateType().equals("month")) {
                count = countMap.get(dateMap.get(value.get(0).getStatTime()));
            }
            item.setRestDisk(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getRestDisk).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            item.setRestMem(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getRestMem).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            item.setSucCount(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getSucCnt).intValue());
            item.setTotalCount(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getTotalCnt).intValue());
            item.setMinRestMem(NumberUtils.sum(value, DwsCdbServiceLevelDataDO::getMinRestMem).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            if (item.getTotalCount() != 0) {
                item.setServiceLevel(((double) item.getSucCount()) / (double) item.getTotalCount());
            }
            if (item.getMinRestMem() != 0) {
                item.setRedundancy(((double) item.getRestMem() / (double) item.getMinRestMem()));
            }
            result.add(item);
        }
        //过滤掉zoneName为空值的情况
        result = result.stream().filter(o -> StringTools.isNotBlank(o.getZoneName()))
                .collect(Collectors.toList());
        //根据国家过滤
        if (ListUtils.isNotEmpty(req.getCountry())) {
            Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
            Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
            Map<String, String> ret = new HashMap<>();
            for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
                String zone = entry.getKey();
                String region = entry.getValue();
                SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
                if (regionDO != null) {
                    ret.put(zone, regionDO.getCountryName());
                }
            }
            result = result.stream().filter(o -> {
                String country = ret.get(o.getZoneName());
                if (country == null) {
                    if (o.getCustomhouseTitle().equals("境内")) {
                        country = "中国内地";
                    }
                }
                if (country != null) {
                    return req.getCountry().contains(country);
                }
                return false;
            }).collect(
                    Collectors.toList());
        }
        return result;
    }

    public Map<String, String> getDayMap(String startDate, String endDate) {
        Map<String, String> result = new HashMap<>();
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            result.put(DateUtils.formatDate(start), DateUtils.formatDate(start));
            start = start.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(startDate.toString(), startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, Integer> getWeekCountMap(String start, String end) {
        Map<String, Integer> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end).isAfter(DateUtils.yesterday())?
                DateUtils.yesterday() : DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            String key = startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            result.put(key, result.getOrDefault(key, 0) + 1);
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(DateUtils.formatDate(startDate), yearMonth.toString());
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, Integer> getMonthCountMap(String start, String end) {
        Map<String, Integer> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end).isAfter(DateUtils.yesterday())?
                DateUtils.yesterday() : DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(yearMonth.toString(), result.getOrDefault(yearMonth.toString(), 0)  + 1);
            startDate = startDate.plusDays(1);
        }
        return result;
    }
    @Override
    public List<TxyRegionInfoDTO> queryCDBZoneNameInfo() {
        String yesterday = DateUtils.formatDate(DateUtils.yesterday());
        List<DwsCdbServiceLevelDataDO> all = ckcldDBHelper.getAll(DwsCdbServiceLevelDataDO.class, "where stat_time = ?",
                yesterday);
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        List<TxyRegionInfoDTO> result = new ArrayList<>();
        for (DwsCdbServiceLevelDataDO data : all) {
            if (StringTools.isNotBlank(data.getZoneName())) {
                TxyRegionInfoDTO item = new TxyRegionInfoDTO();
                item.setZoneName(data.getZoneName());
                item.setAreaName(data.getAreaName());
                item.setRegionName(data.getRegionName());
                item.setCustomhouseTitle(data.getCustomhouseTitle());
                SoeRegionNameCountryDO soeRegionNameCountryDO = regionNameInfoMap.get(item.getRegionName());
                if(soeRegionNameCountryDO != null) {
                    item.setCountry(soeRegionNameCountryDO.getCountryName());
                }else {
                    if (item.getCustomhouseTitle().equals("境内")) {
                        item.setCountry("中国内地");
                    }
                }
                result.add(item);
            }
        }
        result = result.stream().distinct().collect(Collectors.toList());
        return result;
    }

    @Override
    public List<CRSHealthActualData> queryCRSHealthActualReport(CDBHealthActualReq req) {

        Map<String, String> dateMap = new HashMap<>();
        Map<String, Integer> countMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                countMap = getWeekCountMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                countMap = getMonthCountMap(req.getStart(), req.getEnd());
                break;
        }

        WhereSQL condition = req.genBasicCondition();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            condition.and("country in (?)", req.getCountry());
        }
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        List<DwsCrsServiceLevelDataDO> all = ckcldDBHelper.getAll(DwsCrsServiceLevelDataDO.class, condition.getSQL(),
                condition.getParams());
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList, o -> o.getZoneName(),
                o -> o);
        //数据处理
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsCrsServiceLevelDataDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName()), o -> o);
        List<CRSHealthActualData> result = new ArrayList<>();
        for (Entry<String, List<DwsCrsServiceLevelDataDO>> entry : mapList.entrySet()) {
            List<DwsCrsServiceLevelDataDO> value = entry.getValue();
            CRSHealthActualData item = new CRSHealthActualData();
            item.setStatTime(dateMap.get(value.get(0).getStatTime()));
            item.setZoneName(value.get(0).getZoneName());
            item.setRegionName(value.get(0).getRegionName());
            item.setAreaName(value.get(0).getAreaName());
            item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
            item.setCountry(value.get(0).getCountry());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(item.getZoneName());
            if (configItem != null) {
                item.setZoneCategory(configItem.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            int count = 1;
            if (req.getDateType().equals("week") || req.getDateType().equals("month")) {
                count = countMap.get(dateMap.get(value.get(0).getStatTime()));
            }
            item.setRestMem(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getRestMem).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            item.setSucCount(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getSucCnt).intValue());
            item.setTotalCount(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getTotalCnt).intValue());
            item.setMinRestMem(NumberUtils.sum(value, DwsCrsServiceLevelDataDO::getMinRestMem).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            if (item.getTotalCount() != 0) {
                item.setServiceLevel(((double) item.getSucCount()) / (double) item.getTotalCount());
            }
            if (item.getMinRestMem() != 0) {
                item.setRedundancy(((double) item.getRestMem() / (double) item.getMinRestMem()));
            }
            result.add(item);
        }
        return result;
    }

    @Override
    public List<TDSQLHealthActualData> queryTDSQLHealthActualReport(CDBHealthActualReq req) {
        Map<String, String> dateMap = new HashMap<>();
        Map<String, Integer> countMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                countMap = getWeekCountMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                countMap = getMonthCountMap(req.getStart(), req.getEnd());
                break;
        }

        WhereSQL condition = req.genBasicCondition();
        if (ListUtils.isNotEmpty(req.getCountry())) {
            condition.and("country in (?)", req.getCountry());
        }
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        List<DwsTdsqlServiceLevelDataDfDO> all = ckcldDBHelper.getAll(DwsTdsqlServiceLevelDataDfDO.class, condition.getSQL(), condition.getParams());
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList, o -> o.getZoneName(),
                o -> o);
        //数据处理
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsTdsqlServiceLevelDataDfDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName()), o -> o);
        List<TDSQLHealthActualData> result = new ArrayList<>();
        for (Entry<String, List<DwsTdsqlServiceLevelDataDfDO>> entry : mapList.entrySet()) {
            List<DwsTdsqlServiceLevelDataDfDO> value = entry.getValue();
            TDSQLHealthActualData item = new TDSQLHealthActualData();
            item.setStatTime(dateMap.get(value.get(0).getStatTime()));
            item.setZoneName(value.get(0).getZoneName());
            item.setRegionName(value.get(0).getRegionName());
            item.setAreaName(value.get(0).getAreaName());
            item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
            item.setCountry(value.get(0).getCountry());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(item.getZoneName());
            if (configItem != null) {
                item.setZoneCategory(configItem.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            int count = 1;
            if (req.getDateType().equals("week") || req.getDateType().equals("month")) {
                count = countMap.get(dateMap.get(value.get(0).getStatTime()));
            }
            item.setRestMem(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getRestMem).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            item.setRestDisk(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getRestDisk).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            item.setSucCount(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSucCnt).intValue());
            item.setTotalCount(NumberUtils.sum(value,DwsTdsqlServiceLevelDataDfDO::getTotalCnt).intValue());
            item.setMinRestMem(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getMinRestMem).divide(BigDecimal.valueOf(count), 4,
                    RoundingMode.HALF_UP).intValue());
            item.setSoldOutCount(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSoldOutCnt).intValue());
            item.setSoldCount(NumberUtils.sum(value, DwsTdsqlServiceLevelDataDfDO::getSoldCnt).intValue());
            BigDecimal slap = BigDecimal.valueOf(0.7);
            BigDecimal soldRt = BigDecimal.ZERO;
            if (item.getTotalCount() > 0) {
                slap = BigDecimal.valueOf(item.getSucCount()).divide(BigDecimal.valueOf(item.getTotalCount()), 4,
                        RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.7));
            }
            if (item.getSoldCount() > 0) {
                soldRt = BigDecimal.valueOf(item.getSoldOutCount()).divide(BigDecimal.valueOf(item.getSoldCount()), 4, RoundingMode.HALF_UP);
            }
            slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
            item.setServiceLevel(slap.doubleValue());
            if (item.getMinRestMem() != 0) {
                item.setRedundancy(((double) item.getRestMem() / (double) item.getMinRestMem()));
            }
            result.add(item);
        }

        return result;
    }

}
