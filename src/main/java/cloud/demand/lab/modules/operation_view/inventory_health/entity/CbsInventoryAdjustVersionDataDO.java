package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSAdjustInventoryData;
import lombok.Data;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.springframework.beans.BeanUtils;

@Data
@Table("cbs_inventory_adjust_version_data")
public class CbsInventoryAdjustVersionDataDO extends BaseDO {

    /** 版本<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 云盘类型<br/>Column: [volume_type] */
    @Column(value = "volume_type")
    private String volumeType;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 最低库存<br/>Column: [min_stock] */
    @Column(value = "min_stock")
    private BigDecimal minStock;

    /** 缓冲库存<br/>Column: [buffer_stock] */
    @Column(value = "buffer_stock")
    private BigDecimal bufferStock;

    /** 干预前安全库存<br/>Column: [origin_safe_inventory] */
    @Column(value = "origin_safe_inventory")
    private BigDecimal originSafeInventory;

    /** 干预后安全库存<br/>Column: [adjust_safe_inventory] */
    @Column(value = "adjust_safe_inventory")
    private BigDecimal adjustSafeInventory;

    /** 好料库存-云徙<br/>Column: [good_stock_remove] */
    @Column(value = "good_stock_remove")
    private BigDecimal goodStockRemove;

    /** 好料库存-老旧置换<br/>Column: [good_stock_old] */
    @Column(value = "good_stock_old")
    private BigDecimal goodStockOld;



}
