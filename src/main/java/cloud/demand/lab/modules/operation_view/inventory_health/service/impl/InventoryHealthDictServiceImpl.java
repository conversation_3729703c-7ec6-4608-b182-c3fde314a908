package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.InstanceTypeCombineDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.BasMrpV2PurchaseInstanceTypeDO;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

@Service
@Slf4j
public class InventoryHealthDictServiceImpl implements InventoryHealthDictService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DictService dictService;

    @Override
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 36000)
    public InstanceTypeCombineDTO queryAllCombineInstanceType() {
        //  1、查询全部机型组合配置
        String sql = "select instance_type_combine from inventory_health_instance_type_combine_config where deleted = 0";
        List<String> raw = demandDBHelper.getRaw(String.class, sql);

        //  2、构建组合机型的结果结构
        InstanceTypeCombineDTO dto = new InstanceTypeCombineDTO();
        List<Set<String>> result = Lang.list();
        dto.setCombination(result);

        //  3、收集配置机型组合中的全量机型:用来监控同一个机型是不是出现在多个机型组合中，如果有就告警
        Set<String> allInstanceType = Lang.set();
        //  记录冗余组合机型用来提示用户查看
        Set<String> redunInstanceType = Lang.set();

        for (String eachCombination : raw) {
            eachCombination = eachCombination.trim();
            String[] split = eachCombination.split(";");
            //  收集当前组合的机型列表
            Set<String> curResult = Lang.set();
            boolean flag = true;
            for (String instanceType : split) {
                if (!allInstanceType.add(instanceType)) {
                    redunInstanceType.add(instanceType);
                    flag = false;
                }
                curResult.add(instanceType);
            }
            if (flag) {
                result.add(curResult);
            }
        }
        if (redunInstanceType.size() != 0) {
            dto.setErrorMsg("存在机型出现在多个组合中：" + Strings.join(redunInstanceType, '/'));
        }
        return dto;
    }

    /**
     * 获取未来13周的节假周信息
     * @return HolidayWeekDTO
     *          date:未来第n周-当周周一
     *          year:节假周-年信息
     *          week:节假周-周信息
     */
    @Override
    public List<HolidayWeekInfoDTO> getHolidayWeekInfoBase(LocalDate baseDate, int n){
        //  获取当周周一
        Date curWeekMonday = DateUtils.parse(DateUtils.formatDate(baseDate.with(DayOfWeek.MONDAY)));
        List<Tuple2<String, Integer>> mondayList = Lang.list();
        if (n >= 0) {
            for (int i = 1; i <= n; i++) {
                //  n > 0时，先加7天再存
                curWeekMonday = DateUtils.addTime(curWeekMonday, Calendar.DATE, 7);
                mondayList.add(Tuple.of(DateUtils.formatDate(curWeekMonday), i));
            }
        } else {
            Date firstWeekMonday = DateUtils.addTime(curWeekMonday, Calendar.WEEK_OF_MONTH, n);
            for (int i = n; i < 0 ; i++) {
                //  n < 0时，先存下再加7天
                mondayList.add(Tuple.of(DateUtils.formatDate(firstWeekMonday), i));
                firstWeekMonday = DateUtils.addTime(firstWeekMonday, Calendar.DATE, 7);
            }
        }

        List<HolidayWeekInfoDTO> dtos = Lang.list();

        ListUtils.forEach(mondayList, o -> {
            HolidayWeekInfoDTO dto = new HolidayWeekInfoDTO();
            dto.setStartDate(o._1);
            dto.setWeekNFromNow(o._2);
            ResPlanHolidayWeekDO holidayWeekInfo = dictService.getHolidayWeekInfoByDate(o._1);
            dto.setYear(holidayWeekInfo.getYear());
            dto.setMonth(holidayWeekInfo.getMonth());
            dto.setWeek(holidayWeekInfo.getWeek());
            dto.setEndDate(holidayWeekInfo.getEnd());
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public List<HolidayWeekInfoDTO> getHolidayWeekFromCurrent(LocalDate baseDate, int n) {
        //  获取当周周一
        Date curWeekMonday = DateUtils.parse(DateUtils.formatDate(baseDate.with(DayOfWeek.MONDAY)));
        List<Tuple2<String, Integer>> mondayList = Lang.list();

        if (n >= 0) {
            // 当周
            mondayList.add(Tuple.of(DateUtils.formatDate(curWeekMonday), 0));
            for (int i = 1; i <= n; i++) {
                //  n > 0时，先加7天再存
                curWeekMonday = DateUtils.addTime(curWeekMonday, Calendar.DATE, 7);
                mondayList.add(Tuple.of(DateUtils.formatDate(curWeekMonday), i));
            }
        } else {
            Date firstWeekMonday = DateUtils.addTime(curWeekMonday, Calendar.WEEK_OF_MONTH, n);
            for (int i = n; i < 0 ; i++) {
                //  n < 0时，先存下再加7天
                mondayList.add(Tuple.of(DateUtils.formatDate(firstWeekMonday), i));
                firstWeekMonday = DateUtils.addTime(firstWeekMonday, Calendar.DATE, 7);
            }
            // 当周
            mondayList.add(Tuple.of(DateUtils.formatDate(firstWeekMonday), 0));
        }

        List<HolidayWeekInfoDTO> dtos = Lang.list();

        ListUtils.forEach(mondayList, o -> {
            HolidayWeekInfoDTO dto = new HolidayWeekInfoDTO();
            dto.setStartDate(o._1);
            dto.setWeekNFromNow(o._2);
            ResPlanHolidayWeekDO holidayWeekInfo = dictService.getHolidayWeekInfoByDate(o._1);
            dto.setYear(holidayWeekInfo.getYear());
            dto.setMonth(holidayWeekInfo.getMonth());
            dto.setWeek(holidayWeekInfo.getWeek());
            dto.setEndDate(holidayWeekInfo.getEnd());
            dtos.add(dto);
        });
        return dtos;
    }

    @Override
    public List<HolidayWeekInfoDTO> getHolidayWeekBetWeen(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("startDate   " + startDate + " endDate " + endDate);
        }
        LocalDate curWeekMonday = startDate.with(DayOfWeek.MONDAY);
        List<String> mondayList = Lang.list();
        mondayList.add(DateUtils.formatDate(curWeekMonday));
        while(!curWeekMonday.isAfter(endDate)){
            curWeekMonday = curWeekMonday.plusDays(7);
            mondayList.add(DateUtils.formatDate(curWeekMonday));
        }
        List<HolidayWeekInfoDTO> dtos = Lang.list();

        ListUtils.forEach(mondayList, o -> {
            HolidayWeekInfoDTO dto = new HolidayWeekInfoDTO();
            dto.setStartDate(o);
            ResPlanHolidayWeekDO holidayWeekInfo = dictService.getHolidayWeekInfoByDate(o);
            dto.setYear(holidayWeekInfo.getYear());
            dto.setMonth(holidayWeekInfo.getMonth());
            dto.setWeek(holidayWeekInfo.getWeek());
            dto.setEndDate(holidayWeekInfo.getEnd());
            dtos.add(dto);
        });
        return dtos;

    }

    @Override
    public ResPlanHolidayWeekDO getFirstWeekByMonth(YearMonth yearMonth) {
        return getFirstWeekByMonth(yearMonth.getYear(), yearMonth.getMonthValue());
    }

    @Override
    public ResPlanHolidayWeekDO getFirstWeekByMonth(int year, int month) {
        List<ResPlanHolidayWeekDO> holidayWeekInfoByYearMonth = dictService.getHolidayWeekInfoByYearMonth(year,month);
        if (ListUtils.isEmpty(holidayWeekInfoByYearMonth)){
            throw new ITException(String.format("查询指定年月内节假周数据为空：年月：【%s】", SoeCommonUtils.getYearMonth(year,month)));
        }
        // 起始时间升序，最早的就是月首周
        holidayWeekInfoByYearMonth.sort((o1, o2) -> StringUtils.compare(o1.getStart(),o2.getStart()));
        return holidayWeekInfoByYearMonth.get(0);
    }

    @Override
    public ResPlanHolidayWeekDO getFirstWeekByDate(LocalDate date) {
        if (date == null){
            throw new ITException("查询节假日所在月首周时指定日期不能为空");
        }
        ResPlanHolidayWeekDO holidayWeekInfoByDate = dictService.getHolidayWeekInfoByDate(DateUtils.formatDate(date));
        if (holidayWeekInfoByDate == null){
            throw new ITException(String.format("查询指定日期节假周数据为空：：【%s】", date));
        }
        return getFirstWeekByMonth(holidayWeekInfoByDate.getYear(),holidayWeekInfoByDate.getMonth());
    }

    @Override
    public List<String> getPurchaseInstanceType() {
        // 获取采购机型
        List<BasMrpV2PurchaseInstanceTypeDO> all = demandDBHelper.getAll(BasMrpV2PurchaseInstanceTypeDO.class,
                "where default_flag = '1' and is_purchase = '是'");
        return all.stream().map(BasMrpV2PurchaseInstanceTypeDO::getInstanceType).collect(Collectors.toList());
    }
}
