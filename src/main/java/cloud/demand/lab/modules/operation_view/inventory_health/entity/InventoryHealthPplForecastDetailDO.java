package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("inventory_health_ppl_forecast_detail")
public class InventoryHealthPplForecastDetailDO extends BaseDO {

    /** 统计时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 开始购买日期<br/>Column: [begin_buy_date] */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /** 结束购买日期<br/>Column: [end_buy_date] */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title", insertValueScript = "''")
    private String customhouseTitle;

    /** 地域<br/>Column: [area_name] */
    @Column(value = "area_name", insertValueScript = "''")
    private String areaName;

    /** 区域<br/>Column: [region_name] */
    @Column(value = "region_name", insertValueScript = "''")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name", insertValueScript = "''")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type", insertValueScript = "''")
    private String instanceType;

    /** 实例规格<br/>Column: [instance_model] */
    @Column(value = "instance_model", insertValueScript = "''")
    private String instanceModel;

    /** 节假年<br/>Column: [holiday_year] */
    @Column(value = "holiday_year")
    private Integer holidayYear;

    /** 节假月<br/>Column: [holiday_month] */
    @Column(value = "holiday_month")
    private Integer holidayMonth;

    /** 节假周<br/>Column: [holiday_week] */
    @Column(value = "holiday_week")
    private Integer holidayWeek;

    /** 总核心数<br/>Column: [total_core] */
    @Column(value = "total_core")
    private Integer totalCore;

    /** 新增总核心数<br/>Column: [total_core_new] */
    @Column(value = "total_core_new")
    private Integer totalCoreNew;

    /** 退回总核心数<br/>Column: [total_core_return] */
    @Column(value = "total_core_return")
    private Integer totalCoreReturn;

    /** 需求来源(FORECAST:预测、IMPORT:报备）<br/>Column: [source] */
    @Column(value = "source")
    private String source;

    /** 模型预测的数据来源，内领和行业<br/>Column: [forecast_model_source_type] */
    @Column(value = "forecast_model_source_type")
    private String forecastModelSourceType;

}
