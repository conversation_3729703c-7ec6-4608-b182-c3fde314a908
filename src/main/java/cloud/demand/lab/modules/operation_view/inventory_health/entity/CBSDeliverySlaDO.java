package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.CBSDeliveryData;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class CBSDeliverySlaDO{

    /** 园区 */
    @Column("campus")
    private String campus;
    /** 设备类型 */
    @Column("device_type")
    private String deviceType;
    /** 期望交付时间 */
    @Column("quota_use_time")
    private LocalDateTime quotaUseTime;
    /** ERP 交付时间 */
    @Column("erp_actual_date")
    private LocalDateTime erpActualDate;
    /** 星云提单时间 */
    @Column("xy_create_time")
    private LocalDateTime xyCreateTime;
    /** ERP提单时间 */
    @Column("quota_create_time")
    private LocalDateTime quotaCreateTime;
    /** cloud交付时间 */
    @Column(value = "cloud_delivery_time")
    private LocalDateTime cloudDeliveryTime;
    /** 提单时间 diff，按小时精确到天，例如 1.5 天 */
    @Column("create_time_diff")
    private BigDecimal createTimeDiff;
    /** 交付状态：如期交付、延期交付 */
    @Column("delivery_status")
    private String deliveryStatus;
    /** 交付时长（天）：ERP交付时间与期望交付时间的差值 */
    @Column("delivery_days")
    private BigDecimal deliveryDays;
    /** 交付设备台数 */
    @Column("num")
    private Integer num;

    /**
     * CBS设备类型族
     */
    @Column("device_family")
    private String deviceFamily;

    public CBSDeliveryData transform() {
        CBSDeliveryData data = new CBSDeliveryData();
        data.setCampus(campus);
        data.setDeviceType(deviceType);
        data.setDeviceFamily(deviceFamily);
        if (DateUtils.formatDate(xyCreateTime).equals("1970-01-01")) {
            data.setXyApprovalDays(BigDecimal.ZERO);
        } else {
            data.setXyApprovalDays(createTimeDiff);
        }
        data.setDeliveryStatus(deliveryStatus);
        data.setDeliveryDays(deliveryDays);
        data.setNum(num);
        data.setSla(deliveryDays.add(data.getXyApprovalDays()));
        return data;
    }
}
