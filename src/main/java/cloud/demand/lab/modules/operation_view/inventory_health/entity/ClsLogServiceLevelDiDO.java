package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("dwd_cls_log_service_level_di")
public class ClsLogServiceLevelDiDO {
    /** 切片时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private String createTime;

    /** 年月<br/>Column: [year_month] */
    @Column(value = "year_month")
    private String yearMonth;

    /** 网络产品:CLB/EIP<br/>Column: [network_product] */
    @Column(value = "network_product")
    private String networkProduct;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title", insertValueScript = "'(空值)'")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name", insertValueScript = "'(空值)'")
    private String countryName;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name", insertValueScript = "'(空值)'")
    private String areaName;

    /** 地域code<br/>Column: [region_code] */
    @Column(value = "region_code", insertValueScript = "'(空值)'")
    private String regionCode;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name", insertValueScript = "'(空值)'")
    private String regionName;

    /** 资源不足错误请求数<br/>Column: [insufficient_resource_failed] */
    @Column(value = "insufficient_resource_failed")
    private Integer insufficientResourceFailed;

    /** 内部错误请求数<br/>Column: [internal_failed] */
    @Column(value = "internal_failed")
    private Integer internalFailed;

    /** 总请求数<br/>Column: [total] */
    @Column(value = "total")
    private Integer total;
}
