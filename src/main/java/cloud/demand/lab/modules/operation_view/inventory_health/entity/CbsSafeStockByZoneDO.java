package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("cbs_safe_stock_by_zone")
public class CbsSafeStockByZoneDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "region")
    private String region;

    @Column(value = "cbs_zone_id")
    private Integer cbsZoneId;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "volume_type")
    private String volumeType;

    @Column(value = "online_phy_cap")
    private Float onlinePhyCap;

    @Column(value = "avg_sold_cap")
    private Float avgSoldCap;

    @Column(value = "std_sold_cap")
    private Float stdSoldCap;

    @Column(value = "avg_block_inc")
    private Float avgBlockInc;

    @Column(value = "std_block_inc")
    private Float stdBlockInc;

    @Column(value = "foreign_flag")
    private Integer foreignFlag;

    @Column(value = "region_flag")
    private String regionFlag;

    @Column(value = "position_flag")
    private Integer positionFlag;

    @Column(value = "sold_rate")
    private Float soldRate;

    @Column(value = "block_usage_rate")
    private Float blockUsageRate;

    @Column(value = "user_block_usage_rate")
    private Float userBlockUsageRate;

    @Column(value = "sold_rate_to_want_cap")
    private Float soldRateToWantCap;

    @Column(value = "min_stock")
    private Float minStock;

    @Column(value = "buffer_stock")
    private Float bufferStock;

    @Column(value = "safe_stock")
    private Float safeStock;

    @Column(value = "total_avg_band")
    private Float totalAvgBand;

    @Column(value = "has_sold_avg_band")
    private Float hasSoldAvgBand;

    @Column(value = "avg_band_rate")
    private Float avgBandRate;

    @Column(value = "online_good_stock")
    private Float onlineGoodStock;

    @Column(value = "offline_stock")
    private Float offlineStock;

    @Column(value = "online_bad_stock")
    private Float onlineBadStock;

    @Column(value = "online_idle_stock")
    private Float onlineIdleStock;

    @Column(value = "bill_disk_size")
    private Float billDiskSize;

    @Column(value = "inner_disk_size")
    private Float innerDiskSize;

    @Column(value = "upper_block")
    private Float upperBlock;

    @Column(value = "max_can_use_cap")
    private Float maxCanUseCap;

    @Column(value = "theory_over_sold_rate")
    private Float theoryOverSoldRate;

    @Column(value = "add_date")
    private LocalDate addDate;

    @Column(value = "refresh_time")
    private LocalDateTime refreshTime;

    @Column(value = "sold_gap")
    private Float soldGap;

    @Column(value = "latest_avg_sold_cap")
    private Float latestAvgSoldCap;

    @Column(value = "latest_avg_block_inc")
    private Float latestAvgBlockInc;

    @Column(value = "latest_std_sold_cap")
    private Float latestStdSoldCap;

    @Column(value = "lh_avg_sold_cap")
    private Float lhAvgSoldCap;

    @Column(value = "latest_lh_avg_sold_cap")
    private Float latestLhAvgSoldCap;

    @Column(value = "lh_disk_size")
    private Float lhDiskSize;

    @Column(value = "zone_type")
    private String zoneType;

    @Column(value = "schedulable_stock")
    private Float schedulableStock;

    @Column(value = "unschedulable_stock")
    private Float unschedulableStock;

    @Column(value = "offline_stock_sold_rate")
    private Float offlineStockSoldRate;

    @Column(value = "region_name")
    private String regionName;

}
