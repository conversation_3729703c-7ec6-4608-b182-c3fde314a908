package cloud.demand.lab.modules.operation_view.inventory_health.service;

import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.*;

import java.util.List;

public interface TurnoverInventoryAnalysisService {

    List<TurnoverInventoryAnalysisData> queryTurnoverAnalysisReportInventory(TurnoverInventoryAnalysisReq req);

    List<TurnoverInventoryCustomerData> queryTurnoverAnalysisReportCustomer(TurnoverInventoryAnalysisReq req);

    List<PreDeductTurnoverData> queryPreDeductInventoryReport(PreDeductAnalysisReq req);

    List<NotPreDeductInventoryData> queryNotPreDeductInventoryReport(NotPreDeductInventoryReq req);

    TurnoverInventoryTodayData queryTurnoverAnalysisReportToday(TurnoverInventoryTodayReq req);

    DownloadBean exportTurnoverAnalysisReportInventory(TurnoverInventoryAnalysisReq req);

    DownloadBean exportTurnoverAnalysisReportCustomer(TurnoverInventoryAnalysisReq req);

    DownloadBean exportPreDeductInventoryReport(PreDeductAnalysisReq req);

    DownloadBean exportNotPreDeductInventoryReport(NotPreDeductInventoryReq req);

    TurnoverAvgResp queryTurnoverAnalysisAvg(TurnoverInventoryAnalysisReq req);
}
