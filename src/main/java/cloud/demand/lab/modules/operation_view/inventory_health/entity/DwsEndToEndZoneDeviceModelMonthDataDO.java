package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryHealthOverviewServiceImpl.EndToEndTrendDTO;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.InventoryHealthGenServiceImpl.EndToEndMonthDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

@Data
@ToString
@Table("dws_end_to_end_zone_device_model_month_data")
public class DwsEndToEndZoneDeviceModelMonthDataDO {

    @Column(value = "stat_time")
    private String statTime;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 分类一<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 售卖类型<br/>Column: [sale_type] */
    @Column(value = "sale_type")
    private String saleType;

    /** 分类二<br/>Column: [item] */
    @Column(value = "item")
    private String item;

    /** cpu核数<br/>Column: [cpu_count] */
    @Column(value = "cpu_count")
    private BigDecimal cpuCount;


    public static DwsEndToEndZoneDeviceModelMonthDataDO genData(EndToEndMonthDTO dto, Map<String, String> map, String yearmonth) {
        DwsEndToEndZoneDeviceModelMonthDataDO item = new DwsEndToEndZoneDeviceModelMonthDataDO();
        BeanUtils.copyProperties(dto, item);
        item.setStatTime(yearmonth);
        String instance = map.get(dto.getDeviceType());
        item.setInstanceType(StringUtils.isBlank(instance)? "(空值)" : instance);
        item.setCpuCount(dto.getCoreNum());
        if (StringUtils.isBlank(item.getZoneName())) {
            item.setZoneName("(空值)");
            item.setCustomhouseTitle("(空值)");
            item.setAreaName("(空值)");
            item.setRegionName("(空值)");
        }
        return item;
    }

    public static List<EndToEndTrendDTO> genEndToEnd(List<DwsEndToEndZoneDeviceModelMonthDataDO> list) {
        List<EndToEndTrendDTO> result = new ArrayList<>();
        for (DwsEndToEndZoneDeviceModelMonthDataDO item : list) {
            EndToEndTrendDTO data = new EndToEndTrendDTO();
            data.setItem(item.getItem());
            data.setSaleType(item.getSaleType());
            data.setCategory(item.getCategory());
            data.setZoneName(item.getZoneName());
            data.setDeviceType(item.getDeviceType());
            data.setCoreNum(item.getCpuCount());
            data.setDataDate(item.getStatTime());
            result.add(data);
        }
        return result;
    }

}