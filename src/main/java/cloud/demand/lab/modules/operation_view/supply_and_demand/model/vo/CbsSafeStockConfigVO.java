package cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CbsSafeStockConfigVO {

    @Column(value = "version_code")
    private String versionCode;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "volume_type")
    private String volumeType;

    @Column(value = "safe_stock")
    private BigDecimal safeStock;


    public CbsSafeStockByZoneVO transform() {
        CbsSafeStockByZoneVO vo = new CbsSafeStockByZoneVO();
        vo.setZoneName(zoneName);
        vo.setVolumeType(volumeType);
        vo.setSafeStock(safeStock);
        return vo;
    }
}
