package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("industry_demand_product_enum")
public class IndustryDemandProductEnumDO {

    /**
     * 主键<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /**
     * 删除标记（也叫启用标记）<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    private String industry;

    /**
     * 产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;
}
