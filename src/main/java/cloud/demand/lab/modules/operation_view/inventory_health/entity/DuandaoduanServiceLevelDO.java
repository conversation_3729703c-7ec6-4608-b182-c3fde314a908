package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("t_duandaoduan_service_level")
public class DuandaoduanServiceLevelDO {

    /** cdb类型，高可用类型<br/>Column: [cdbtype] */
    @Column(value = "cdbtype", isKey = true)
    private String cdbtype;

    @Column(value = "fdate", isKey = true)
    private String fdate;

    /** 地区信息取自CMDB<br/>Column: [regionname] */
    @Column(value = "regionname", isKey = true)
    private String regionname;

    /** 区信息取自CMDB虚拟可用区<br/>Column: [zonename] */
    @Column(value = "zonename", isKey = true)
    private String zonename;

    /** 线上最大装机内存容量，单位G<br/>Column: [maxSellMem] */
    @Column(value = "maxSellMem")
    private Integer maxSellMem;

    @Column(value = "minRestMemG")
    private Integer minRestMemG;

    /** 线上剩余内存量 G<br/>Column: [X1RealRestMem] */
    @Column(value = "X1RealRestMem")
    private Integer x1RealRestMem;

    /** 线上剩余磁盘量，折算的；G<br/>Column: [X1RealRestDiskG] */
    @Column(value = "X1RealRestDiskG")
    private Integer x1RealRestDiskG;

    /** 线上物理机设备数量<br/>Column: [machineCnt] */
    @Column(value = "machineCnt")
    private Integer machineCnt;

    /** 当天发货小于15分钟的实例数量<br/>Column: [Less15MinCnt] */
    @Column(value = "Less15MinCnt")
    private Integer less15MinCnt;

    /** 当天发货实例总数<br/>Column: [totalCnt] */
    @Column(value = "totalCnt")
    private Integer totalCnt;

    /** 发货速度合格百分比,99.99格式,一天内啥都没发货的，填--<br/>Column: [fahuoOKPct] */
    @Column(value = "fahuoOKPct")
    private String fahuoOKPct;

    @Column(value = "updatetime")
    private LocalDateTime updatetime;

}
