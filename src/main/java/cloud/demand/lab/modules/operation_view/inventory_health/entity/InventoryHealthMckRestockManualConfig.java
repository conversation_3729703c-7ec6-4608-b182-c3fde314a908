package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Table("inventory_health_mck_restock_manual_config")
public class InventoryHealthMckRestockManualConfig extends BaseDO implements Cloneable {
    /**
     * 配置的日期，每天会有一份配置，后一天继承前一天的
     */
    @Column("date")
    private String date;
    // CVM
    @Column("product_type")
    private String productType;
    @Column("instance_type")
    private String instanceType;
    @Column("customhouse_title")
    private String customhouseTitle;
    @Column("area_name")
    private String areaName;
    @Column("region_name")
    private String regionName;
    @Column("zone_name")
    private String zoneName;
    @Column("year")
    private Integer year;
    @Column("week")
    private Integer week;
    /**
     * 供应 - 当前下单（T + 6）
     */
    @Column("supply_current_order")
    private BigDecimal supplyCurrentOrder;
    /**
     * 供应 - 向资源方传递需求信号
     */
    @Column("supply_to_downstream_demand")
    private BigDecimal supplyToDownstreamDemand;
    /**
     * 供应 - 人工调整
     */
    @Column("supply_manual_config")
    private BigDecimal supplyManualConfig;

    /**
     *供应 - 执行人工调整操作的原因
     */
    @Column("supply_manual_config_reason")
    private String supplyManualConfigReason;

    /**
     * 需求 - 人工调整
     */
    @Column("demand_manual_config")
    private BigDecimal demandManualConfig;

    /**
     * 需求 - 执行人工调整操作的原因
     */
    @Column("demand_manual_config_reason")
    private String demandManualConfigReason;

    /**
     * 安全库存 - 人工调整
     */
    @Column("safe_inventory_manual_config")
    private BigDecimal safeInventoryManualConfig;

    /**
     * 安全库存 - 执行人工调整操作的原因
     */
    @Column("safe_inventory_manual_config_reason")
    private String safeInventoryManualConfigReason;

    public InventoryHealthMckRestockManualConfig clone() {
        try {
            InventoryHealthMckRestockManualConfig clone = (InventoryHealthMckRestockManualConfig) super.clone();
            clone.setId(null);
            clone.setCreateTime(null);
            clone.setUpdateTime(null);
            clone.setDeleted(false);
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException(e);
        }
    }
}
