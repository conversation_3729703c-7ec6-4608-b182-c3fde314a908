package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.modules.operation_view.entity.p2p.FileNameAndBytesDTO;
import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.ORMUtils.WhereContent;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.common_dict.DO.ResPlanHolidayWeekDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.common_dict.service.impl.DictServiceImpl;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.GraphDateItem;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.ZoneInsSlaDto;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthActualResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.TrendGraphResp.PointItem;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.trend_graph.MoveDetailDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.trend_graph.PurchaseDetailDTO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsLeisureAndBusySoldOutDataDfDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.CustomerCustomGroupEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthAlgorithm;
import cloud.demand.lab.modules.operation_view.inventory_health.service.TrendGraphService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.EffectiveAlgorithmDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.EnableDeliveryDataForAlgorithmDTO;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.service.OperationViewService2;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view.web.OperationViewController2;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.nutz.lang.Lang;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class TrendGraphServiceImpl implements TrendGraphService {

    @Autowired
    OperationViewService2 operationViewService2;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper ckcubesDBHelper;

    private ExecutorService threadPool = Executors.newFixedThreadPool(10);

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource DBHelper demandDBHelper;

    @Override
    public TrendGraphResp queryInventoryHealthTrendGraph(TrendGraphReq req) {
        switch (req.getCardType()) {
            case Sla:
                return querySlaTrendGraph(req);
            case Inventory:
                return queryInventoryTrendGraph(req);
            case Redundancy:
                return queryRedundancyTrendGraph(req);
            case Supply:
                return querySupplyTrendGraph(req);
        }
        return new TrendGraphResp();
    }

    public String getInstanceTypeNameForExport(TrendGraphReq req) {
        if (req.getInstanceType().size() == 1) {
            return req.getInstanceType().get(0);
        } else {
            return "";
        }
    }

    public String getZoneNameForExport(TrendGraphReq req) {
        if (req.getZoneName().size() == 1) {
            return req.getZoneName().get(0);
        } else {
            return "";
        }
    }

    @Override
    public FileNameAndBytesDTO exportPurchaseDetailExcel(TrendGraphReq req) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/purchase-detail.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        String instanceTypeName = getInstanceTypeNameForExport(req);
        String zoneName = getZoneNameForExport(req);
        String date = req.getDate().toString();

        String fileNamePrefix = "库存健康趋势-采购明细(" + instanceTypeName + "-" + zoneName + "-" + date + ")";
        // 从数据库查询需要的数据
        List<String> campus = getCampusByZoneName(req.getZoneName());
        List<String> device = getDeviceTypeByInstanceType(req.getInstanceType());
        List<LocalDate> days = getDaysFromParams(req);
        String dateColum = req.supplyPurchaseTypeToTimeField();

        WhereSQL sql = new WhereSQL();
        sql.and("DAY = ?", req.getDate().toString())
                .and("quota_plan_product_name = '腾讯云CVM'")
                .and("cloud_business_type = '云业务'")
                .and("quota_device_class in (?)", device)
                .and("campus in (?)", campus)
                .and("toDate(" + dateColum + ") in (?)", days);
        sql.addGroupBy("quota_id", "quota_device_class", "campus", "xy_industry", "xy_customer_name", "quota_use_time", "erp_actual_date", "cloud_delivery_time");
        List<PurchaseDetailDTO> data = ckcubesDBHelper.getAll(PurchaseDetailDTO.class, sql.getSQL(), sql.getParams());
        // 填充可用区，机型数据
        DictService dictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, StaticZoneDO> campus2ZoneInfMap = dictService.getCampus2ZoneInfoMap();
        Map<String, String> deviceType2InstanceTypeMap = dictService.getCsigDeviceTypeToInstanceTypeMap();
        data.stream().forEach(item -> {
            StaticZoneDO zoneInfo = campus2ZoneInfMap.get(item.getCampus());

            if (zoneInfo != null) {
                item.setRegionType(zoneInfo.getCustomhouseTitle());
                item.setAreaName(zoneInfo.getAreaName());
                item.setRegionName(zoneInfo.getRegionName());
                item.setZoneName(zoneInfo.getZoneName());
            }

            item.setInstanceType(deviceType2InstanceTypeMap.get(item.getQuotaDeviceClass()));
        });

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    @Override
    public FileNameAndBytesDTO exportMoveDetailExcel(TrendGraphReq req) {
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/move-detail.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        String instanceTypeName = getInstanceTypeNameForExport(req);
        String zoneName = getZoneNameForExport(req);
        String date = req.getDate().toString();

        String fileNamePrefix = "库存健康趋势-搬迁明细(" + instanceTypeName + "-" + zoneName + "-" + date + ")";
        // 从数据库查询需要的数据
        List<String> campus = getCampusByZoneName(req.getZoneName());
        List<String> device = getDeviceTypeByInstanceType(req.getInstanceType());
        List<LocalDate> days = getDaysFromParams(req);
        String dateColum = req.supplyMoveTypeToTimeField();

        WhereSQL inSql = new WhereSQL();
        inSql.and("DAY = ?", req.getDate().toString())
                .and("BelongBnName='[N][腾讯云CVM_宿主机]'")
                .and("Bussiness2Name not like '[自研上云]%'")
                .and("SCampusName not in (?)", campus)
                .and("DCampusName in (?)", campus)
                .and("SSvrDeviceClass in (?)", device)
                .and("date(" + dateColum + ") in (?)", days);
        inSql.addGroupBy("DetailCode", "SSvrDeviceClass", "SCampusName", "DCampusName", "AffirmFinishTime", "FinishTime", "RequestLevel", "Reason");
        List<MoveDetailDTO> inData = ckcubesDBHelper.getAll(MoveDetailDTO.class, inSql.getSQL(), inSql.getParams());

        WhereSQL outSql = new WhereSQL();
        outSql.and("DAY = ?", req.getDate().toString())
                .and("BelongBnName='[N][腾讯云CVM_宿主机]'")
                .and("Bussiness2Name not like '[自研上云]%'")
                .and("SCampusName in (?)", campus)
                .and("DCampusName not in (?)", campus)
                .and("SSvrDeviceClass in (?)", device)
                .and("date(" + dateColum + ") in (?)", days);
        outSql.addGroupBy("DetailCode", "SSvrDeviceClass", "SCampusName", "DCampusName", "AffirmFinishTime", "FinishTime", "RequestLevel", "Reason");
        List<MoveDetailDTO> outData = ckcubesDBHelper.getAll(MoveDetailDTO.class, outSql.getSQL(), outSql.getParams());

        List<MoveDetailDTO> data = inData;
        if (outData != null && outData.size() > 0) {
            for (MoveDetailDTO outItem : outData) {
                outItem.setCore(-outItem.getCore());
                outItem.setNum(-outItem.getNum());
                data.add(outItem);
            }
        }
        log.info(data.toString());
        // 填充可用区，机型数据
        DictService dictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, StaticZoneDO> campus2ZoneInfMap = dictService.getCampus2ZoneInfoMap();
        Map<String, String> deviceType2InstanceTypeMap = dictService.getCsigDeviceTypeToInstanceTypeMap();
        data.stream().forEach(item -> {
            StaticZoneDO sZoneInfo = campus2ZoneInfMap.get(item.getSourceCampusName());

            if (sZoneInfo != null) {
                item.setSourceRegionType(sZoneInfo.getCustomhouseTitle());
                item.setSourceAreaName(sZoneInfo.getAreaName());
                item.setSourceRegionName(sZoneInfo.getRegionName());
                item.setSourceZoneName(sZoneInfo.getZoneName());
            }

            StaticZoneDO dZoneInfo = campus2ZoneInfMap.get(item.getDestCampusName());

            if (dZoneInfo != null) {
                item.setDestRegionType(dZoneInfo.getCustomhouseTitle());
                item.setDestAreaName(dZoneInfo.getAreaName());
                item.setDestRegionName(dZoneInfo.getRegionName());
                item.setDestZoneName(dZoneInfo.getZoneName());
            }

            item.setInstanceType(deviceType2InstanceTypeMap.get(item.getDeviceType()));
        });
        log.info(data.toString());
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        FileNameAndBytesDTO result = new FileNameAndBytesDTO();
        result.setBytes(out.toByteArray());
        result.setFileName(fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");
        return result;
    }

    public List<LocalDate> getDaysFromParams(TrendGraphReq req) {
        List<LocalDate> days = ListUtils.newList();

        if ("day".equals(req.getDateRange())) {
            // 计算 req.beginDate 以及 req.endDate 之间的日期，都包含
            val beginDate = req.getBeginDate();
            val endDate = req.getEndDate();

            LocalDate tempDate = beginDate;

            while (!tempDate.isAfter(endDate)) {
                days.add(tempDate);
                tempDate = tempDate.plusDays(1);
            }
        } else if ("week".equals(req.getDateRange())) {
            // 如果是周维度，返回周范围内的所有日期
            List<ResPlanHolidayWeekDO> dtos = getHolidayWeekInfo(req.getBeginDate(), req.getEndDate());
            if (dtos.size() > 0) {
                LocalDate startDate = DateUtils.parseLocalDate(dtos.get(0).getStart());
                LocalDate endDate = DateUtils.parseLocalDate(dtos.get(dtos.size() - 1).getEnd());

                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (endDate.isAfter(yesterday)) {
                    endDate = yesterday;
                }

                while (!startDate.isAfter(endDate)) {
                    days.add(startDate);
                    startDate = startDate.plusDays(1);
                }
            } else {
                throw BizException.makeThrow("所选时间范围内查询不到节假周信息，无法查询到周维度的数据。如有问题，请联系 brightwwu");
            }
        } else {
            // 取开始日期所在月的第一天作为月维度的开始日期，取结束日期所在月的最后一天（或者昨天，两者取较小的值）作为结束日期
            val beginDate = LocalDate.of(req.getBeginDate().getYear(), req.getBeginDate().getMonth(), 1);
            LocalDate yesterday = LocalDate.now().minusDays(1);
            LocalDate nextMonth = req.getEndDate().plusMonths(1);
            LocalDate endDate = LocalDate.of(nextMonth.getYear(), nextMonth.getMonth(), 1).minusDays(1);

            if (endDate.isAfter(yesterday)) {
                endDate = yesterday;
            }

            LocalDate tempDate = beginDate;

            while (!tempDate.isAfter(endDate)) {
                days.add(tempDate);
                tempDate = tempDate.plusDays(1);
            }
        }

        // 对 days 进行去重
        List<LocalDate> finalDays = ListUtils.newList();

        for (LocalDate day : days) {
            if (!finalDays.contains(day)) {
                finalDays.add(day);
            }
        }

        return finalDays;
    }

    /**
     * 与 getDaysFromParams 的区别是：对于周和月维度，范围是周末和月末那一天的切片
     * @param req
     * @return
     */
    private List<LocalDate> getDaysFromParamsSlice(TrendGraphReq req) {
        List<LocalDate> days = ListUtils.newList();

        if ("day".equals(req.getDateRange())) {
            // 计算 req.beginDate 以及 req.endDate 之间的日期，都包含
            val beginDate = req.getBeginDate();
            val endDate = req.getEndDate();

            LocalDate tempDate = beginDate;

            while (!tempDate.isAfter(endDate)) {
                days.add(tempDate);
                tempDate = tempDate.plusDays(1);
            }
        } else if ("week".equals(req.getDateRange())) {
            // 如果是周维度，返回周末的那一天
            days = getHolidayWeekInfo(req.getBeginDate(), req.getEndDate()).stream().map(w -> {
                LocalDate d = LocalDate.parse(w.getEnd());
                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (d.isAfter(yesterday)) {
                    return yesterday;
                }

                return d;
            }).collect(Collectors.toList());
        } else {
            // 如果是月维度，返回开始日期到结束日期之间，每个月的月末数据
            val beginDate = req.getBeginDate();
            LocalDate beginMonth = LocalDate.of(beginDate.getYear(), beginDate.getMonth(), 1);

            while (!beginMonth.isAfter(req.getEndDate())) {
                LocalDate nextMonth = beginMonth.plusMonths(1);
                LocalDate endOfThisMonth = LocalDate.of(nextMonth.getYear(), nextMonth.getMonth(), 1).minusDays(1);

                if (endOfThisMonth.isAfter(req.getEndDate())) {
                    endOfThisMonth = req.getEndDate();
                }
                // 对于当月，最迟取昨天
                if (endOfThisMonth.isAfter(DateUtils.yesterday())) {
                    endOfThisMonth = DateUtils.yesterday();
                }

                days.add(endOfThisMonth);
                beginMonth = nextMonth;
            }
        }

        // 对 days 进行去重
        List<LocalDate> finalDays = ListUtils.newList();

        for (LocalDate day : days) {
            if (!finalDays.contains(day)) {
                finalDays.add(day);
            }
        }

        return finalDays;
    }

    /**
     * 服务水平&售罄情况
     *
     * @param req
     * @return
     */
    public TrendGraphResp querySlaTrendGraph(TrendGraphReq req) {
        if (req.getSoldType() == null) {
            req.setSoldType("全时段");
        }
        List<LocalDate> days = getDaysFromParams(req);

        List<String> verList = days.stream().map(s -> DateUtils.format(s, "yyyyMMdd")).collect(Collectors.toList());

        WhereContent w = new WhereContent();
        w.andIn("zone_name", req.getZoneName());
        w.andIn("instance_family", req.getInstanceType());
        w.andIn("version", verList);

        if ("包年包月".equals(req.getSlaType()) || "弹性".equals(req.getSlaType())) {
            w.andEqual("demand_type", req.getSlaType());
        }

        //增加客户类型筛选条件
        if ("MEDIUM_LONG_TAIL".equals(req.getCustomerCustomGroup())) {
            w.andEqual("customer_type", "中长尾");
        }else if ("LIST_REPORT".equals(req.getCustomerCustomGroup())) {
            w.andEqual("customer_type", "头部");
        }
        //获取需过滤的可用区+实例类型
        Set<String> zoneInstance = getZoneInstanceSet(req);
        List<DwsCloudServerLevelDO> all = ckcldDBHelper.getAll(DwsCloudServerLevelDO.class, w.getSql(), w.getParams());
        //过滤掉
        if (!zoneInstance.isEmpty()) {
            all = all.stream().filter(o->{
                String str = String.join("@", o.getZoneName(), o.getInstanceFamily());
                return !(zoneInstance.contains(str));
            }).collect(Collectors.toList());
        }
        Map<String, ZoneInsSlaDto> rs = DwsCloudToZoneInsSla(all);

        Map<String, List<BigDecimal>> soldMap = getSoldMap(req, verList, zoneInstance);

        List<PointItem> sla = new ArrayList<>();
        List<PointItem> sold = new ArrayList<>();

        // 如果是日维度，返回每天的，如果是周维度，返回一周的平均
        if ("day".equals(req.getDateRange())) {
            verList.forEach(date -> {
                if (rs.containsKey(date)) {
                    ZoneInsSlaDto zdo = rs.get(date);
                    List<BigDecimal> soldData = soldMap.get(date);
                    BigDecimal slas = getSlaData(zdo, soldData);
                    sla.add(new PointItem(date, slas));
                } else {
                    ZoneInsSlaDto zdo = new ZoneInsSlaDto();
                    zdo.setApiSuccTotal(BigDecimal.ZERO);
                    zdo.setApiTotal(BigDecimal.ZERO);
                    List<BigDecimal> soldData = soldMap.get(date);
                    BigDecimal slas = getSlaData(zdo, soldData);
                    sla.add(new PointItem(date, slas));
                }
                if (soldMap.containsKey(date)) {
                    BigDecimal temp = BigDecimal.ZERO;
                    List<BigDecimal> soldRate = soldMap.get(date);
                    if (soldRate.get(0).intValue() > 0) {
                        temp = soldRate.get(1).divide(soldRate.get(0), 4, BigDecimal.ROUND_UP);
                    }
                    sold.add(new PointItem(date, temp));
                } else {
                    sold.add(new PointItem(date, BigDecimal.ZERO));
                }
            });
        } else if ("week".equals(req.getDateRange())) {
            List<ResPlanHolidayWeekDO> dtos = getHolidayWeekInfo(req.getBeginDate(), req.getEndDate());

            for (ResPlanHolidayWeekDO item : dtos) {
                // 遍历一周的开始日期到结束日期，累加每天的量
                LocalDate start = DateUtils.parseLocalDate(item.getStart());
                LocalDate end = DateUtils.parseLocalDate(item.getEnd());

                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (end.isAfter(yesterday)) {
                    end = yesterday;
                }

                if (start.isAfter(end)) {
                    continue;
                }

                String display = DateUtils.format(end, "yyyyMMdd");
                ZoneInsSlaDto zoneInsSlaDto = new ZoneInsSlaDto();
                zoneInsSlaDto.setApiSuccTotal(BigDecimal.ZERO);
                zoneInsSlaDto.setApiTotal(BigDecimal.ZERO);
                zoneInsSlaDto.setSumSold(BigDecimal.ZERO);
                zoneInsSlaDto.setSumSoldOut(BigDecimal.ZERO);
                BigDecimal weekSold = BigDecimal.ZERO;
                BigDecimal weekSoldOut = BigDecimal.ZERO;
                // 将一周的总数求和，然后再求服务水平和售罄率
                while (!start.isAfter(end)) {
                    String date = DateUtils.format(start, "yyyyMMdd");

                    if (rs.containsKey(date)) {
                        ZoneInsSlaDto zdo = rs.get(date);
                        zoneInsSlaDto.setApiSuccTotal(zoneInsSlaDto.getApiSuccTotal().add(zdo.getApiSuccTotal()));
                        zoneInsSlaDto.setApiTotal(zoneInsSlaDto.getApiTotal().add(zdo.getApiTotal()));
                        zoneInsSlaDto.setSumSold(zoneInsSlaDto.getSumSold().add(zdo.getSumSold()));
                        zoneInsSlaDto.setSumSoldOut(zoneInsSlaDto.getSumSoldOut().add(zdo.getSumSoldOut()));
                    }

                    if (soldMap.containsKey(date)) {
                        weekSold = weekSold.add(soldMap.get(date).get(0));
                        weekSoldOut = weekSoldOut.add(soldMap.get(date).get(1));
                    }

                    start = start.plusDays(1);
                }
                // 一周是一条数据
                BigDecimal slas = getSlaData(zoneInsSlaDto, ListUtils.newList(weekSold, weekSoldOut));
                PointItem slaItem = new PointItem(display, slas);
                BigDecimal soldRate = BigDecimal.ZERO;
                if (weekSold.intValue() > 0) {
                    soldRate = weekSoldOut.divide(weekSold, 4, BigDecimal.ROUND_UP);
                }
                PointItem soldItem = new PointItem(display, soldRate);

                sla.add(slaItem);
                sold.add(soldItem);
            }
        } else {
            // 月份 --> [[某天的sla， 某天的sold], [某天的sla， 某天的sold], ...]
            Map<String, List<ZoneInsSlaDto>> slaSoldMap = new HashMap<>();
            Map<String, List<List<BigDecimal>>> soldMonthMap = new HashMap<>();

            for (LocalDate day : days) {
                String currentMonth = DateUtils.format(day, "yyyyMM");

                slaSoldMap.putIfAbsent(currentMonth, ListUtils.newList());
                soldMonthMap.putIfAbsent(currentMonth,ListUtils.newList());
                String date = DateUtils.format(day, "yyyyMMdd");

                if (rs.containsKey(date)) {
                    slaSoldMap.get(currentMonth).add(rs.get(date));
                }
                if (soldMap.containsKey(date)) {
                    soldMonthMap.get(currentMonth).add(soldMap.get(date));
                }
            }

            slaSoldMap.entrySet().stream().forEach(e -> {
                List<ZoneInsSlaDto> slaSold = e.getValue();
                List<List<BigDecimal>> soldRate = soldMonthMap.get(e.getKey());
                ZoneInsSlaDto zoneInsSlaDto = new ZoneInsSlaDto();
                zoneInsSlaDto.setApiSuccTotal(BigDecimal.ZERO);
                zoneInsSlaDto.setApiTotal(BigDecimal.ZERO);
                zoneInsSlaDto.setSumSold(BigDecimal.ZERO);
                zoneInsSlaDto.setSumSoldOut(BigDecimal.ZERO);
                BigDecimal monthSold = BigDecimal.ZERO;
                BigDecimal monthSoldOut = BigDecimal.ZERO;

                for (ZoneInsSlaDto zdo : slaSold) {
                    zoneInsSlaDto.setApiSuccTotal(zoneInsSlaDto.getApiSuccTotal().add(zdo.getApiSuccTotal()));
                    zoneInsSlaDto.setApiTotal(zoneInsSlaDto.getApiTotal().add(zdo.getApiTotal()));
                    zoneInsSlaDto.setSumSold(zoneInsSlaDto.getSumSold().add(zdo.getSumSold()));
                    zoneInsSlaDto.setSumSoldOut(zoneInsSlaDto.getSumSoldOut().add(zdo.getSumSoldOut()));
                }
                for (List<BigDecimal> bigDecimals : soldRate) {
                    monthSold = monthSold.add(bigDecimals.get(0));
                    monthSoldOut = monthSoldOut.add(bigDecimals.get(1));
                }
                BigDecimal temp = BigDecimal.ZERO;
                if (monthSold.intValue() > 0) {
                    temp = monthSoldOut.divide(monthSold, 4, BigDecimal.ROUND_UP);
                }
                BigDecimal slas = getSlaData(zoneInsSlaDto, ListUtils.newList(monthSold, monthSoldOut));
                PointItem slaItem = new PointItem(e.getKey(), slas);
                PointItem soldItem = new PointItem(e.getKey(), temp);

                sla.add(slaItem);
                sold.add(soldItem);
            });
        }

        Map data = new HashMap();
        data.put("服务水平", sla);
        data.put("售罄比例", sold);
        TrendGraphResp resp = new TrendGraphResp();
        resp.setType(req.getCardType());
        resp.setData(data);
        return resp;
    }

    public BigDecimal getSlaData(ZoneInsSlaDto zdo, List<BigDecimal> soldOut) {
        BigDecimal slap = BigDecimal.valueOf(0.7);
        BigDecimal soldRt = BigDecimal.ZERO;
        if (zdo.getApiTotal().intValue() > 0) {
            slap = zdo.getApiSuccTotal().divide(zdo.getApiTotal(), 4, BigDecimal.ROUND_UP)
                    .multiply(BigDecimal.valueOf(0.7));
        }
        if (soldOut != null) {
            if (soldOut.get(0).intValue() > 0) {
                soldRt = soldOut.get(1).divide(soldOut.get(0), 4, BigDecimal.ROUND_UP);
            }
        }
        slap = slap.add(BigDecimal.valueOf(1.0000).subtract(soldRt).multiply(BigDecimal.valueOf(0.3)));
        return slap;
    }

    private Map<String, ZoneInsSlaDto> DwsCloudToZoneInsSla(List<DwsCloudServerLevelDO> all) {
        Map<String, ZoneInsSlaDto> map = new HashMap<>();
        Map<String, List<DwsCloudServerLevelDO>> mapList = ListUtils.toMapList(all, DwsCloudServerLevelDO::getVersion, o -> o);
        for (Map.Entry<String, List<DwsCloudServerLevelDO>> entry : mapList.entrySet()) {
            String version = entry.getKey();
            List<DwsCloudServerLevelDO> levels = entry.getValue();
            ZoneInsSlaDto dto = new ZoneInsSlaDto();
            dto.setApiTotal(NumberUtils.sum(levels, DwsCloudServerLevelDO::getApiTotal));
            dto.setApiSuccTotal(NumberUtils.sum(levels, DwsCloudServerLevelDO::getApiSucTotal));
            dto.setSumSold(NumberUtils.sum(levels, DwsCloudServerLevelDO::getSoldTotal));
            dto.setSumSoldOut(NumberUtils.sum(levels, DwsCloudServerLevelDO::getSoldOutTotal));
            map.put(version, dto);
        }
        return map;
    }

    private Set<String> getZoneInstanceSet(TrendGraphReq req) {
        Set<String> set = new HashSet<>();
        List<TrendGraphReq.Item> ignore = req.getIgnore();
        if (ListUtils.isNotEmpty(ignore)) {
            ignore.forEach(o->{
                set.add(String.join("@", o.getZoneName(), o.getInstanceType()));
            });
        }
        return set;
    }

    private Map<String, List<BigDecimal>> getSoldMap(TrendGraphReq req, List<String> verList, Set<String> zoneInstance) {
        WhereSQL w = new WhereSQL();
        w.and("zone_name in (?)", req.getZoneName());
        if (req.getSoldType().equals("全时段")) {
            w.and("instance_family in (?)", req.getInstanceType());
            w.and("version in (?)", verList);
            List<DwsCloudServerLevelDO> all = ckcldDBHelper.getAll(DwsCloudServerLevelDO.class, w.getSQL(), w.getParams());
            if (!zoneInstance.isEmpty()) {
                all = all.stream().filter(o->{
                    String str = String.join("@", o.getZoneName(), o.getInstanceFamily());
                    return !(zoneInstance.contains(str));
                }).collect(Collectors.toList());
            }
            Map<String, List<DwsCloudServerLevelDO>> mapList = ListUtils.toMapList(all, DwsCloudServerLevelDO::getVersion, o -> o);
            Map<String, List<BigDecimal>> soldMap = new HashMap<>();
            for (Map.Entry<String, List<DwsCloudServerLevelDO>> entry : mapList.entrySet()) {
                String version = entry.getKey();
                List<DwsCloudServerLevelDO> levels = entry.getValue();
                Map<String, List<DwsCloudServerLevelDO>> tempList = ListUtils.toMapList(levels,
                        o -> String.join("@", o.getZoneName(), o.getInstanceFamily()),
                        o -> o);
                BigDecimal soldTotal = BigDecimal.ZERO;
                BigDecimal soldOutTotal = BigDecimal.ZERO;
                for (List<DwsCloudServerLevelDO> dto : tempList.values()) {
                    if (ListUtils.isNotEmpty(dto)) {
                        soldTotal = soldTotal.add(dto.get(0).getSoldTotal());
                        soldOutTotal = soldOutTotal.add(dto.get(0).getSoldOutTotal());
                    }
                }
                soldMap.put(version, ListUtils.newList(soldTotal, soldOutTotal));
            }

            return soldMap;
        }
        w.and("instance_type in (?)", req.getInstanceType());
        w.and("imp_date in (?)", verList);
        List<DwsLeisureAndBusySoldOutDataDfDO> all = ckcldDBHelper.getAll(DwsLeisureAndBusySoldOutDataDfDO.class,
                w.getSQL(), w.getParams());
        if (!zoneInstance.isEmpty()) {
            all = all.stream().filter(o->{
                String str = String.join("@", o.getZoneName(), o.getInstanceType());
                return !(zoneInstance.contains(str));
            }).collect(Collectors.toList());
        }
        Map<String, List<DwsLeisureAndBusySoldOutDataDfDO>> mapList = ListUtils.toMapList(all,
                DwsLeisureAndBusySoldOutDataDfDO::getImpDate, o -> o);
        Map<String, List<BigDecimal>> soldMap = new HashMap<>();
        for (Entry<String, List<DwsLeisureAndBusySoldOutDataDfDO>> entry : mapList.entrySet()) {
            List<DwsLeisureAndBusySoldOutDataDfDO> value = entry.getValue();
            if (req.getSoldType().equals("闲时")) {
                BigDecimal soldTotal = NumberUtils.sum(value, o -> o.getLeisureSoldTotal());
                BigDecimal soldOutTotal = NumberUtils.sum(value, o -> o.getLeisureSoldOutTotal());
                soldMap.put(entry.getKey(), ListUtils.newList(soldTotal, soldOutTotal));
            }else {
                BigDecimal soldTotal = NumberUtils.sum(value, o -> o.getBusySoldTotal());
                BigDecimal soldOutTotal = NumberUtils.sum(value, o -> o.getBusySoldOutTotal());
                soldMap.put(entry.getKey(), ListUtils.newList(soldTotal, soldOutTotal));
            }
        }
        return soldMap;
    }

    /**
     * 库存 & 销售规模
     *
     * @param req
     * @return
     */
    private TrendGraphResp queryInventoryTrendGraph(TrendGraphReq req) {
        List<LocalDate> scaleDays = getDaysFromParamsSlice(req);
        List<LocalDate> inventoryDays = getDaysFromParams(req);
        List<PointItem> actualInventory = new ArrayList<>();
        List<PointItem> safeInventory = new ArrayList<>();
        List<PointItem> saleScale = new ArrayList<>();
        List<PointItem> reservedInventory = new ArrayList<>();
        List<PointItem> actualInventoryRemoveReserved = new ArrayList<>();

        String sumSql = "sum(if(biz_range_type='外部业务',cur_bill_core,cur_service_core)) core";
        // 判断计费规模、服务规模、全部
        if (req.getScaleType() != null && "计费规模".equals(req.getScaleType())) {
            sumSql = "sum(if(biz_range_type='外部业务', cur_bill_core, toDecimal32(0, 6))) core";
        } else if (req.getScaleType() != null && "服务规模".equals(req.getScaleType())) {
            sumSql = "sum(if(biz_range_type='外部业务', toDecimal32(0, 6), cur_service_core)) core";
        }
        CustomerCustomGroupEnum customerCustomGroupEnum = CustomerCustomGroupEnum.getByCode(req.getCustomerCustomGroup());
        List<String> customerTabType = CustomerCustomGroupEnum.getCustomerTabType(customerCustomGroupEnum);
        //规模数据
        String scaleSql =
                "select stat_time as dataDate, " + sumSql + " from  "
                        + "std_crp.dwd_txy_scale_df  \n"
                        + "where  product='CVM' and stat_time in (?)\n"
                        + "and zone_name in (?)\n"
                        + "and instance_type in (?)\n"
                        + "and customer_tab_type in (?)\n"
                        + "group by stat_time";
        Map<String, BigDecimal> scaleMap = ckcldStdCrpDBHelper.getRaw(GraphDateItem.class, scaleSql, scaleDays,
                        req.getZoneName(),
                        req.getInstanceType(), customerTabType)
                .stream().collect(Collectors.toMap(GraphDateItem::getDate, GraphDateItem::getCore));
        // 取实际库存、安全库存、以及预扣
        InventoryMaps inventoryMaps = getInventoryMaps(req, inventoryDays);

        // 对于周、月，都是求均值
        // 如果是日维度，返回每天的，如果是周维度，返回的是周均
        if ("day".equals(req.getDateRange())) {
            for (LocalDate date : inventoryDays) {
                BigDecimal actual = inventoryMaps.actualInvMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                BigDecimal safe = inventoryMaps.safeInvMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                BigDecimal reserved = inventoryMaps.reservedCoreMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                String disPlay = DateUtils.format(date, "yyyyMMdd");
                actualInventory.add(new PointItem(disPlay, actual));
                safeInventory.add(new PointItem(disPlay, safe));
                saleScale.add(new PointItem(disPlay, scaleMap.getOrDefault(date.toString(), BigDecimal.ZERO)));
                reservedInventory.add(new PointItem(disPlay, reserved));
                actualInventoryRemoveReserved.add(new PointItem(disPlay, actual.subtract(reserved)));
            }
        } else if ("week".equals(req.getDateRange())) {
            List<ResPlanHolidayWeekDO> dtos = getHolidayWeekInfo(req.getBeginDate(), req.getEndDate());

            for (ResPlanHolidayWeekDO item : dtos) {
                // 一周是一条数据
                List<BigDecimal> totalActual = ListUtils.newList();
                List<BigDecimal> totalSafe = ListUtils.newList();
                List<BigDecimal> totalReserved = ListUtils.newList();

                // 遍历一周的开始日期到结束日期，累加每天的量
                LocalDate start = DateUtils.parseLocalDate(item.getStart());
                LocalDate end = DateUtils.parseLocalDate(item.getEnd());

                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (end.isAfter(yesterday)) {
                    end = yesterday;
                }

                if (start.isAfter(end)) {
                    continue;
                }

                String display = DateUtils.format(end, "yyyyMMdd");

                while (!start.isAfter(end)) {
                    totalActual.add(inventoryMaps.actualInvMap.getOrDefault(start.toString(), BigDecimal.ZERO));
                    totalSafe.add(inventoryMaps.safeInvMap.getOrDefault(start.toString(), BigDecimal.ZERO));
                    totalReserved.add(inventoryMaps.reservedCoreMap.getOrDefault(start.toString(), BigDecimal.ZERO));
                    start = start.plusDays(1);
                }
                // 求周均
                BigDecimal actualAvg = NumberUtils.avg(totalActual, 2);
                BigDecimal safeAvg = NumberUtils.avg(totalSafe, 2);
                BigDecimal reservedAvg = NumberUtils.avg(totalReserved, 2);

                actualInventory.add(new PointItem(display, actualAvg));
                safeInventory.add(new PointItem(display, safeAvg));
                saleScale.add(new PointItem(display, scaleMap.getOrDefault(end.toString(), BigDecimal.ZERO)));
                reservedInventory.add(new PointItem(display, reservedAvg));
                actualInventoryRemoveReserved.add(new PointItem(display, actualAvg.subtract(reservedAvg)));
            }
        } else {
            // 月份 -> [[某天的实际库存， 某天的安全库存，某天的预扣], [某天的实际库存， 某天的安全库存，某天的预扣], ...]
            Map<String, List<List<BigDecimal>>> actualSafeMap = new HashMap<>();

            for (LocalDate day : inventoryDays) {
                String currentMonth = DateUtils.format(day, "yyyyMM");

                actualSafeMap.putIfAbsent(currentMonth, ListUtils.newList());
                actualSafeMap.get(currentMonth).add(ListUtils.newList(
                        inventoryMaps.actualInvMap.getOrDefault(day.toString(), BigDecimal.ZERO),
                        inventoryMaps.safeInvMap.getOrDefault(day.toString(), BigDecimal.ZERO),
                        inventoryMaps.reservedCoreMap.getOrDefault(day.toString(), BigDecimal.ZERO)
                ));
            }

            actualSafeMap.entrySet().stream().forEach(e -> {
                List<List<BigDecimal>> actualSafe = e.getValue();
                List<BigDecimal> monthActual = actualSafe.stream().map(s -> s.get(0)).collect(Collectors.toList());
                List<BigDecimal> monthSafe = actualSafe.stream().map(s -> s.get(1)).collect(Collectors.toList());
                List<BigDecimal> monthReserved = actualSafe.stream().map(s -> s.get(2)).collect(Collectors.toList());

                BigDecimal actualAvg = NumberUtils.avg(monthActual, 2);
                BigDecimal safeAvg = NumberUtils.avg(monthSafe, 2);
                BigDecimal reservedAvg = NumberUtils.avg(monthReserved, 2);
                String display = e.getKey();
                actualInventory.add(new PointItem(display, actualAvg));
                safeInventory.add(new PointItem(display, safeAvg));
                Date monthDate = DateUtils.parse(display, "yyyyMM");
                LocalDate lastDayOfMonth = DateUtils.getLastDayOfMonth(monthDate);

                if (lastDayOfMonth.isAfter(req.getEndDate())) {
                    lastDayOfMonth = req.getEndDate();
                }
                // 对于当月，最迟取昨天
                if (lastDayOfMonth.isAfter(DateUtils.yesterday())) {
                    lastDayOfMonth = DateUtils.yesterday();
                }

                saleScale.add(new PointItem(display, scaleMap.getOrDefault(lastDayOfMonth.toString(), BigDecimal.ZERO)));
                reservedInventory.add(new PointItem(display, reservedAvg));
                actualInventoryRemoveReserved.add(new PointItem(display, actualAvg.subtract(reservedAvg)));
            });
        }

        TrendGraphResp resp = new TrendGraphResp();
        resp.setType(req.getCardType());

//        if (req.getIsIgnoreReserved() != null && req.getIsIgnoreReserved()) {
        if (ListUtils.isEmpty(req.getInvDetailType()) ||
                (ListUtils.isNotEmpty(req.getInvDetailType()) && req.getInvDetailType().contains("用户预扣"))) {
            resp.setData(ImmutableMap.of(
                    "实际库存", actualInventory,
                    "安全库存", safeInventory,
                    "规模变化量", saleScale,
                    "客户预扣", reservedInventory,
                    "实际库存-剔除预扣", actualInventoryRemoveReserved)
            );
        } else {
            resp.setData(ImmutableMap.of(
                    "实际库存", actualInventory,
                    "安全库存", safeInventory,
                    "规模变化量", saleScale));
        }

        return resp;
    }

    /**
     * 冗余系数
     *
     * @param req
     * @return
     */

    private TrendGraphResp queryRedundancyTrendGraph(TrendGraphReq req) {
        List<LocalDate> days = getDaysFromParams(req);

        List<PointItem> healthNice = new ArrayList<>();
        List<PointItem> healthUp = new ArrayList<>();
        List<PointItem> healthDown = new ArrayList<>();

        InventoryMaps maps = getInventoryMaps(req, days);
        Map<String, BigDecimal> turnoverInvMap = getTurnOverInvOnly(req, days);

        Function<LocalDate, List<BigDecimal>> getRedundancy = day -> {
            BigDecimal actual = maps.actualInvMap.getOrDefault(day.toString(), BigDecimal.ZERO);
            BigDecimal safe = maps.safeInvMap.getOrDefault(day.toString(), BigDecimal.ZERO);
            BigDecimal turnover = turnoverInvMap.getOrDefault(day.toString(), BigDecimal.ZERO);

            BigDecimal rt = safe.intValue() > 0 ? NumberUtils.divide(actual, safe.add(turnover), 2) : BigDecimal.ZERO;

            if (req.getIsIgnoreReserved() != null && req.getIsIgnoreReserved()) {
                BigDecimal reserved = maps.reservedCoreMap.getOrDefault(day.toString(), BigDecimal.ZERO);
                rt = safe.intValue() > 0 ? NumberUtils.divide(actual.subtract(reserved), safe.add(turnover), 2) : BigDecimal.ZERO;
            }

            if (rt.floatValue() < 1.0) {
                return ListUtils.newList(rt, null ,null);
            } else if (rt.floatValue() > 2.0 && actual.intValue() > 1000) {
                return ListUtils.newList(null, null, rt);
            } else {
                return ListUtils.newList(null, rt, null);
            }
        };

        // 如果是日维度，返回每天的，如果是周维度，返回的是周均
        if ("day".equals(req.getDateRange())) {
            for (LocalDate date : days) {
                List<BigDecimal> values = getRedundancy.apply(date);
                healthDown.add(new PointItem(DateUtils.format(date, "yyyyMMdd"), values.get(0)));
                healthNice.add(new PointItem(DateUtils.format(date, "yyyyMMdd"), values.get(1)));
                healthUp.add(new PointItem(DateUtils.format(date, "yyyyMMdd"), values.get(2)));
            }
        } else if ("week".equals(req.getDateRange())) {
            List<ResPlanHolidayWeekDO> dtos = getHolidayWeekInfo(req.getBeginDate(), req.getEndDate());

            for (ResPlanHolidayWeekDO item : dtos) {
                // 一周是一条数据
                List<BigDecimal> totalActual = ListUtils.newList();
                List<BigDecimal> totalSafe = ListUtils.newList();

                // 遍历一周的开始日期到结束日期，累加每天的量
                LocalDate start = DateUtils.parseLocalDate(item.getStart());
                LocalDate end = DateUtils.parseLocalDate(item.getEnd());

                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (end.isAfter(yesterday)) {
                    end = yesterday;
                }

                if (start.isAfter(end)) {
                    continue;
                }

                String display = DateUtils.format(end, "yyyyMMdd");

                while (!start.isAfter(end)) {
                    totalActual.add(maps.actualInvMap.getOrDefault(start.toString(), BigDecimal.ZERO));
                    totalSafe.add(maps.safeInvMap.getOrDefault(start.toString(), BigDecimal.ZERO));

                    start = start.plusDays(1);
                }
                // 求周均
                BigDecimal actualAvg = NumberUtils.avg(totalActual, 2);
                BigDecimal safeAvg = NumberUtils.avg(totalSafe, 2);

                BigDecimal rt = safeAvg.intValue() > 0 ? NumberUtils.divide(actualAvg, safeAvg, 2) : BigDecimal.ZERO;
                // 求周均
                if (rt.floatValue() < 1.0) {
                    healthDown.add(new PointItem(display, rt));
                    healthNice.add(new PointItem(display, null));
                    healthUp.add(new PointItem(display, null));
                } else if (rt.floatValue() > 2.0 && actualAvg.intValue() > 1000) {
                    healthDown.add(new PointItem(display, null));
                    healthNice.add(new PointItem(display, null));
                    healthUp.add(new PointItem(display, rt));
                } else {
                    healthDown.add(new PointItem(display, null));
                    healthNice.add(new PointItem(display, rt));
                    healthUp.add(new PointItem(display, null));
                }
            }
        } else {
            // 月份 -> [[某天的实际库存， 某天的安全库存], [某天的实际库存， 某天的安全库存], ...]
            Map<String, List<List<BigDecimal>>> actualSafeMap = new HashMap<>();

            for (LocalDate day : days) {
                String currentMonth = DateUtils.format(day, "yyyyMM");

                actualSafeMap.putIfAbsent(currentMonth, ListUtils.newList());
                actualSafeMap.get(currentMonth).add(ListUtils.newList(
                        maps.actualInvMap.getOrDefault(day.toString(), BigDecimal.ZERO),
                        maps.safeInvMap.getOrDefault(day.toString(), BigDecimal.ZERO)
                ));
            }

            actualSafeMap.entrySet().stream().forEach(e -> {
                List<List<BigDecimal>> actualSafe = e.getValue();
                List<BigDecimal> monthActual = actualSafe.stream().map(s -> s.get(0)).collect(Collectors.toList());
                List<BigDecimal> monthSafe = actualSafe.stream().map(s -> s.get(1)).collect(Collectors.toList());

                BigDecimal actualAvg = NumberUtils.avg(monthActual, 2);
                BigDecimal safeAvg = NumberUtils.avg(monthSafe, 2);
                String display = e.getKey();
                BigDecimal rt = safeAvg.intValue() > 0 ? NumberUtils.divide(actualAvg, safeAvg, 2) : BigDecimal.ZERO;
                // 求月均
                if (rt.floatValue() < 1.0) {
                    healthDown.add(new PointItem(display, rt));
                    healthNice.add(new PointItem(display, null));
                    healthUp.add(new PointItem(display, null));
                } else if (rt.floatValue() > 2.0 && actualAvg.intValue() > 1000) {
                    healthDown.add(new PointItem(display, null));
                    healthNice.add(new PointItem(display, null));
                    healthUp.add(new PointItem(display, rt));
                } else {
                    healthDown.add(new PointItem(display, null));
                    healthNice.add(new PointItem(display, rt));
                    healthUp.add(new PointItem(display, null));
                }
            });
        }

        TrendGraphResp resp = new TrendGraphResp();
        resp.setType(req.getCardType());
        resp.setData(ImmutableMap.of("库存击穿", healthDown, "库存健康", healthNice, "库存冗余", healthUp));
        return resp;
    }

    public List<String> getCampusByZoneName(List<String> zoneName) {
        Map<String, StaticZoneDO> campus2ZoneMap = SpringUtil.getBean(DictServiceImpl.class).getCampus2ZoneInfoMap();
        List<String> campus = new ArrayList<>();
        for (Entry<String, StaticZoneDO> entry : campus2ZoneMap.entrySet()) {
            StaticZoneDO value = entry.getValue();
            if (zoneName.contains(value.getZoneName())) {
                campus.add(entry.getKey());
            }
        }
        log.info("供应情况zone:{} -> campus {}", zoneName, campus);
        return campus;
    }

    public List<String> getDeviceTypeByInstanceType(List<String> instanceType) {
        DictService dictService = SpringUtil.getBean(DictServiceImpl.class);

        List<String> device = new ArrayList<>();

        for (String ins : instanceType) {
            List<String> deviceType = dictService.getCsigDeviceTypeByInstanceType(ins);

            if (deviceType != null) {
                device.addAll(deviceType);
            }
        }

        return device;
    }

    /**
     * 供应情况
     *
     * @param req
     * @return
     */
    private TrendGraphResp querySupplyTrendGraph(TrendGraphReq req) {
        TrendGraphResp resp = new TrendGraphResp();
        resp.setType(req.getCardType());
        List<PointItem> purNormalList = new ArrayList<>();
        List<PointItem> purOtherList = new ArrayList<>();
        List<PointItem> moveList = new ArrayList<>();
        List<PointItem> totalList = new ArrayList<>();

//        String campusSql = "select distinct campus_name  from  yunti_stategy_zone   where zone_name in (?)";
//        List<String> campus = yuntiDBHelper.getRaw(String.class, campusSql, req.getZoneName());

        List<String> campus = getCampusByZoneName(req.getZoneName());
        List<String> device = getDeviceTypeByInstanceType(req.getInstanceType());

        List<ResPlanHolidayWeekDO> dtos = null;
        List<LocalDate> days = new ArrayList<>();
        String dateCond;
        // 构造时间范围查询的 mysql 语句
        // 业务要求，这里周维度的量要取是一周的总量，而不是最后一天的量
        if ("day".equals(req.getDateRange())) {
            LocalDate beginDate = req.getBeginDate();
            LocalDate endDate = req.getEndDate();
            if (req.getSupplyPurchaseType().equals("预计到货时间")) {
                //多展示14天的数据
                endDate = endDate.plusDays(14);
            }

            LocalDate tempDate = beginDate;

            while (!tempDate.isAfter(endDate)) {
                days.add(tempDate);
                tempDate = tempDate.plusDays(1);
            }
            dateCond = "and dataDate in ('" + days.stream().map(d -> d.toString()).collect(Collectors.joining("','")) + "')\n";
        } else if ("week".equals(req.getDateRange())) {
            if (req.getSupplyPurchaseType().equals("预计到货时间")) {
                LocalDate endDate = req.getEndDate();
                req.setEndDate(endDate.plusDays(14));
            }
            dtos = getHolidayWeekInfo(req.getBeginDate(), req.getEndDate());

            if (dtos.size() > 0) {
                String startDate = dtos.get(0).getStart();
                String endDate = dtos.get(dtos.size() - 1).getEnd();

                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (LocalDate.parse(endDate).isAfter(yesterday) && !req.getSupplyPurchaseType().equals("预计到货时间")) {
                    endDate = yesterday.toString();
                }

                dateCond = "and dataDate between '" + startDate + "' and '" + endDate + "'\n";
            } else {
                throw BizException.makeThrow("所选时间范围内查询不到节假周信息，无法查询到周维度的数据。如有问题，请联系 brightwwu");
            }
        } else {
            val beginDate = LocalDate.of(req.getBeginDate().getYear(), req.getBeginDate().getMonth(), 1);
            LocalDate yesterday = LocalDate.now().minusDays(1);
            if (req.getSupplyPurchaseType().equals("预计到货时间")) {
                //向后顺延一个月
                LocalDate endDate = req.getEndDate();
                req.setEndDate(endDate.plusMonths(1));
            }
            LocalDate nextMonth = req.getEndDate().plusMonths(1);
            LocalDate endDate = LocalDate.of(nextMonth.getYear(), nextMonth.getMonth(), 1).minusDays(1);

            if (endDate.isAfter(yesterday) && !req.getSupplyPurchaseType().equals("预计到货时间")) {
                endDate = yesterday;
            }

            LocalDate tempDate = beginDate;

            while (!tempDate.isAfter(endDate)) {
                days.add(tempDate);
                tempDate = tempDate.plusDays(1);
            }
            String startDate1 = days.get(0).toString();
            String endDate1 = days.get(days.size() - 1).toString();
            dateCond = "and dataDate between '" + startDate1 + "' and '" + endDate1 + "'\n";
        }

        //从实例类型找到设备类型
        Map<String, BigDecimal> purNormalMap = new HashMap<>();
        Map<String, BigDecimal> purOtherMap = new HashMap<>();
        //从实例类型找到设备类型
        Map<String, BigDecimal> movMap = new HashMap<>();
        if (!device.isEmpty() && !campus.isEmpty()) {
            // 实例能转换成实际机型才会查询采购和搬迁数据

            //采购数据
            String dateColum = req.supplyPurchaseTypeToTimeField();
            // 客户名称是 "常规水位，安全库存，中长尾客户" 为"水位采购"，所有其他客户名为 "客户采购"
            String sql = "select toDate(" + dateColum + ") as dataDate , SUM(cpu_logic_core) as  core \n"
                    + "from cubes.demandMarket \n"
                    + "where DAY=? \n"
                    + "and campus in (?) \n"
                    + "and quota_plan_product_name = '腾讯云CVM' and cloud_business_type='云业务' \n"
                    + "and quota_device_class in (?)\n"
                    + dateCond
                    + "and xy_customer_name in ('常规水位', '安全库存', '中长尾客户', '春保水位') \n"
                    + "group by dataDate";
            purNormalMap = ckcubesDBHelper.getRaw(GraphDateItem.class, sql,
                            req.getDate().toString(),
                            campus,
                            device)
                    .stream().collect(Collectors.toMap(item -> {
                        String date = item.getDate();
                        // 转换成 YYYY-MM-DD 格式
                        return DateUtils.format(DateUtils.parse(date), "yyyy-MM-dd");
                    }, GraphDateItem::getCore));
            log.info("水位采购数据 {}", purNormalMap);

            String customerSql = "select toDate(" + dateColum + ") as dataDate , SUM(cpu_logic_core) as  core \n"
                    + "from cubes.demandMarket \n"
                    + "where DAY=? \n"
                    + "and campus in (?) \n"
                    + "and quota_plan_product_name = '腾讯云CVM' and cloud_business_type='云业务' \n"
                    + "and quota_device_class in (?)\n"
                    + dateCond
                    + "and xy_customer_name not in ('常规水位', '安全库存', '中长尾客户', '春保水位') \n"
                    + "group by dataDate";
            purOtherMap = ckcubesDBHelper.getRaw(GraphDateItem.class, customerSql,
                            req.getDate().toString(),
                            campus,
                            device)
                    .stream().collect(Collectors.toMap(item -> {
                        String date = item.getDate();
                        // 转换成 YYYY-MM-DD 格式
                        return DateUtils.format(DateUtils.parse(date), "yyyy-MM-dd");
                    }, GraphDateItem::getCore));
            log.info("客户采购数据 {}", purOtherMap);
            //搬迁数据

            String mvDateColum = req.supplyMoveTypeToTimeField();

            String mvSql = "select dataDate,sum(core) core from (\n"
                    + "\tselect date(" + mvDateColum
                    + ") as dataDate,sum(TotalCore) core from  cubes.smove_slice_market \n"
                    + "\twhere `DAY` = ? \n"
                    + "\tand  BelongBnName='[N][腾讯云CVM_宿主机]'\n"
                    + "\tand Bussiness2Name not like '[自研上云]%'\n"
                    + "\tand SCampusName not in (?) \n"
                    + "\tand DCampusName  in (?)\n"
                    + "\tand SSvrDeviceClass  in (?)\n"
                    + dateCond
                    + "\tgroup by dataDate\n"
                    + "\tunion all \n"
                    + "\tselect date(" + mvDateColum
                    + ") as dataDate,-sum(TotalCore)  core from  cubes.smove_slice_market \n"
                    + "\twhere `DAY` = ? \n"
                    + "\tand  BelongBnName='[N][腾讯云CVM_宿主机]'\n"
                    + "\tand Bussiness2Name not like '[自研上云]%'\n"
                    + "\tand SCampusName in (?) \n"
                    + "\tand DCampusName not in (?)\n"
                    + "\tand SSvrDeviceClass  in (?)\n"
                    + dateCond
                    + "\tgroup by dataDate\n"
                    + ")aa group by dataDate";
            String version = req.getDate().toString();

            movMap = ckcubesDBHelper.getRaw(GraphDateItem.class, mvSql, version, campus, campus, device, version,
                            campus,
                            campus, device)
                    .stream()
                    .collect(Collectors.toMap(GraphDateItem::getDate, GraphDateItem::getCore));
            log.info("搬迁数据 {}", movMap);
        }

        // 如果是周数据
        if ("week".equals(req.getDateRange())) {
            for (ResPlanHolidayWeekDO item : dtos) {
                // 遍历一周的开始日期到结束日期，累加每天的量
                LocalDate start = DateUtils.parseLocalDate(item.getStart());
                LocalDate end = DateUtils.parseLocalDate(item.getEnd());

                LocalDate yesterday = LocalDate.now().minusDays(1);

                if (end.isAfter(yesterday) && !req.getSupplyPurchaseType().equals("预计到货时间")) {
                    end = yesterday;
                }

                if (start.isAfter(end)) {
                    continue;
                }

                String display = DateUtils.format(end, "yyyyMMdd");
                // 一周是一条数据
                PointItem purNormalItem = new PointItem(display, BigDecimal.ZERO);
                PointItem purOtherItem = new PointItem(display, BigDecimal.ZERO);
                PointItem moveItem = new PointItem(display, BigDecimal.ZERO);
                PointItem totalItem = new PointItem(display, BigDecimal.ZERO);

                while (!start.isAfter(end)) {
                    BigDecimal purNormalCore = purNormalMap.getOrDefault(start.toString(), BigDecimal.ZERO);
                    purNormalItem.setVale(purNormalItem.getVale().add(purNormalCore));

                    BigDecimal moveCore = movMap.getOrDefault(start.toString(), BigDecimal.ZERO);
                    moveItem.setVale(moveItem.getVale().add((moveCore)));

                    BigDecimal purOtherCore = purOtherMap.getOrDefault(start.toString(), BigDecimal.ZERO);
                    purOtherItem.setVale(purOtherItem.getVale().add(purOtherCore));

                    BigDecimal totalCore = purNormalCore.add(purOtherCore).add(moveCore);
                    totalItem.setVale(totalItem.getVale().add(totalCore));

                    start = start.plusDays(1);
                }

                purNormalList.add(purNormalItem);
                purOtherList.add(purOtherItem);
                moveList.add(moveItem);
                totalList.add(totalItem);
            }
        } else {
            if ("day".equals(req.getDateRange())) {
                for (LocalDate date : days) {
                    String display = DateUtils.format(date, "yyyyMMdd");

                    //获取采购数量
                    //获取搬迁数量 搬迁可能是负数
                    //获取总量  采购+搬迁

                    BigDecimal purNormalCore = purNormalMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                    BigDecimal moveCore = movMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                    BigDecimal purOtherCore = purOtherMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                    purNormalList.add(new PointItem(display, purNormalCore));
                    purOtherList.add(new PointItem(display, purOtherCore));
                    moveList.add(new PointItem(display, moveCore));
                    totalList.add(new PointItem(display, purNormalCore.add(purOtherCore).add(moveCore)));
                }
            } else {
                // 月维度，按月求和
                Map<String, List<BigDecimal>> monthPurNormalMap = new HashMap<>();
                Map<String, List<BigDecimal>> monthPurOtherMap = new HashMap<>();
                Map<String, List<BigDecimal>> monthMoveMap = new HashMap<>();
                Map<String, List<BigDecimal>> monthTotalMap = new HashMap<>();

                for (LocalDate date : days) {
                    String display = DateUtils.format(date, "yyyyMM");

                    BigDecimal purNormalCore = purNormalMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                    BigDecimal moveCore = movMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                    BigDecimal purOtherCore = purOtherMap.getOrDefault(date.toString(), BigDecimal.ZERO);
                    BigDecimal totalCore = purNormalCore.add(purOtherCore).add(moveCore);

                    monthPurNormalMap.putIfAbsent(display, ListUtils.newList());
                    monthPurOtherMap.putIfAbsent(display, ListUtils.newList());
                    monthMoveMap.putIfAbsent(display, ListUtils.newList());
                    monthTotalMap.putIfAbsent(display, ListUtils.newList());

                    monthPurNormalMap.get(display).add(purNormalCore);
                    monthPurOtherMap.get(display).add(purOtherCore);
                    monthMoveMap.get(display).add(moveCore);
                    monthTotalMap.get(display).add(totalCore);
                }

                monthPurNormalMap.entrySet().forEach(o -> {
                    String display = o.getKey();
                    BigDecimal purNormalCore = NumberUtils.sum(o.getValue());
                    purNormalList.add(new PointItem(display, purNormalCore));
                });
                monthPurOtherMap.entrySet().forEach(o -> {
                    String display = o.getKey();
                    BigDecimal purOtherCore = NumberUtils.sum(o.getValue());
                    purOtherList.add(new PointItem(display, purOtherCore));
                });
                monthMoveMap.entrySet().forEach(o -> {
                    String display = o.getKey();
                    BigDecimal moveCore = NumberUtils.sum(o.getValue());
                    moveList.add(new PointItem(display, moveCore));
                });
                monthTotalMap.entrySet().forEach(o -> {
                    String display = o.getKey();
                    BigDecimal totalCore = NumberUtils.sum(o.getValue());
                    totalList.add(new PointItem(display, totalCore));
                });
            }
        }

        resp.setType(req.getCardType());
        resp.setData(ImmutableMap.of(
                "水位采购", purNormalList,
                "客户采购", purOtherList,
                "搬迁", moveList,
                "总计", totalList
        ));
        return resp;
    }

    InventoryHealthActualReq createActualReq(TrendGraphReq req, LocalDate date) {
        InventoryHealthActualReq req2 = new InventoryHealthActualReq();
        req2.setDate(date.toString());
        req2.setZoneName(req.getZoneName());
        req2.setMaterialType(req.getMaterialType());
        req2.setLineType(req.getLineType());
        req2.setInstanceType(req.getInstanceType());
        req2.setCustomerCustomGroup(req.getCustomerCustomGroup());
        req2.setTimeDimension("日切片");
        req2.setIsIgnoreReserved(req.getIsIgnoreReserved());
        req2.setCustomerCustomGroup(req.getCustomerCustomGroup());

        return req2;
    }

    List<InventoryHealthActualResp.Item> queryActualInventory(TrendGraphReq req, LocalDate date) {
        InventoryHealthActualReq req2 = createActualReq(req, date);
        InventoryHealthActualV2ServiceImpl inventoryHealthActualV2Service = SpringUtil.getBean(
                InventoryHealthActualV2ServiceImpl.class);
        return inventoryHealthActualV2Service.queryInventoryHealthActualV2(req2).getData();
    }

    Map<String, BigDecimal> getActualInvOnly(TrendGraphReq req, List<LocalDate> days, Boolean isOnlyReserved) {
        WhereSQL condition = new WhereSQL();
        condition.and("stat_time in (?)", days.stream().map(d -> d.toString()).collect(Collectors.toList()));
        condition.and("product_type = 'CVM'");
        condition.and("instance_type in (?)", req.getInstanceType());
        condition.and("zone_name in (?)", req.getZoneName());

        if (ListUtils.isNotEmpty(req.getLineType())) {
            condition.and("line_type in (?)", req.getLineType());
        }

        if (ListUtils.isNotEmpty(req.getMaterialType())) {
            condition.and("material_type in (?)", req.getMaterialType());
        }
        // 只要预扣数据
        if (isOnlyReserved) {
            condition.and("inv_detail_type = '用户预扣'");
        } else {
            if (ListUtils.isNotEmpty(req.getInvDetailType())) {
                req.getInvDetailType().add("(空值)");
                condition.and("inv_detail_type in (?)", req.getInvDetailType());
            }
        }

        condition.addGroupBy("stat_time");
        condition.addOrderBy("stat_time");

        return ckcldDBHelper.getAll(ActualInventoryItem.class, condition.getSQL(), condition.getParams()).stream().collect(Collectors.toMap(
                o -> o.date,
                o -> o.cores
        ));

    }

    Map<String, BigDecimal> getSafeInvOnly(TrendGraphReq req, List<LocalDate> days, String algorithm) {
        WhereSQL condition = new WhereSQL();
        condition.and("product_type = 'CVM'");
        condition.and("algorithm = ?", algorithm);

        String customerCustomGroup = req.getCustomerCustomGroup();
        CustomerCustomGroupEnum groupEnum = CustomerCustomGroupEnum.getByCode(customerCustomGroup);
        if (groupEnum == null) {
            groupEnum = CustomerCustomGroupEnum.ALL;
        }
        Set<String> mondays = new HashSet<>();
        for (LocalDate day : days) {
            String curWeekMonday = DateUtils.formatDate(day.with(DayOfWeek.MONDAY));
            mondays.add(curWeekMonday);
        }
        List<String> dates = new ArrayList<>(mondays);
        condition.and("customer_custom_group = ?", groupEnum.getName());
        condition.and("stat_time in (?)", days.stream().map(d -> d.toString()).collect(Collectors.toList()));
        condition.and("instance_type in (?)", req.getInstanceType());
        condition.and("zone_name in (?)", req.getZoneName());

        condition.addGroupBy("stat_time");
        condition.addOrderBy("stat_time");

        EnableDeliveryDataForAlgorithmDTO enableDeliveryDataForAlgorithm
                = SpringUtil.getBean(OperationViewController2.class).getEnableDeliveryDataForAlgorithm();
        // 包月安全库存
        Map<String, BigDecimal> monthlySafeInvMap = ckcldDBHelper.getAll(SafeInventoryItem.class, condition.getSQL(), condition.getParams()).stream().collect(Collectors.toMap(o -> o.date, o -> {
            if (enableDeliveryDataForAlgorithm.getStatus().equals("生效")) {
                return o.cores;
            } else {
                return o.noDeliveryCores;
            }
        }));
        // 弹性备货配额
        WhereSQL bufferCondition = new WhereSQL();
        bufferCondition.and("product_type = 'CVM'");
        bufferCondition.and("stat_time in (?)",  dates);
        bufferCondition.and("instance_type in (?)", req.getInstanceType());
        bufferCondition.and("zone_name in (?)", req.getZoneName());

        List<DwsBufferSafeInventoryDfDO> buffer = ckcldDBHelper.getAll(DwsBufferSafeInventoryDfDO.class, bufferCondition.getSQL(), bufferCondition.getParams()).stream().map(item -> {
            // 确保弹性备货 > 0
            item.setMckBufferSafetyInv(NumberUtils.max(item.getMckBufferSafetyInv(), BigDecimal.ZERO));
            item.setBufferSafetyInv(NumberUtils.max(item.getBufferSafetyInv(), BigDecimal.ZERO));
            return item;
        }).collect(Collectors.toList());
        Map<String, List<BigDecimal>> mapList = ListUtils.toMapList(buffer, o -> o.getStatTime(), o -> o.getFinalBufferSafetyInv());
        Map<String, BigDecimal> tempBufferSafeInvMap = new HashMap<>();
        for (Map.Entry<String, List<BigDecimal>> entry : mapList.entrySet()) {
            tempBufferSafeInvMap.put(entry.getKey(), NumberUtils.sum(entry.getValue()));
        }
        Map<String, BigDecimal> bufferSafeInvMap = new HashMap<>();
        for (LocalDate day : days) {
            String curWeekMonday = DateUtils.formatDate(day.with(DayOfWeek.MONDAY));
            BigDecimal bigDecimal = tempBufferSafeInvMap.get(curWeekMonday);
            bufferSafeInvMap.put(day.toString(), bigDecimal);
        }

        // 人工调整
        WhereSQL manualCondition = new WhereSQL();
        manualCondition.and("stat_time in (?)", days.stream().map(d -> d.toString()).collect(Collectors.toList()));
        manualCondition.and("instance_type in (?)", req.getInstanceType());
        manualCondition.and("zone_name in (?)", req.getZoneName());
        manualCondition.and("deleted = 0");

        manualCondition.addGroupBy("stat_time");
        manualCondition.addGroupBy("instance_type");
        manualCondition.addGroupBy("zone_name");

        manualCondition.addOrderBy("stat_time");
        // 找出所有人工调整
        List<ManualConfigItem> manualConfigItems = demandDBHelper.getAll(ManualConfigItem.class, manualCondition.getSQL(), manualCondition.getParams());
        Map<String, List<ManualConfigItem>> configList = ListUtils.toMapList(manualConfigItems, o -> o.getDate(), o -> o);
        Map<String, BigDecimal> manualConfigMap = new HashMap<>();
        for (Entry<String, List<ManualConfigItem>> entry : configList.entrySet()) {
            List<ManualConfigItem> value = entry.getValue();
            manualConfigMap.put(entry.getKey(), NumberUtils.sum(value, o -> o.getCores()));
        }

        return days.stream().map(day -> {
            String date = day.toString();
            BigDecimal monthSafeInv = monthlySafeInvMap.getOrDefault(date, BigDecimal.ZERO) == null ?
                    BigDecimal.ZERO : monthlySafeInvMap.getOrDefault(date, BigDecimal.ZERO);
            BigDecimal bufferSafeInv = bufferSafeInvMap.getOrDefault(date, BigDecimal.ZERO) == null ?
                    BigDecimal.ZERO : bufferSafeInvMap.getOrDefault(date, BigDecimal.ZERO);
            BigDecimal manualSafeInv = manualConfigMap.getOrDefault(date, BigDecimal.ZERO) == null ?
                    BigDecimal.ZERO : manualConfigMap.getOrDefault(date, BigDecimal.ZERO);
            BigDecimal safeInv = NumberUtils.max(monthSafeInv.add(bufferSafeInv).add(manualSafeInv), BigDecimal.ZERO);
            return ListUtils.newList(date, safeInv);
        }).collect(Collectors.toMap(o -> (String) o.get(0), o -> (BigDecimal) o.get(1)));
    }

    Map<String, BigDecimal> getReservedCoreOnly(TrendGraphReq req, List<LocalDate> days) {
        // 预扣数据从两方面取，days 在 2024-01-11 之前的，取老数据源。在 2024-01-11 以及之后，取新数据源
        List<LocalDate> beforeDays = days.stream().filter(d -> d.isBefore(LocalDate.of(2024, 1, 11))).collect(Collectors.toList());
        List<LocalDate> afterDays = days.stream().filter(d -> !d.isBefore(LocalDate.of(2024, 1, 11))).collect(Collectors.toList());
        Map<String, BigDecimal> afterReserved = getActualInvOnly(req, afterDays, true);

        if (ListUtils.isNotEmpty(beforeDays)) {
            WhereSQL condition = new WhereSQL();
            condition.and("stat_time in (?)", beforeDays.stream().map(d -> d.toString()).collect(Collectors.toList()));
            condition.and("instance_type in (?)", req.getInstanceType());
            condition.and("zone_name in (?)", req.getZoneName());

            condition.addGroupBy("stat_time");
            condition.addOrderBy("stat_time");

            afterReserved.putAll(ckcldDBHelper.getAll(ReservedCoreItem.class, condition.getSQL(), condition.getParams()).stream().collect(Collectors.toMap(o -> o.date, o -> o.cores)));
        }

        return afterReserved;
    }

    InventoryMaps getInventoryMaps(TrendGraphReq req, List<LocalDate> days) {
        Map<String, BigDecimal> actualInvMap = new HashMap<>();
        Map<String, BigDecimal> safeInvMap = new HashMap<>();
        Map<String, BigDecimal> reservedCoreMap = new HashMap<>();

        // 如果是历史算法，查结果表，如果是未来算法，暂时走老逻辑，未来算法暂未启用，未来也可能调整，先不动
        EffectiveAlgorithmDTO effectiveAlgorithm
                = SpringUtil.getBean(OperationViewController2.class).getEffectiveAlgorithm();

        if (effectiveAlgorithm.getAlgorithm().equals("futureWeekPeak")) {
            List<Future<List<InventoryHealthActualResp.Item>>> futures = new ArrayList<>();

            for (LocalDate date : days) {
                Future<List<InventoryHealthActualResp.Item>> sft = threadPool.submit(() -> queryActualInventory(req, date));
                futures.add(sft);
            }
            // 未来算法走老的逻辑
            try {
                for (int i = 0; i < days.size(); i++) {
                    LocalDate date = days.get(i);
                    List<InventoryHealthActualResp.Item> rs = futures.get(i).get();
                    BigDecimal actual = NumberUtils.sum(rs, InventoryHealthActualResp.Item::getActualInventoryCore);
                    BigDecimal safe = NumberUtils.sum(rs, InventoryHealthActualResp.Item::getSafetyInventoryCore);
                    BigDecimal reserved = NumberUtils.sum(rs, InventoryHealthActualResp.Item::getReservedCores);
                    actualInvMap.put(date.toString(), actual);
                    safeInvMap.put(date.toString(), safe);
                    reservedCoreMap.put(date.toString(), reserved);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            } catch (ExecutionException e) {
                e.printStackTrace();
                throw BizException.makeThrow(e.getMessage());
            }
        } else {
            // 1. 查实际库存
            actualInvMap = getActualInvOnly(req, days, false);
            // 2. 查安全库存
            String algorithm = InventoryHealthAlgorithm.getNameFromCode(effectiveAlgorithm.getAlgorithm());
            safeInvMap = getSafeInvOnly(req, days, algorithm);

            // 3. 查预扣
            reservedCoreMap = getReservedCoreOnly(req, days);

            //4. 查周转库存


        }

        InventoryMaps result = new InventoryMaps();
        result.actualInvMap = actualInvMap;
        result.reservedCoreMap = reservedCoreMap;
        result.safeInvMap = safeInvMap;

        return result;
    }

    public Map<String, BigDecimal> getTurnOverInvOnly(TrendGraphReq req, List<LocalDate> days) {
        Map<String, BigDecimal> turnoverInvMap = new HashMap<>();
        //借助运营视图求出周转库存
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        OperationViewService2Impl viewSerivcie = SpringUtil.getBean(OperationViewService2Impl.class);
        List<Future<Map<String, List<OperationViewService2Impl.DwsInventoryHealthMckTurnoverWfAnyDO>>>> futures = new ArrayList<>();
        Set<Date> tempMondays = new HashSet<>();
        Map<String, List<String>> mondayMap = ListUtils.toMapList(days, o -> o.with(DayOfWeek.MONDAY).toString(), o -> o.toString());
        for (LocalDate day : days) {
            Date curWeekMonday = DateUtils.parse(DateUtils.formatDate(day.with(DayOfWeek.MONDAY)));
            tempMondays.add(curWeekMonday);
        }
        List<Date> mondays = new ArrayList<>(tempMondays);
        for (Date monday : mondays) {
            OperationViewReq2 ovReq = new OperationViewReq2();
            ovReq.setDate(monday);
            ovReq.setZoneName(req.getZoneName());
            ovReq.setInstanceType(req.getInstanceType());
            ovReq.setLineType(req.getLineType());
            ovReq.setMaterialType(req.getMaterialType());
            ovReq.setInvDetailType(req.getInvDetailType());
            ovReq.setCustomerCustomGroup(req.getCustomerCustomGroup());
            Future<Map<String, List<OperationViewService2Impl.DwsInventoryHealthMckTurnoverWfAnyDO>>> submit
                    = threadPool.submit(() -> viewSerivcie.getTurnoverInventoryWeek13AvgV2(ovReq));
            futures.add(submit);
        }
        try {
            for(int i = 0; i < mondays.size(); i++) {
                Map<String, List<OperationViewService2Impl.DwsInventoryHealthMckTurnoverWfAnyDO>> map = futures.get(i).get();
                BigDecimal turnoverInv = BigDecimal.ZERO;
                BigDecimal weekPeek = BigDecimal.ZERO;
                for (List<OperationViewService2Impl.DwsInventoryHealthMckTurnoverWfAnyDO> value : map.values()) {
                    turnoverInv = turnoverInv.add(NumberUtils.avg(value, 6,o -> o.getTurnoverInv()));
                    weekPeek = weekPeek.add(NumberUtils.avg(value, 6,o -> o.getWeekPeakCore()));
                }
                Date monday = mondays.get(i);
                List<String> tempDays = mondayMap.get(DateUtils.formatDate(monday));
                for (String tempDay : tempDays) {
                    if (req.getIsIgnoreReserved() == null || req.getIsIgnoreReserved()) {
                        turnoverInvMap.put(tempDay, turnoverInv);
                    }else {
                        turnoverInvMap.put(tempDay, weekPeek);
                    }
                }
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }

        return turnoverInvMap;
    }

// 改为前端筛选范围，下面的逻辑可以不要了，注释了只是为了方便查询旧逻辑
//    /**
//     * @param date
//     * @param gap 正数往前取，负数往后取
//     * @return
//     */
//    public List<LocalDate> getDays(LocalDate date, int gap) {
//        List<LocalDate> days = new ArrayList<>();
//        if (gap < 0) {
//            while (gap < 0) {
//                days.add(date.plusDays(gap++));
//            }
//            days.add(date);
//        } else {
//            days.add(date);
//            for (int i = 1; i <= gap; i++) {
//                days.add(date.plusDays(i));
//            }
//        }
//        return days;
//    }

    /**
     * 根据给定的时间范围获取该周
     **/
    public List<ResPlanHolidayWeekDO> getHolidayWeekInfo(LocalDate beginDate, LocalDate endDate) {

        List<ResPlanHolidayWeekDO> all = SpringUtil.getBean(DictServiceImpl.class).getAllHolidayWeekInfos();
        List<ResPlanHolidayWeekDO> dtos = Lang.list();

        for (int i = 0; i < all.size(); i++) {
            ResPlanHolidayWeekDO wdo = all.get(i);
            LocalDate wdoEndDate = LocalDate.parse(wdo.getEnd());
            // 只要该周的结束时间在给定的时间范围内，该周即被选中
            if (wdo != null && !wdoEndDate.isBefore(beginDate) && !wdoEndDate.isAfter(endDate)) {
                dtos.add(wdo);
            }
        }
        return dtos;
    }

    public static class InventoryMaps {
        Map<String, BigDecimal> actualInvMap = new HashMap<>();
        Map<String, BigDecimal> safeInvMap = new HashMap<>();
        Map<String, BigDecimal> reservedCoreMap = new HashMap<>();

    }

    @Table("dws_safe_inventory_history_monthly_df")
    public static class SafeInventoryItem {
        @Column("stat_time")
        String date;
        @Column(value = "cores", computed = "sum(monthly_safety_inv)")
        BigDecimal cores;
        @Column(value = "no_delivery_cores", computed = "sum(no_delivery_monthly_safety_inv)")
        BigDecimal noDeliveryCores;
    }

    @Table("dws_actual_inventory_df")
    public static class ActualInventoryItem {
        @Column("stat_time")
        String date;
        @Column(value = "cores", computed = "sum(actual_inv)")
        BigDecimal cores;
    }

    @Table("dws_buffer_safe_inventory_df")
    public static class BufferSafeInventoryItem {
        @Column("stat_time")
        String date;
        @Column(value = "cores", computed = "sum(buffer_safety_inv)")
        BigDecimal cores;
    }

    @Data
    @Table("inventory_health_manual_config_snapshot")
    public static class ManualConfigItem {
        @Column("stat_time")
        String date;
        @Column("instance_type")
        String instanceType;
        @Column("zone_name")
        String zoneName;
        @Column(value = "cores", computed = "sum(num)")
        BigDecimal cores;
    }

    @Table("dws_safe_inventory_history_monthly_df")
    public static class ManualConfigSafeInventoryItem {
        @Column("stat_time")
        String date;
        @Column("instance_type")
        String instanceType;
        @Column("zone_name")
        String zoneName;
        @Column(value = "cores", computed = "sum(monthly_safety_inv)")
        BigDecimal cores;
        @Column(value = "no_delivery_cores", computed = "sum(no_delivery_monthly_safety_inv)")
        BigDecimal noDeliveryCores;
    }

    @Table("dws_buffer_safe_inventory_df")
    public static class ManualConfigBufferSafeInventoryItem {
        @Column("stat_time")
        String date;
        @Column("instance_type")
        String instanceType;
        @Column("zone_name")
        String zoneName;
        @Column(value = "cores", computed = "sum(buffer_safety_inv)")
        BigDecimal cores;
    }

    @Table("dws_yunxiao_rubik_grid_df")
    public static class ReservedCoreItem {
        @Column("stat_time")
        String date;
        @Column(value = "cores", computed = "sum(cores)")
        BigDecimal cores;
    }
}
