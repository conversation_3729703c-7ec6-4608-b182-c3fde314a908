package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_yunxiao_rubik_grid_df")
public class DwsYuxiaoRubikGridDfDO {
    /**
     * 统计日期
     */
    @Column("stat_time")
    String statTime;

    /**
     * 预扣块 ID
     */
    @Column("grid_id")
    long gridId;

    /**
     * 预扣类型，英文代码
     * {
     *             fault_reserved: "故障预留",
     *             not_reserved: "not_reserved",
     *             reserved_from_host: "普通预留",
     *             reserved_longtime: "长期预留",
     *             reserved_onetime: "单次预留"
     *         }
     */
    @Column("reserve_mode")
    String reserveMode;

    /**
     * 预扣类型，中文名
     */
    @Column("reserve_mode_name")
    String reserveModeName;

    @Column("customhouse_title")
    String customhouseTitle;

    @Column("area_name")
    String areaName;

    @Column("region_name")
    String regionName;

    @Column("zone_name")
    String zoneName;

    /**
     * 实例族
     */
    @Column("instance_type")
    String instanceType;

    /**
     * 实例类型
     */
    @Column("instance_model")
    String instanceModel;

    /**
     * 预扣状态，英文代码
     * {
     *             idle: "空闲",
     *             occupied: "占用",
     *             unassigned: "未分配",
     *             unreachable: "不可用",
     *             destroyed: "到期销毁",
     *             deleted: "删除"
     *         }
     */
    @Column("status")
    String status;

    /**
     * 预扣状态，中文
     */
    @Column("status_name")
    String statusName;

    /**
     * 行业部门
     */
    @Column("industry_dept")
    String industryDept;

    /**
     * 客户名称
     */
    @Column("customer_name")
    String customerName;

    /**
     * 客户简称
     */
    @Column("customer_short_name")
    String customerShortName;

//    /**
//     * UIN
//     */
//    @Column("customer_uin")
//    String customerUin;

    /**
     * 预扣创建时间
     */
    @Column("grid_create_time")
    Date gridCreateTime;

    /**
     * 预扣修改时间
     */
    @Column("grid_update_time")
    Date gridUpdateTime;

    @Column("burst_mem")
    int burstMem;

    @Column("cbs_flag")
    int cbsFlag; //:1

    /**
     * 预扣核心数，单位是核。从云霄的数据转化而来，例如云霄返回 cpu:800，那么这里存储的就是 8 核
     */
    @Column("cores")
    int cores; // :800

    @Column("cpu_mode")
    String cpuMode; // :"custom"

    /**
     * 设备分类
     */
    @Column("device_class")
    String deviceClass; // :"VSGPUNVIDIAV100X_3"

    /**
     * 设备 ID
     */
    @Column("device_id")
    long deviceId; // :414897477

    @Column("file_flag")
    int fileFlag; //:0

    /**
     * 对应云霄返回的 gridOwner 字段
     */
    @Column("appid")
    String appid;

    /**
     * 主机 IP
     */
    @Column("host_ip")
    String hostIp; // :"*************"

    @Column("hypervisor")
    String hypervisor; //:"kvm"

    @Column("idcId")
    long idcId; // :8531

    @Column("inner_switch")
    String innerSwitch; // :"BJ-LS-M101-A13-CE6865-LA25G-01"

    @Column("is_reserve_package")
    int isReservePackage; // :0

    @Column("is_support_cvm")
    boolean isSupportCvm; // :true

    @Column("is_support_eks")
    boolean isSupportEks; //:true

    @Column("lm_flag")
    int lmFlag; //:1

    @Column("match_mode")
    String matchMode; // :""

    @Column("match_rule")
    String matchRule; // :""

    @Column("mem")
    long mem; // :40960

    @Column("netVersion")
    int netVersion; // :3

    @Column("pico_tag")
    String picoTag; // :""

    @Column("pico_tag_size")
    long picoTagSize; // :0

    @Column("pin_type")
    int pinType; // :3

    /**
     * 资源池类型，只拉qcloud类型
     */
    @Column("pool")
    String pool; // :"qcloud"

    @Column("product_category")
    String productCategory; // :""

    @Column("rack_id")
    long rackId; // :338419

    @Column("subnet")
    String subnet; // :"9.125.236.64"

    @Column("svr_version")
    String svrVersion; // :"6.0.3"

    @Column("vendor_model")
    String vendorModel; // :"HUAWEI G560-NV"
}
