package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.excel.core.annotation.DotExcelField;
import cloud.demand.lab.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class ActualDeliverySlaDetailDO {
    @Column(value = "quota_id")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "单号")
    private String quotaId;

    @Column(value = "physics_pcCode")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "固资号")
    private String physicsPcCode;

    @Column(value = "quota_device_class")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "设备类型")
    private String quotaDeviceClass;

    /**
     * 实例类型，根据设备类型从映射表中关联出来
     */
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "实例类型")
    private String instanceType;

    /**
     * 地域信息，根据 campus 映射关联出来
     */
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "境内外")
    private String regionType;
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "区域")
    private String areaName;
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "地域")
    private String regionName;
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "可用区")
    private String zoneName;

    @Column(value = "campus")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "Campus")
    private String campus;

    /**
     * 一行数据就是一台的交付，直接 count 即可
     */
    @Column(value = "num")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "台数")
    private Integer num;

    @Column(value = "core")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "逻辑核心数")
    private Integer core;

    @Column(value = "xy_industry")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "行业")
    private String industry;

    @Column(value = "xy_customer_name")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "客户名称")
    private String customerName;

    @Column(value = "quota_use_time")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "期望交付时间")
    private String quotaUseTime;

    @Column(value = "xy_create_time")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "云产品星云提单时间")
    private String xyCreateTime;

    @Column(value = "quota_create_time")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "Cloud提单时间")
    private String quotaCreateTime;

    @Column(value = "xy_approval_days")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "云审批时长")
    private BigDecimal xyApprovalDays;

    @Column(value = "sla")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "SLA")
    private Integer sla;

    @Column(value = "erp_actual_date")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "ERP交付时间")
    private String erpActualDate;

    /**
     * 如期交付、延期交付
     */
    @Column(value = "delivery_status")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "交付状态")
    private String deliveryStatus;

    @Column(value = "delivery_days")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "ERP交付时间-Cloud提单时间")
    private BigDecimal deliveryDays;

    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "ERP交付时长（如期交付下，交付时间-Cloud提单时间超过SLA值，默认取SLA）")
    private BigDecimal erpDeliveryDays;

    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "交付时长（云审批时长+ERP交付时长）")
    private BigDecimal totalDeliveryDays;

    @Column(value = "cloud_delivery_time")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_DELIVERY_DETAILS, excelColumnName = "产品提货时间")
    private String cloudDeliveryTime;
}
