package cloud.demand.lab.modules.operation_view.inventory_health.entity;


import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("cynosstockinfo")
public class CynosstockinfoDO {

    /** 自增主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 地域<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 地域售卖情况<br/>Column: [regionHasStock] */
    @Column(value = "regionHasStock")
    private Boolean regionHasStock;

    /** cpu<br/>Column: [cpu] */
    @Column(value = "cpu")
    private String cpu;

    /** mem<br/>Column: [memory] */
    @Column(value = "memory")
    private String memory;

    /** 机器类型<br/>Column: [machineType] */
    @Column(value = "machineType")
    private String machineType;

    /** 主库可用区<br/>Column: [masterZone] */
    @Column(value = "masterZone")
    private String masterZone;

    /** 主库售卖情况<br/>Column: [masterHasStock] */
    @Column(value = "masterHasStock")
    private Boolean masterHasStock;

    /** 主库可售卖个数<br/>Column: [masterStockCount] */
    @Column(value = "masterStockCount")
    private Integer masterStockCount;

    /** 是否多可用区部署<br/>Column: [isDeployDouble] */
    @Column(value = "isDeployDouble")
    private Boolean isDeployDouble;

    /** 从库售卖情况<br/>Column: [slaveHasStock] */
    @Column(value = "slaveHasStock")
    private Boolean slaveHasStock;

    /** 从库可用区<br/>Column: [slaveZone] */
    @Column(value = "slaveZone")
    private String slaveZone;

    /** 从库可售卖个数<br/>Column: [slaveStockCount] */
    @Column(value = "slaveStockCount")
    private Integer slaveStockCount;

    /** 从库是否真实可售<br/>Column: [isSlaveAvail] */
    @Column(value = "isSlaveAvail")
    private Boolean isSlaveAvail;

    /** 数据插入时间(年月日)<br/>Column: [createDate] */
    @Column(value = "createDate")
    private LocalDateTime createDate;

}
