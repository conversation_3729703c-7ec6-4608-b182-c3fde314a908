package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_leisure_and_busy_sold_out_data_df")
public class DwsLeisureAndBusySoldOutDataDfDO {

    /** 切片日期<br/>Column: [imp_date] */
    @Column(value = "imp_date")
    private String impDate;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 可用区编号<br/>Column: [zone_id] */
    @Column(value = "zone_id")
    private Long zoneId;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 规格总数<br/>Column: [sold_total] */
    @Column(value = "sold_total")
    private BigDecimal soldTotal;

    /** 售出规格数<br/>Column: [sold_out_total] */
    @Column(value = "sold_out_total")
    private BigDecimal soldOutTotal;

    /** 闲时规格总数<br/>Column: [leisure_sold_total] */
    @Column(value = "leisure_sold_total")
    private BigDecimal leisureSoldTotal;

    /** 闲时售出规格数<br/>Column: [leisure_sold_out_total] */
    @Column(value = "leisure_sold_out_total")
    private BigDecimal leisureSoldOutTotal;

    /** 忙时规格总数<br/>Column: [busy_sold_total] */
    @Column(value = "busy_sold_total")
    private BigDecimal busySoldTotal;

    /** 忙时售出规格数<br/>Column: [busy_sold_out_total] */
    @Column(value = "busy_sold_out_total")
    private BigDecimal busySoldOutTotal;

}
