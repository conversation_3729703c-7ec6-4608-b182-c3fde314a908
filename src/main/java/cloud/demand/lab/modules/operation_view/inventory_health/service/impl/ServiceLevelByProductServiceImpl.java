package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.modules.operation_view.enums.ServiceLevelPlanProductEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.actual.InventoryHealthTrendResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cdb_actual.CDBHealthActualReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.network_actual.NetworkHealthActualTrendReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.out_api.ServiceLevelDataByProductReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.out_api.ServiceLevelDataByProductResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.out_api.ServiceLevelDataByProductResp.Item;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.out_api.ServiceLevelDataReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.DwsCloudServerLevelDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthInstanceFamilyType2;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.PplDatabaseEnum;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CBSHealthActualService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CDBHealthActualService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthActualV2Service;
import cloud.demand.lab.modules.operation_view.inventory_health.service.NetworkHealthActualService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.ServiceLevelByProductService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.VolumeTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.SortingField;
import com.pugwoo.wooutils.collect.SortingUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class ServiceLevelByProductServiceImpl implements ServiceLevelByProductService {

    /**
     * cvm
     */
    @Resource
    private InventoryHealthActualV2Service healthActualV2Service;

    /**
     * cbs
     */
    @Resource
    private CBSHealthActualService cbsHealthActualService;

    /**
     * 数据库
     */
    @Resource
    private CDBHealthActualService cdbHealthActualService;

    /**
     * 网络
     */
    @Resource
    private NetworkHealthActualService networkHealthActualService;

    @Override
    public ServiceLevelDataByProductResp queryServiceLevelDataByProduct(ServiceLevelDataByProductReq req) {
        ServiceLevelDataByProductResp ret = new ServiceLevelDataByProductResp();
        List<Item> items = new ArrayList<>();
        // 校验请求参数
        if (!req.check()) {
            return ret;
        }
        for (String planProduct : req.getPlanProduct()) {
            ServiceLevelPlanProductEnum productEnum = ServiceLevelPlanProductEnum.getByName(planProduct);
            if (productEnum == null) {
                log.error(String.format("规划产品【%s】不支持查询", planProduct));
                return ret;
            }
            ServiceLevelDataReq productReq = req.toReq(planProduct);
            switch (productEnum) {
                case CVM:
                    items.addAll(queryCvmSL(productReq));
                    break;
                case CBS:
                    items.addAll(queryCbsSL(productReq));
                    break;
                case CDB2:
                case CDB:
                    items.addAll(queryCDBSL(productReq));
                    break;
                case CRS:
                    items.addAll(queryCrsSL(productReq));
                    break;
                case CyDB:
                    items.addAll(queryCyDbSL(productReq));
                    break;
                case VPC:
                    items.addAll(queryVpcSL(productReq));
                    break;
            }
        }
        List<SortingField<Item, ? extends Comparable<?>>> sortList = new ArrayList<>();
        sortList.add(new SortingField<Item, String>() {
            @Override
            public String apply(Item item) {
                return item.getPlanProduct();
            }
        });
        sortList.add(new SortingField<Item, String>() {
            @Override
            public String apply(Item item) {
                return item.getYearMonth();
            }
        });
        SortingUtils.sort(items, sortList);
        ret.setItems(items);
        return ret;
    }

    /**
     * 查询 cvm 服务水平
     *
     * @param req 请求
     * @return
     */
    public List<Item> queryCvmSL(ServiceLevelDataReq req) {
        InventoryHealthTrendReq trendReq = new InventoryHealthTrendReq();
        trendReq.setCustomerCustomGroup("ALL");
        trendReq.setCustomhouseTitle(req.getCustomhouseTitle());
        trendReq.setCountry(req.getCountryName());
        trendReq.setExpandField("areaName");
        trendReq.setInstanceTypeCategory(ListUtils.newList(InventoryHealthInstanceFamilyType2.PRINCIPAL.getCode()));
        trendReq.setInvDetailType(ListUtils.newList(
                "用户预扣",
                "大核库存",
                "全空母机",
                "上架缓冲",
                "大核预留",
                "上线",
                "搬迁",
                "改造",
                "冗余库存",
                "退役转出"
        ));
        trendReq.setMaterialType(ListUtils.newList(
                "好料",
                "流转库存",
                "冗余库存",
                "周转库存"
        ));
        trendReq.setSoldType("忙时");
        trendReq.setTimeDimension("month");
        trendReq.setZoneCategory(ListUtils.newList(
                InventoryHealthZoneType.PRINCIPAL.getCode()
        ));
        trendReq.setStart(req.getStartYearMonth() + "-01");
        trendReq.setEnd(req.getEndYearMonth() + "-01");
        trendReq.setIsIncludeReserved(false);
        trendReq.setIsIgnoreReserved(false);
        trendReq.setInstanceType(ListUtils.newList());
        InventoryHealthTrendResp inventoryHealthTrendResp = healthActualV2Service.queryInventoryHealthTrend(trendReq);
        List<Item> items = new ArrayList<>();
        List<InventoryHealthTrendResp.Item> data = inventoryHealthTrendResp.getData();
        if (ListUtils.isNotEmpty(data)) {
            // 聚合汇总，只有年月维度
            ListUtils.groupBy(data,
                    InventoryHealthTrendResp.Item::getDate).forEach((date, ls) -> {
                Item item = new Item();
                item.setYearMonth(date);
                BigDecimal sold = BigDecimal.ZERO;
                BigDecimal soldOut = BigDecimal.ZERO;
                BigDecimal apiTotal = BigDecimal.ZERO;
                BigDecimal apiSucTotal = BigDecimal.ZERO;
                for (InventoryHealthTrendResp.Item l : ls) {
                    // 服务水平计算：
                    // 服务水平 = API 成功率 * 70% + (1 - 售罄率) * 30%
                    // 售罄率 = 售罄规格数 / 售卖规格数 （以 version-zoneName-instanceFamily为 key，相加后算均值）
                    List<DwsCloudServerLevelDO> actualSla = l.getActualSla();
                    if (ListUtils.isEmpty(actualSla)) {
                        continue;
                    }

                    for (Entry<String, List<DwsCloudServerLevelDO>> entry : ListUtils.groupBy(actualSla,
                                    ac -> String.join("@", ac.getVersion(), ac.getZoneName(), ac.getInstanceFamily()))
                            .entrySet()) {
                        List<DwsCloudServerLevelDO> slList = entry.getValue();

                        for (DwsCloudServerLevelDO serverLevelDO : slList) {
                            apiTotal = apiTotal.add(serverLevelDO.getApiTotal());
                            apiSucTotal = apiSucTotal.add(serverLevelDO.getApiSucTotal());
                        }

                         // 售罄数和售卖数只用加一次
                        sold = sold.add(slList.get(0).getSoldTotal());
                        soldOut = soldOut.add(slList.get(0).getSoldOutTotal());
                    }
                }
                BigDecimal apiRate = apiTotal.equals(BigDecimal.ZERO)
                        ? BigDecimal.ONE : apiSucTotal.divide(apiTotal, 4, RoundingMode.HALF_UP); // api 请求成功率 默认 100%
                BigDecimal soldRate = sold.equals(BigDecimal.ZERO)
                        ? BigDecimal.ZERO : soldOut.divide(sold, 4, RoundingMode.HALF_UP); // 售罄率 默认 0%
                BigDecimal sl = apiRate.multiply(BigDecimal.valueOf(0.7))
                        .add(BigDecimal.ONE.subtract(soldRate)
                                .multiply(BigDecimal.valueOf(0.3))); // 服务水平 = API 成功率 * 70% + (1 - 售罄率) * 30%
                item.setServiceLevel(sl);
                item.setPlanProduct(req.getPlanProduct());
                items.add(item);
            });
        }
        return items;
    }

    /**
     * 查询 cbs 服务水平
     *
     * @param req 请求
     * @return
     */
    public List<Item> queryCbsSL(ServiceLevelDataReq req) {
        List<Item> items = new ArrayList<>();
        CBSHealthActualReq cbsReq = new CBSHealthActualReq();
        cbsReq.setStart(req.getStartYearMonth() + "-01");
        // 月最后一天，例子：2025-07-01 ---> 2025-07-31
        cbsReq.setEnd(getEndDayByYm(req.getEndYearMonth()));
        cbsReq.setDateType("month");
        cbsReq.setVolumeType(VolumeTypeEnum.PREMIUM.getCode());
        cbsReq.setZoneCategory(ListUtils.newList(InventoryHealthZoneType.PRINCIPAL.getCode()));
        cbsReq.setCountry(req.getCountryName());
        cbsReq.setCustomhouseTitle(req.getCustomhouseTitle());
        List<CBSHealthActualData> cbsHealthActualData = cbsHealthActualService.queryCBSHealthActualTrendReport(cbsReq);
        ListUtils.groupBy(cbsHealthActualData, CBSHealthActualData::getStatTime).forEach((date, ls) -> {
            Item item = new Item();
            item.setYearMonth(date);
            item.setPlanProduct(req.getPlanProduct());
            BigDecimal failDisk = BigDecimal.ZERO;
            BigDecimal successDisk = BigDecimal.ZERO;
            for (CBSHealthActualData l : ls) {
                failDisk = failDisk.add(l.getFailedDiskSize());
                successDisk = successDisk.add(l.getSuccessDiskSize());
            }
            BigDecimal totalDisk = failDisk.add(successDisk);
            item.setServiceLevel(
                    totalDisk.equals(BigDecimal.ZERO) ? BigDecimal.ZERO:
                            successDisk.divide(totalDisk, 4, RoundingMode.HALF_UP));
            items.add(item);
        });
        return items;
    }

    // =============== 数据库 ================
    public List<Item> queryCDBSL(ServiceLevelDataReq req) {
        CDBHealthActualReq dbReq = buildDBReq(req);
        dbReq.setProductType(PplDatabaseEnum.MongoDB.getAlias());
        return buildDBResp(req.getPlanProduct(), cdbHealthActualService.queryCDBHealthActualReport(dbReq));
    }

    public List<Item> queryCrsSL(ServiceLevelDataReq req) {
        CDBHealthActualReq dbReq = buildDBReq(req);
        dbReq.setProductType(PplDatabaseEnum.Redis.getAlias());
        return buildDBResp(req.getPlanProduct(), cdbHealthActualService.queryCDBHealthActualReport(dbReq));
    }

    public List<Item> queryCyDbSL(ServiceLevelDataReq req) {
        CDBHealthActualReq dbReq = buildDBReq(req);
        dbReq.setProductType(PplDatabaseEnum.TDSQLC.getAlias());
        return buildDBResp(req.getPlanProduct(), cdbHealthActualService.queryCDBHealthActualReport(dbReq));
    }

    public CDBHealthActualReq buildDBReq(ServiceLevelDataReq req) {
        CDBHealthActualReq ret = new CDBHealthActualReq();
        ret.setStart(req.getStartYearMonth() + "-01");
        ret.setEnd(getEndDayByYm(req.getEndYearMonth()));
        ret.setDateType("month");
        ret.setZoneCategory(ListUtils.newList(InventoryHealthZoneType.PRINCIPAL.getCode()));
        ret.setCustomhouseTitle(req.getCustomhouseTitle());
        ret.setCountry(req.getCountryName());
        return ret;
    }

    public List<Item> buildDBResp(String planProduct, List<CDBHealthActualData> data) {
        List<Item> ret = new ArrayList<>();
        ListUtils.groupBy(data,
                CDBHealthActualData::getStatTime).forEach((date, ls) -> {
            Item item = new Item();
            item.setYearMonth(date);
            item.setPlanProduct(planProduct);
            double totalCount = 0;
            double sucCount = 0;
            for (CDBHealthActualData l : ls) {
                totalCount += l.getTotalCount();
                sucCount += l.getSucCount();
            }

            item.setServiceLevel(BigDecimal.valueOf(totalCount == 0 ? 0 : sucCount / totalCount).setScale(4,RoundingMode.HALF_UP));
            ret.add(item);
        });
        return ret;
    }

    // =============== 网络 ================

    public List<Item> queryVpcSL(ServiceLevelDataReq req) {
        NetworkHealthActualTrendReq networkReq = new NetworkHealthActualTrendReq();
        networkReq.setProduct(ListUtils.newList("EIP", "CLB"));
        networkReq.setDateType("month");
        networkReq.setStart(req.getStartYearMonth() + "-01");
        networkReq.setEnd(getEndDayByYm(req.getEndYearMonth()));
        networkReq.setCountry(req.getCountryName());
        networkReq.setCustomhouseTitle(req.getCustomhouseTitle());
        List<NetworkHealthActualTrendData> data = networkHealthActualService.queryNetworkHealthActualTrendReport(
                networkReq);
        List<Item> items = new ArrayList<>();
        ListUtils.groupBy(data, NetworkHealthActualTrendData::getStatTime).forEach((date, ls) -> {
            Item item = new Item();
            item.setYearMonth(date);
            item.setPlanProduct(req.getPlanProduct());
            BigDecimal totalNum = BigDecimal.ZERO;
            BigDecimal failNum = BigDecimal.ZERO;
            for (NetworkHealthActualTrendData l : ls) {
                totalNum = totalNum.add(l.getTotalNum());
                failNum = failNum.add(l.getInternalFailedNum().add(l.getInsufficientFailedNum()));
            }
            BigDecimal sucNum = totalNum.subtract(failNum);
            item.setServiceLevel(totalNum.equals(BigDecimal.ZERO) ? BigDecimal.ZERO :
                    sucNum.divide(totalNum, 4 ,RoundingMode.HALF_UP));
            items.add(item);
        });
        return items;
    }

    /**
     * 获取年月最后一天
     *
     * @param yearMonth 年月
     * @return
     */
    private String getEndDayByYm(String yearMonth) {
        return DateUtils.format(DateUtils.parseLocalDate(yearMonth + "-01").plusMonths(1).plusDays(-1));
    }
}
