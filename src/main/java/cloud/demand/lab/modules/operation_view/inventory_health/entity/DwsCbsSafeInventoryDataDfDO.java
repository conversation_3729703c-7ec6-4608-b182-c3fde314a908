package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import lombok.Data;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("dws_cbs_safe_inventory_data_df")
public class DwsCbsSafeInventoryDataDfDO {

    /** 切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** 云盘类型<br/>Column: [volume_type] */
    @Column(value = "volume_type")
    private String volumeType;

    /** 最低库存<br/>Column: [min_stock] */
    @Column(value = "min_stock")
    private BigDecimal minStock;

    /** 缓冲库存<br/>Column: [buffer_stock] */
    @Column(value = "buffer_stock")
    private BigDecimal bufferStock;

    /** 安全库存<br/>Column: [safe_inventory] */
    @Column(value = "safe_inventory")
    private BigDecimal safeInventory;

    /** 好料库存<br/>Column: [online_good_stock] */
    @Column(value = "online_good_stock")
    private BigDecimal onlineGoodStock;

    /** 线下库存<br/>Column: [offline_stock] */
    @Column(value = "offline_stock")
    private BigDecimal offlineStock;

    /** 差料库存<br/>Column: [online_bad_stock] */
    @Column(value = "online_bad_stock")
    private BigDecimal onlineBadStock;

    /** 呆料库存<br/>Column: [online_idle_stock] */
    @Column(value = "online_idle_stock")
    private BigDecimal onlineIdleStock;

    /** 实际库存<br/>Column: [actual_inventory] */
    @Column(value = "actual_inventory")
    private BigDecimal actualInventory;

    public CbsInventoryAdjustVersionDataDO transform() {
        CbsInventoryAdjustVersionDataDO item = new CbsInventoryAdjustVersionDataDO();
        item.setZoneName(zoneName);
        item.setRegionName(regionName);
        item.setCustomhouseTitle(customhouseTitle);
        item.setVolumeType(volumeType);
        item.setOriginSafeInventory(safeInventory);
        item.setAdjustSafeInventory(safeInventory);
        item.setMinStock(minStock);
        item.setBufferStock(bufferStock);
        item.setGoodStockRemove(BigDecimal.ZERO);
        item.setGoodStockOld(BigDecimal.ZERO);
        return item;
    }

}
