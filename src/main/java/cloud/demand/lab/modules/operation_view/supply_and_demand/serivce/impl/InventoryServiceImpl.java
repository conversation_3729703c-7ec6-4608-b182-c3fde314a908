package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.CbsInventoryAdjustVersionDataDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.AdsInventoryHealthSupplySummaryDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.UnitEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandParamTypeReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.AdsInventoryHealthSupplySummaryDfVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.req.SupplyDemandHedgingReq;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SupplyDemandHedgingItemVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.InventoryService;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.operation_view.util.SopDateUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import report.utils.query.SimpleSqlBuilder;
import report.utils.query.WhereBuilder;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/18 14:54
 */
@Service
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public List<String> queryParams(SupplyDemandParamTypeReq req) {
        if (StringUtils.isBlank(req.getParamType())) {
            return new ArrayList<>();
        }

        String column = ORMUtils.getColumnByFieldName(AdsInventoryHealthSupplySummaryDfDO.class, req.getParamType());
        if (column == null) {
            return new ArrayList<>();
        }
        String sql = "select distinct ${paramType} from cloud_demand.ads_inventory_health_supply_summary_df where product_type = ? ";

        return ckcldDBHelper.getRaw(String.class, sql.replace("${paramType}", column), req.getProductCategory());
    }

    public List<SupplyDemandHedgingItemVO> queryInventory(SupplyDemandHedgingReq req) {
        WhereBuilder whereBuilder = new WhereBuilder(req, AdsInventoryHealthSupplySummaryDfDO.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        if (req.getProductCategory().equals("CBS") && (ListUtils.isEmpty(req.getLineType()) || req.getLineType().contains("好料"))) {
            //materialType只对好料生效
            if (ListUtils.isNotEmpty(req.getMaterialType())) {
                List<String> lineType = req.getLineType();
                List<String> materialType = req.getMaterialType();
                req.setMaterialType(null);
                req.setLineType(null);
                whereBuilder = new WhereBuilder(req, AdsInventoryHealthSupplySummaryDfDO.class);
                whereSQL = whereBuilder.whereSQL();
                if (ListUtils.isEmpty(lineType)) {
                    lineType = Arrays.asList("好料", "呆料", "差料");
                }
                List<String> leave = lineType.stream().filter(o -> !o.equals("好料")).collect(Collectors.toList());
                if (ListUtils.isNotEmpty(leave)) {
                    whereSQL.and("((line_type = '好料' and material_type in (?)) or line_type in (?))", materialType, leave);
                }else {
                    whereSQL.and("line_type = '好料' and material_type in (?)", materialType);
                }
            }
        }else {
            req.setMaterialType(null);
            whereBuilder = new WhereBuilder(req, AdsInventoryHealthSupplySummaryDfDO.class);
            whereSQL = whereBuilder.whereSQL();
        }
        List<String> statTimeList = ListUtils.newArrayList();
        if (BooleanUtils.isTrue(req.isOverView())) {
            statTimeList.add(req.getStatTime());
        } else {
            if (StringUtils.isNotBlank(req.getStartStatTime())) {
                statTimeList.add(req.getStartStatTime());
            }
            if (StringUtils.isNotBlank(req.getEndStatTime())) {
                statTimeList.add(req.getEndStatTime());
            }
        }

        whereSQL.and(" stat_time in (?) ", statTimeList);
        whereSQL.and(" product_type = ? ", req.getProductCategory());
        whereSQL.andIf(ListUtils.isNotEmpty(req.getProduct()), " product in (?) ", req.getProduct());
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/query_inventory.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereSQL.getSQL());
        sql = SimpleSqlBuilder.doReplace(sql, "unit", req.getUnit());

        List<String> allDims = ListUtils.newArrayList("zoneCategory", "instanceCategory", "customhouseTitle", "countryName", "areaName",
                "regionName", "zoneName", "instanceGroup", "instanceType", "volumeType", "product");
        List<String> fieldNames = Arrays.stream(AdsInventoryHealthSupplySummaryDfDO.class.getDeclaredFields()).map(Field::getName).collect(Collectors.toList());
        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), allDims);
        List<AdsInventoryHealthSupplySummaryDfVO> dataList = ckcldDBHelper.getRaw(AdsInventoryHealthSupplySummaryDfVO.class, sql, whereSQL.getParams());
        if (StringUtils.equals(req.getUnit(), UnitEnum.NUM.getName()) && StringUtils.equals(req.getProductCategory(), ProductCategoryEnum.DB.getName())) {
            //获取数据库内存容量(目前只考虑一个规划产品一个机型
            Map<String, BigDecimal> deviceMap = demandDBHelper.getAll(BasDeviceMemoryDO.class)
                    .stream().collect(Collectors.toMap(BasDeviceMemoryDO::getPlanProductName, BasDeviceMemoryDO::getGbSaleMemory, (k1, k2) -> k1));
            for (AdsInventoryHealthSupplySummaryDfVO item : dataList) {
                BigDecimal singleMemory = deviceMap.getOrDefault(item.getProduct(), BigDecimal.ZERO);
                if (BigDecimal.ZERO.compareTo(singleMemory) == 0) {
                    item.setBeginInventory(BigDecimal.ZERO);
                } else {
                    item.setBeginInventory(SoeCommonUtils.divide(item.getBeginInventory(), singleMemory));
                }
            }
        }
        if (StringUtils.equals(req.getProductCategory(), "CBS")) {
            if (ListUtils.isEmpty(req.getLineType()) || req.getLineType().contains("好料")) {
                if (req.getGoodMaterialTag().equals("goodMetrialType") && ListUtils.isNotEmpty(req.getGoodMaterialType())
                        && req.getGoodMaterialType().size() != 3) {
                    WhereSQL condition = new WhereBuilder(req, CbsInventoryAdjustVersionDataDO.class).whereSQL();
                    //获取配置
                    Map<String, String> weekMap = ListUtils.toMap(statTimeList, o -> o, o -> {
                        LocalDate localDate = DateUtils.parseLocalDate(o);
                        int isoWeek = localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
                        int weekBasedYear = localDate.get(IsoFields.WEEK_BASED_YEAR);
                        return weekBasedYear + "-W" + SopDateUtils.fixNumber(isoWeek);
                    });
                    List<String> versions = weekMap.values().stream().distinct().collect(Collectors.toList());
                    condition.and("version_code in (?)", versions);
                    List<CbsInventoryAdjustVersionDataDO> all = demandDBHelper.getAll(CbsInventoryAdjustVersionDataDO.class, condition.getSQL(), condition.getParams());
                    Map<String, List<CbsInventoryAdjustVersionDataDO>> configMap = ListUtils.toMapList(all,
                            o -> String.join("@", o.getVersionCode(),
                                    o.getVolumeType(), o.getZoneName()), o -> o);
                    for (AdsInventoryHealthSupplySummaryDfVO vo :dataList){
                        LocalDate localDate = DateUtils.parseLocalDate(vo.getStatTime());
                        int isoWeek = localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
                        int weekBasedYear = localDate.get(IsoFields.WEEK_BASED_YEAR);
                        String week = weekBasedYear + "-W" + SopDateUtils.fixNumber(isoWeek);
                        String key = String.join("@", week, vo.getVolumeType(), vo.getZoneName());
                        List<CbsInventoryAdjustVersionDataDO> configList = configMap.get(key);
                        BigDecimal remove = NumberUtils.sum(configList, CbsInventoryAdjustVersionDataDO::getGoodStockRemove);
                        BigDecimal old = NumberUtils.sum(configList, CbsInventoryAdjustVersionDataDO::getGoodStockOld);
                        BigDecimal temp = vo.getBeginInventory().subtract(remove).subtract(old);
                        BigDecimal sum = BigDecimal.ZERO;
                        for (String str : req.getGoodMaterialType()) {
                            if (str.equals("实际可售")) {
                                sum = sum.add(temp);
                            }else if (str.equals("云徙")) {
                                sum = sum.add(remove);
                            }else {
                                sum = sum.add(old);
                            }
                        }
                        vo.setBeginInventory(sum);
                    }
                }
            }
        }
        List<SupplyDemandHedgingItemVO> itemList = ListUtils.newArrayList();
        itemList.addAll(ListUtils.transform(dataList, item -> SupplyDemandHedgingItemVO.transform(item, "withholdInventory")));
        itemList.addAll(ListUtils.transform(dataList, item -> SupplyDemandHedgingItemVO.transform(item, "actualInventory")));

        List<String> mergeDims = ListUtils.union(req.getDims(), ListUtils.newArrayList("statTime", "type"));
        return SupplyDemandHedgingItemVO.mergeList(itemList, mergeDims);
    }
}
