package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.CBSAdjustVersionData;
import lombok.Data;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("cbs_inventory_adjust_version")
public class CbsInventoryAdjustVersionDO extends BaseDO {

    /** 版本<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 版本开始时间<br/>Column: [version_start_date] */
    @Column(value = "version_start_date")
    private String versionStartDate;

    /** 版本结束时间<br/>Column: [version_end_date] */
    @Column(value = "version_end_date")
    private String versionEndDate;


    public CBSAdjustVersionData transform() {
        CBSAdjustVersionData item = new CBSAdjustVersionData();
        item.setVersionCode(versionCode);
        item.setStartDate(versionStartDate);
        item.setEndDate(versionEndDate);
        return item;
    }

}
