package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryBasicReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleOutData;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleOutReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleReq;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleResp.Item;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_disassemble.InventoryDisassembleTodayResp;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainInstanceTypeConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthAlgorithm;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryDisassembleService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.lab.modules.operation_view.inventory_health.service.impl.InventoryTurnoverServiceImpl.SafeInventoryDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.InventoryHealthManualConfigSnapshotDO;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import cloud.demand.lab.modules.operation_view.util.SopDateUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.random.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class InventoryDisassembleServiceImpl implements InventoryDisassembleService {

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    RedisHelper redisHelper;

    @Resource
    DBHelper demandDBHelper;


    @Resource
    OutsideViewOldService outsideViewOldService;

    @Resource
    InventoryHealthDictService inventoryHealthDictService;

    @Resource
    private DBHelper ckstdcrpDBHelper;


     public final ExecutorService threadPool = Executors.newFixedThreadPool(3);


    @Override
    public InventoryDisassembleResp queryInventoryDisassembleReport(InventoryDisassembleReq req) {
        List<Item> data = queryInventoryDisassembleReportOrigin(req);
        //按照维度进行聚合
        if (ListUtils.isNotEmpty(req.getDimension())) {
            Map<String, List<Item>> mapList = ListUtils.toMapList(data, o -> getDimensionString(req.getDimension(), o),
                    o -> o);
            List<Item> finalData = new ArrayList<>();
            for (Entry<String, List<Item>> entry : mapList.entrySet()) {
                Item item = new Item();
                List<Item> value = entry.getValue();
                List<String> dimension = req.getDimension();
                for (String dim : dimension) {
                    switch (dim) {
                        case "instanceCategory":
                            item.setInstanceCategory(value.get(0).getInstanceCategory());
                            break;
                        case "zoneCategory":
                            item.setZoneCategory(value.get(0).getZoneCategory());
                            break;
                        case "instanceType":
                            item.setInstanceType(value.get(0).getZoneCategory());
                            break;
                        case "customhouseTitle":
                            item.setCustomhouseTitle(value.get(0).getCustomhouseTitle());
                            break;
                        case "regionName":
                            item.setRegionName(value.get(0).getRegionName());
                            break;
                        case "areaName":
                            item.setAreaName(value.get(0).getAreaName());
                            break;
                        case "zoneName":
                            item.setZoneName(value.get(0).getZoneName());
                            break;
                    }
                }
                item.setStatTime(value.get(0).getStatTime());
                item.setActualInventory(NumberUtils.sum(value, Item::getActualInventory).intValue());
                item.setActualTurnoverInventory(NumberUtils.sum(value, Item::getActualTurnoverInventory).intValue());
                item.setPreDeductInventory(NumberUtils.sum(value, Item::getPreDeductInventory).intValue());
                item.setNotPreDeductTurnoverInventory(NumberUtils.sum(value, Item::getNotPreDeductTurnoverInventory).intValue());
                item.setActualSafeInventory(NumberUtils.sum(value, Item::getActualSafeInventory).intValue());
                item.setRedundantInventory(NumberUtils.sum(value, Item::getRedundantInventory).intValue());
                finalData.add(item);
            }
            InventoryDisassembleResp resp = new InventoryDisassembleResp();
            resp.setData(finalData);
            return resp;
        }
        InventoryDisassembleResp resp = new InventoryDisassembleResp();
        resp.setData(data);
        resp.setWeekAvg(getWeekAvg(data));
        resp.setMonthAvg(getMonthAvg(data));
        return resp;
    }

    public String getDimensionString(List<String> dimension, Item item) {
        List<String> dimList = new ArrayList<>();
        for (String dim : dimension) {
            switch (dim) {
                case "instanceCategory":
                    dimList.add(item.getInstanceCategory());
                    break;
                case "zoneCategory":
                    dimList.add(item.getZoneCategory());
                    break;
                case "instanceType":
                    dimList.add(item.getInstanceType());
                    break;
                case "customhouseTitle":
                    dimList.add(item.getCustomhouseTitle());
                    break;
                case "regionName":
                    dimList.add(item.getRegionName());
                    break;
                case "areaName":
                    dimList.add(item.getAreaName());
                    break;
                case "zoneName":
                    dimList.add(item.getZoneName());
                    break;
            }
        }
        return String.join("@", dimList);
    }

    public List<InventoryDisassembleResp.AvgItem> getWeekAvg(List<Item> items) {
        //先按照日进行聚合 算出日总和
        List<InventoryDisassembleResp.AvgItem> dateAvg = getDateAvg(items);
        List<InventoryDisassembleResp.AvgItem> weekAvg = new ArrayList<>();
        Map<String, List<InventoryDisassembleResp.AvgItem>> mapList = ListUtils.toMapList(dateAvg, o -> {
            LocalDate localDate = DateUtils.parseLocalDate(o.getStatTime());
            int week = localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            int year = localDate.get(IsoFields.WEEK_BASED_YEAR);
            return year + "-W" + fixNumber(week);
        }, o -> o);
        for (Entry<String, List<InventoryDisassembleResp.AvgItem>> entry : mapList.entrySet()) {
            InventoryDisassembleResp.AvgItem avgItem = new InventoryDisassembleResp.AvgItem();
            avgItem.setStatTime(entry.getKey());
            int size = entry.getValue().size();
            List<String> dates = entry.getValue().stream().map(InventoryDisassembleResp.AvgItem::getStatTime).
                    distinct().sorted(String::compareTo).collect(Collectors.toList());
            avgItem.setStartDate(dates.get(0));
            avgItem.setEndDate(dates.get(dates.size() - 1));
            BigDecimal actualSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getActualInventory);
            BigDecimal redundantSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getRedundantInventory);
            BigDecimal safeSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getActualSafeInventory);
            BigDecimal turnoverSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getActualTurnoverInventory);
            avgItem.setActualInventory(actualSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            avgItem.setRedundantInventory(redundantSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            avgItem.setActualSafeInventory(safeSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            avgItem.setActualTurnoverInventory(turnoverSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            weekAvg.add(avgItem);
        }
        return weekAvg;

    }

    public List<InventoryDisassembleResp.AvgItem> getMonthAvg(List<Item> items) {
        //先按照日进行聚合 算出日总和
        List<InventoryDisassembleResp.AvgItem> dateAvg = getDateAvg(items);
        List<InventoryDisassembleResp.AvgItem> monthAvg = new ArrayList<>();
        Map<String, List<InventoryDisassembleResp.AvgItem>> mapList = ListUtils.toMapList(dateAvg, o -> {
            LocalDate localDate = DateUtils.parseLocalDate(o.getStatTime());
            return YearMonth.of(localDate.getYear(), localDate.getMonthValue()).toString();
        }, o -> o);
        for (Entry<String, List<InventoryDisassembleResp.AvgItem>> entry : mapList.entrySet()) {
            InventoryDisassembleResp.AvgItem avgItem = new InventoryDisassembleResp.AvgItem();
            avgItem.setStatTime(entry.getKey());
            int size = entry.getValue().size();
            List<String> dates = entry.getValue().stream().map(InventoryDisassembleResp.AvgItem::getStatTime).
                    distinct().sorted(String::compareTo).collect(Collectors.toList());
            avgItem.setStartDate(dates.get(0));
            avgItem.setEndDate(dates.get(dates.size() - 1));
            BigDecimal actualSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getActualInventory);
            BigDecimal redundantSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getRedundantInventory);
            BigDecimal safeSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getActualSafeInventory);
            BigDecimal turnoverSum = NumberUtils.sum(entry.getValue(), InventoryDisassembleResp.AvgItem::getActualTurnoverInventory);
            avgItem.setActualInventory(actualSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            avgItem.setRedundantInventory(redundantSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            avgItem.setActualSafeInventory(safeSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            avgItem.setActualTurnoverInventory(turnoverSum.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP).intValue());
            monthAvg.add(avgItem);
        }
        return monthAvg;
    }

    public List<InventoryDisassembleResp.AvgItem> getDateAvg(List<Item> items) {
        Map<String, List<Item>> dateMap = ListUtils.toMapList(items, Item::getStatTime, o -> o);
        List<InventoryDisassembleResp.AvgItem> dateSum = new ArrayList<>();
        for (Entry<String, List<Item>> entry : dateMap.entrySet()) {
            InventoryDisassembleResp.AvgItem avgItem = new InventoryDisassembleResp.AvgItem();
            avgItem.setStatTime(entry.getKey());
            List<Item> value = entry.getValue();
            int actualInventory = 0;
            int actualTurnoverInventory = 0;
            int actualSafeInventory = 0;
            int redundantInventory = 0;
            Map<String, List<Item>> instanceMap = ListUtils.toMapList(value, Item::getInstanceType, o -> o);
            for (Entry<String, List<Item>> insEntry : instanceMap.entrySet()) {
                Map<String, List<Item>> mapList = ListUtils.toMapList(insEntry.getValue(), Item::getCustomhouseTitle, o -> o);
                for (Entry<String, List<Item>> tempEntry : mapList.entrySet()) {
                    int actualSum = NumberUtils.sum(tempEntry.getValue(), Item::getActualInventory).intValue();
                    int safeSum = NumberUtils.sum(tempEntry.getValue(), Item::getSafeInventory).intValue();
                    int turnSum = NumberUtils.sum(tempEntry.getValue(), Item::getActualTurnoverInventory).intValue();
                    int actualSafeSum = Math.min(safeSum, Math.max(actualSum - turnSum, 0));
                    int redundantSum = actualSum - turnSum - actualSafeSum;
                    actualInventory += actualSum;
                    actualTurnoverInventory += turnSum;
                    actualSafeInventory += actualSafeSum;
                    redundantInventory += redundantSum;
                }
            }
            avgItem.setActualInventory(actualInventory);
            avgItem.setActualTurnoverInventory(actualTurnoverInventory);
            avgItem.setActualSafeInventory(actualSafeInventory);
            avgItem.setRedundantInventory(redundantInventory);
            dateSum.add(avgItem);
        }
        return dateSum;
    }

    public String fixNumber(int num){
        if (num < 10 && num >= 0){
            return "0" + num;
        }
        return "" + num;
    }

    public List<Item> queryInventoryDisassembleReportOrigin(InventoryDisassembleReq req) {
        WhereSQL condition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        WhereSQL cateCondition = bean.genCategoryCondition(req.getZoneCategory(), req.getInstanceCategory(), false,
                DateUtils.formatDate(DateUtils.yesterday()), req.getCustomhouseTitle());
        condition.and(cateCondition);
        Map<String, ActualInventoryDO> actualMap;
        Map<String, TurnoverInventoryData> turnMap;
        Map<String, BigDecimal> safeMap;
        Future<Map<String, ActualInventoryDO>> acutalFuture = threadPool.submit(() -> getActualInventoryData(condition, req));
        Future<Map<String, TurnoverInventoryData>> turnFuture = threadPool.submit(() -> getTurnoverData(condition, req));
        Future<Map<String, BigDecimal>> safeFuture = threadPool.submit(() -> getTargetSafeInventoryData(condition, req));
        try {
            actualMap = acutalFuture.get();
            turnMap = turnFuture.get();
            safeMap = safeFuture.get();
        } catch (InterruptedException  | ExecutionException e) {
            throw new RuntimeException("线程执行失败，原因：" + e.getStackTrace());
        }
        List<Item> data = new ArrayList<>();
        List<InventoryHealthMainZoneNameConfigDO> zoneConfigList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        List<InventoryHealthMainInstanceTypeConfigDO> insConfigList = demandDBHelper.getAll(InventoryHealthMainInstanceTypeConfigDO.class, "where date = ?", DateUtils.formatDate(DateUtils.yesterday()));
        Map<String, InventoryHealthMainZoneNameConfigDO> zoneConfigMap = ListUtils.toMap(zoneConfigList, o -> o.getZoneName(),
                o -> o);
        Map<String, List<InventoryHealthMainInstanceTypeConfigDO>> insConfigMap = ListUtils.toMapList(insConfigList,
                o -> o.getInstanceType(), o -> o);
        for (Entry<String, ActualInventoryDO> entry : actualMap.entrySet()) {
            ActualInventoryDO actual = entry.getValue();
            String key = entry.getKey();
            TurnoverInventoryData turnoverInv = turnMap.get(key);
            BigDecimal safeInv = safeMap.get(key) == null ? BigDecimal.ZERO : safeMap.get(key);
            Item item = new Item();
            item.setStatTime(actual.getStatTime());
            item.setInstanceType(actual.getInstanceType());
            item.setZoneName(actual.getZoneName());
            item.setAreaName(actual.getAreaName());
            item.setRegionName(actual.getRegionName());
            item.setCustomhouseTitle(actual.getCustomhouseTitle());
            item.setActualInventory(actual.getActualInv());
            InventoryHealthMainZoneNameConfigDO zoneConfig = zoneConfigMap.get(
                    item.getZoneName());
            if (zoneConfig != null) {
                item.setZoneCategory(zoneConfig.getTypeName());
            }else {
                item.setZoneCategory("未分类");
            }
            List<InventoryHealthMainInstanceTypeConfigDO> insConfig = insConfigMap.get(
                    item.getInstanceType());
            if (ListUtils.isNotEmpty(insConfig)) {
                String customhouse = item.getCustomhouseTitle();
                if (item.getCustomhouseTitle().equals("境外")) {
                    List<String> collect = insConfig.stream().map(
                            InventoryHealthMainInstanceTypeConfigDO::getRegionType).collect(Collectors.toList());
                    if (collect.contains("海外")) {
                        customhouse = "海外";
                    }
                }
                Map<String, InventoryHealthMainInstanceTypeConfigDO> map = ListUtils.toMap(insConfig,
                        o -> o.getRegionType(), o -> o);
                InventoryHealthMainInstanceTypeConfigDO temp = map.get(customhouse);
                if (temp != null) {
                    item.setInstanceCategory(temp.getType2Name());
                }else {
                    item.setInstanceCategory("未分类");
                }
            }else {
                item.setInstanceCategory("未分类");
            }
            if (turnoverInv != null) {
                item.setNotPreDeductTurnoverInventory(turnoverInv.getNotPreDeductTurnoverCores());
                item.setPreDeductInventory(turnoverInv.getPreDeductInventory());
                item.setActualTurnoverInventory(turnoverInv.getTurnoverInventory());
            }else {
                item.setNotPreDeductTurnoverInventory(0);
                item.setPreDeductInventory(0);
                item.setActualTurnoverInventory(0);
            }
            item.setSafeInventory(safeInv.intValue());
            int gap = Math.max(item.getActualInventory() - item.getActualTurnoverInventory(), 0);
            item.setActualSafeInventory(Math.min(gap, safeInv.intValue()));
            item.setRedundantInventory(Math.max(item.getActualInventory() - item.getActualSafeInventory() - item.getActualTurnoverInventory(), 0));
            data.add(item);
        }
//        List<String> purchaseInstanceType = inventoryHealthDictService.getPurchaseInstanceType();
//        if (ListUtils.isNotEmpty(data)) {
//            data = data.stream().filter(o -> purchaseInstanceType.contains(o.getInstanceType())).collect(Collectors.toList());
//        }
        return data;
    }

    public DownloadBean exportInventoryDisassembleTotal(InventoryDisassembleReq req) {
        InventoryDisassembleResp resp = queryInventoryDisassembleReport(req);
        List<Item> data = resp.getData();
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/inventory_disassemble_total.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "库存拆解数据明细" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public List<InventoryDisassembleOutData> queryInventoryDisassembleOut(InventoryDisassembleOutReq req) {
        InventoryDisassembleReq finalReq = new InventoryDisassembleReq();
        finalReq.setStart(req.getStatDate());
        finalReq.setEnd(req.getStatDate());
        String ins = redisHelper.getString("DisassembleInsCategory");
        String turnover = redisHelper.getString("DisassembleTurnoverType");
        List<String> turnoverType = new ArrayList<>();
        List<String> instanceCategory = new ArrayList<>();
        if (!StringUtils.isBlank(ins) && !ins.equals("(空值)")) {
            String[] split = ins.split("@");
            instanceCategory.addAll(Arrays.asList(split));
        }
        if (!StringUtils.isBlank(turnover) && !turnover.equals("(空值)")) {
            String[] split = turnover.split("@");
            turnoverType.addAll(Arrays.asList(split));
        }
        finalReq.setTurnoverType(turnoverType);
        finalReq.setInstanceCategory(instanceCategory);
        InventoryDisassembleResp resp = queryInventoryDisassembleReport(finalReq);
        List<Item> data = resp.getData();
        String type = req.getType();
        if (StringUtils.isBlank(type)) {
            type = "zoneName";
        }
        List<InventoryDisassembleOutData> result = new ArrayList<>();
        String finalType = type;
        Map<String, List<Item>> mapList = ListUtils.toMapList(data, o -> genKeyByZoneDimension(finalType, o), o -> o);
        for (Entry<String, List<Item>> entry : mapList.entrySet()) {
            List<Item> value = entry.getValue();
            InventoryDisassembleOutData outData = new InventoryDisassembleOutData();
            outData.setStatTime(req.getStatDate());
            outData.setInstanceType(value.get(0).getInstanceType());
            outData.setInstanceCategory(value.get(0).getInstanceCategory());
            switch (type) {
                case "zoneName":
                    outData.setZoneDimension(value.get(0).getZoneName());
                    break;
                case "regionName":
                    outData.setZoneDimension(value.get(0).getRegionName());
                    break;
                case "areaName":
                    outData.setZoneDimension(value.get(0).getAreaName());
                    break;
                case "customhouseTitle":
                    outData.setZoneDimension(value.get(0).getCustomhouseTitle());
                    break;
            }
            outData.setActualInventory(NumberUtils.sum(value, Item::getActualInventory).intValue());
            outData.setActualTurnoverInventory(NumberUtils.sum(value, Item::getActualTurnoverInventory).intValue());
            outData.setPreDeductInventory(NumberUtils.sum(value, Item::getPreDeductInventory).intValue());
            outData.setNotPreDeductTurnoverInventory(NumberUtils.sum(value, Item::getNotPreDeductTurnoverInventory).intValue());
            outData.setSafeInventory(NumberUtils.sum(value, Item::getSafeInventory).intValue());
            int gap = Math.max(outData.getActualInventory() - outData.getActualTurnoverInventory(), 0);
            outData.setActualSafeInventory(Math.min(gap, outData.getSafeInventory()));
            outData.setRedundantInventory(outData.getActualInventory() - outData.getActualTurnoverInventory() - outData.getActualSafeInventory());
            result.add(outData);
        }
        return result;
    }

    public String genKeyByZoneDimension(String type, Item data) {
        String key = null;
        switch (type) {
            case "zoneName":
                key = String.join("@", data.getInstanceType(), data.getZoneName());
                break;
            case "regionName":
                key = String.join("@", data.getInstanceType(), data.getRegionName());
                break;
            case "areaName":
                key = String.join("@", data.getInstanceType(), data.getAreaName());
                break;
            case "customhouseTitle":
                key = String.join("@", data.getInstanceType(), data.getCustomhouseTitle());
                break;
        }
        return key;
    }

    @Override
    public InventoryDisassembleTodayResp queryInventoryDisassembleToday(InventoryBasicReq req) {
        InventoryDisassembleReq tempReq = new InventoryDisassembleReq();
        BeanUtils.copyProperties(req, tempReq);
        tempReq.setStart(req.getEnd());
        InventoryDisassembleResp tempResp = queryInventoryDisassembleReport(tempReq);
        List<Item> data = tempResp.getData();
        InventoryDisassembleTodayResp resp = new InventoryDisassembleTodayResp();
        //先按照实例类型+境内外聚合
        Map<String, List<Item>> mapList = ListUtils.toMapList(data, o -> String.join("@", o.getInstanceType(), o.getCustomhouseTitle()), o -> o);
        int actualInventorySum = 0;
        int turnoverInventorySum = 0;
        int safeInventorySum = 0;
        int redundantInventorySum = 0;
        for (Entry<String, List<Item>> entry : mapList.entrySet()) {
            List<Item> value = entry.getValue();
            int actualInventory = NumberUtils.sum(value, Item::getActualInventory).intValue();
            int turnoverInventory = NumberUtils.sum(value, Item::getActualTurnoverInventory).intValue();
            int originSafe = NumberUtils.sum(value, Item::getSafeInventory).intValue();
            int safeInventory = Math.min(originSafe, Math.max(actualInventory - turnoverInventory, 0));
            int redundantInventory = actualInventory - turnoverInventory - safeInventory;
            actualInventorySum += actualInventory;
            turnoverInventorySum += turnoverInventory;
            safeInventorySum += safeInventory;
            redundantInventorySum += redundantInventory;

        }
        resp.setSafeInventory(safeInventorySum);
        resp.setTurnoverInventory(turnoverInventorySum);
        resp.setActualInventory(actualInventorySum);
        resp.setRedundantInventory(redundantInventorySum);
        return resp;
    }

    public Map<String,ActualInventoryDO> getActualInventoryData(WhereSQL condition, InventoryDisassembleReq req) {
        String field = "select stat_time, zone_name, instance_type, region_name, area_name, customhouse_title, sum(actual_inv) as sum";
        String groupBy = "stat_time, zone_name, instance_type, region_name, area_name, customhouse_title";
        WhereSQL actCondition = condition.copy();
        actCondition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        actCondition.and("material_type <> '呆料'");
        actCondition.addGroupBy(groupBy);
        String sql = field + " from dws_actual_inventory_df" + actCondition.getSQL();
        List<ActualInventoryDO> all = ckcldDBHelper.getRaw(ActualInventoryDO.class, sql,
                actCondition.getParams());
        //针对机型族进行筛选
        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
             all = all.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(Collectors.toList());
        }
        //将实际库存数据按照时间+可用区+实例类型来聚合
        return ListUtils.toMap(all,
                o -> String.join("@", o.getStatTime(), o.getZoneName(), o.getInstanceType()), o -> o);
    }

    public Map<String, TurnoverInventoryData> getTurnoverData(WhereSQL condition, InventoryDisassembleReq req) {
        //1.获取未预扣周转库存
        WhereSQL notPreCondition = condition.copy();
        notPreCondition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        String noPreField = "select stat_time,zone_name,instance_type,not_pre_deduct_turnover_cores";
        String noPreSql = noPreField + " from dws_new_turnover_inventory_data_df" + notPreCondition.getSQL();
        List<NotPreDeductInventoryDO> notPreData = ckcldDBHelper.getRaw(NotPreDeductInventoryDO.class, noPreSql,
                notPreCondition.getParams());
        //2.获取库存维度的预扣库存
        String preField = "select stat_time, zone_name, instance_type, region_name, area_name, customhouse_title, sum(actual_inv) as sum";
        String preGroupBy = "stat_time, zone_name, instance_type, region_name, area_name, customhouse_title";
        WhereSQL preCondition = condition.copy();
        preCondition.addGroupBy(preGroupBy);
        preCondition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        //库存细类：客户预扣同时剔除呆料
        preCondition.and("inv_detail_type = '用户预扣'");
        preCondition.and("material_type <> '呆料'");
        String preSql = preField + " from dws_actual_inventory_df" + preCondition.getSQL();
        List<ActualInventoryDO> preData = ckcldDBHelper.getRaw(ActualInventoryDO.class, preSql,
                preCondition.getParams());

        //3.获取逻辑区&标签预扣核数
//        WhereSQL logicCondition = condition.copy();
//        logicCondition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
//        logicCondition.and("biz_type = 'CVM'");
//        String logicField = "select stat_time, zone_name, instance_type, region_name, area_name, customhouse_title, sum(cpu_core_total) as sum";
//        String logicGroupBy = "stat_time, zone_name, instance_type, region_name, area_name, customhouse_title";
//        logicCondition.addGroupBy(logicGroupBy);
//        String logicSql = logicField + " from dws_logical_tags_withhold_df" + logicCondition.getSQL();
//        List<ActualInventoryDO> logicData = ckstdcrpDBHelper.getRaw(ActualInventoryDO.class, logicSql, logicCondition.getParams());
        //3.组合处理
        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            notPreData = notPreData.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(
                    Collectors.toList());
            preData = preData.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType())))
                    .collect(Collectors.toList());
//            logicData = logicData.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType())))
//                    .collect(Collectors.toList());
        }
        Map<String, NotPreDeductInventoryDO> notPreMap = ListUtils.toMap(notPreData,
                o -> String.join("@", o.getStatTime(), o.getZoneName(), o.getInstanceType()), o -> o);
        Map<String, ActualInventoryDO> preMap = ListUtils.toMap(preData,
                o -> String.join("@", o.getStatTime(), o.getZoneName(), o.getInstanceType()), o -> o);
//        Map<String, ActualInventoryDO> logicMap = ListUtils.toMap(logicData,
//                o -> String.join("@", o.getStatTime(), o.getZoneName(), o.getInstanceType()), o -> o);
        Set<String> keySet = new HashSet<>();
        Map<String, TurnoverInventoryData> result = new HashMap<>();
        if (ListUtils.isEmpty(req.getTurnoverType()) || req.getTurnoverType().size() == 2) {
            if (ListUtils.isNotEmpty(notPreMap.keySet())) {
                keySet.addAll(notPreMap.keySet());
            }
            if (ListUtils.isNotEmpty(preMap.keySet())) {
                keySet.addAll(preMap.keySet());
            }
//            if (ListUtils.isNotEmpty(logicMap.keySet())) {
//                keySet.addAll(logicMap.keySet());
//            }
            for (String key : keySet) {
                TurnoverInventoryData item = new TurnoverInventoryData();
                NotPreDeductInventoryDO notPre = notPreMap.get(key);
                ActualInventoryDO pre = preMap.get(key);
//                ActualInventoryDO logic = logicMap.get(key);
                if (notPre != null) {
                    item.setStatTime(notPre.getStatTime());
                    item.setZoneName(notPre.getZoneName());
                    item.setInstanceType(notPre.getInstanceType());
                } else if (pre != null) {
                    item.setStatTime(pre.getStatTime());
                    item.setZoneName(pre.getZoneName());
                    item.setInstanceType(pre.getInstanceType());
                }
//                Integer preDeduct = pre == null ? 0 : pre.getActualInv();
//                Integer logicDeduct = logic == null ? 0 : logic.getActualInv();
                item.setPreDeductInventory(pre == null ? 0 : pre.getActualInv());
                item.setNotPreDeductTurnoverCores(notPre == null ? 0 : notPre.getNotPreDeductInventory());
                item.setTurnoverInventory(item.getPreDeductInventory() + item.getNotPreDeductTurnoverCores());
                result.put(key, item);
            }
        }else if (req.getTurnoverType().size() == 1 && req.getTurnoverType().contains("用户预扣")) {
            if (ListUtils.isNotEmpty(preMap.keySet())) {
                keySet.addAll(preMap.keySet());
            }
//            if (ListUtils.isNotEmpty(logicMap.keySet())) {
//                keySet.addAll(logicMap.keySet());
//            }
            for (String key : keySet) {
                TurnoverInventoryData item = new TurnoverInventoryData();
                ActualInventoryDO pre = preMap.get(key);
//                ActualInventoryDO logic = logicMap.get(key);
                if (pre != null) {
                    item.setStatTime(pre.getStatTime());
                    item.setZoneName(pre.getZoneName());
                    item.setInstanceType(pre.getInstanceType());
                }
//                Integer preDeduct = pre == null ? 0 : pre.getActualInv();
//                Integer logicDeduct = logic == null ? 0 : logic.getActualInv();
                item.setPreDeductInventory(pre == null ? 0 : pre.getActualInv());
                item.setNotPreDeductTurnoverCores(0);
                item.setTurnoverInventory(item.getPreDeductInventory() + item.getNotPreDeductTurnoverCores());
                result.put(key, item);
            }
        }else if (req.getTurnoverType().size() == 1 && req.getTurnoverType().contains("未预扣周转")) {
            for (Entry<String, NotPreDeductInventoryDO> entry : notPreMap.entrySet()) {
                TurnoverInventoryData item = new TurnoverInventoryData();
                item.setStatTime(entry.getValue().getStatTime());
                item.setZoneName(entry.getValue().getZoneName());
                item.setInstanceType(entry.getValue().getInstanceType());
                item.setNotPreDeductTurnoverCores(entry.getValue().getNotPreDeductInventory());
                item.setPreDeductInventory(0);
                item.setTurnoverInventory(item.getPreDeductInventory() + item.getNotPreDeductTurnoverCores());
                result.put(entry.getKey(), item);
            }
        }
        return result;
    }

    public Map<String, BigDecimal> getTargetSafeInventoryData(WhereSQL condition, InventoryDisassembleReq req) {
        //1.包月安全库存
        String operationViewAlgorithm = redisHelper.getString("operationViewAlgorithm");
        String algorithm = InventoryHealthAlgorithm.getNameFromCode(operationViewAlgorithm);
        WhereSQL safeCondition = condition.copy();
        safeCondition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        safeCondition.and("algorithm = ?", algorithm);
        safeCondition.and("product_type = 'CVM'");
        safeCondition.and("customer_custom_group = 'ALL'");
        String safeField = "stat_time, zone_name, instance_type, region_name, area_name, customhouse_title, monthly_safety_inv";
        String safeSql = "select " + safeField + " from dws_safe_inventory_history_monthly_df" + safeCondition.getSQL();
        List<SafeInventoryDO> safeInvs = ckcldDBHelper.getRaw(SafeInventoryDO.class, safeSql, safeCondition.getParams());
        //2.弹性配额
        Set<String> mondays = new HashSet<>();
        LocalDate start = DateUtils.parseLocalDate(req.getStart());
        LocalDate end = DateUtils.parseLocalDate(req.getEnd());
        LocalDate cur = start;
        while(!cur.isAfter(end)) {
            String curWeekMonday = DateUtils.formatDate(cur.with(DayOfWeek.MONDAY));
            mondays.add(curWeekMonday);
            cur = cur.plusDays(1);
        }

        WhereSQL bufferCondition = condition.copy();
        bufferCondition.and("stat_time in (?)", new ArrayList<>(mondays));
        bufferCondition.and("product_type = 'CVM'");
        List<DwsBufferSafeInventoryDfDO> bufferInvs = ckcldDBHelper.getAll(DwsBufferSafeInventoryDfDO.class,
                bufferCondition.getSQL(), bufferCondition.getParams()).stream().map(item -> {
            // 确保弹性备货 > 0
            item.setMckBufferSafetyInv(NumberUtils.max(item.getMckBufferSafetyInv(), BigDecimal.ZERO));
            item.setBufferSafetyInv(NumberUtils.max(item.getBufferSafetyInv(), BigDecimal.ZERO));
            return item;
        }).collect(Collectors.toList());
        //3.人工调整
        WhereSQL manualCondition = condition.copy();
        manualCondition.and("stat_time between ? and ?", req.getStart(), req.getEnd());
        List<InventoryHealthManualConfigSnapshotDO> manualList =
                demandDBHelper.getAll(
                        InventoryHealthManualConfigSnapshotDO.class, manualCondition.getSQL(), manualCondition.getParams());
        //4.处理并合并
        if (ListUtils.isNotEmpty(req.getGinsFamily())) {
            List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
            Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);
            Set<String> ginSet = new HashSet<>(req.getGinsFamily());
            safeInvs = safeInvs.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType()))).collect(
                    Collectors.toList());
            bufferInvs = bufferInvs.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType())))
                    .collect(Collectors.toList());
            manualList = manualList.stream().filter(o -> ginSet.contains(ginMap.get(o.getInstanceType())))
                    .collect(Collectors.toList());
        }
        Map<String, List<SafeInventoryDO>> safeMapList = ListUtils.toMapList(safeInvs,
                o -> String.join("@", o.getStatTime(), o.getZoneName(), o.getInstanceType()), o -> o);
        Map<String, InventoryHealthManualConfigSnapshotDO> manualMapList = ListUtils.toMap(manualList,
                o -> String.join("@", DateUtils.formatDate(o.getStatTime()), o.getZoneName(), o.getInstanceType()),
                o -> o);
        Map<String, List<DwsBufferSafeInventoryDfDO>> tempMap = ListUtils.toMapList(bufferInvs,
                DwsBufferSafeInventoryDfDO::getStatTime, o -> o);
        Set<String> dateKey = tempMap.keySet();
        List<DwsBufferSafeInventoryDfDO> temp = new ArrayList<>();
        while(!start.isAfter(end)) {
            LocalDate monday = start.with(DayOfWeek.MONDAY);
            if (dateKey.contains(DateUtils.formatDate(monday))) {
                List<DwsBufferSafeInventoryDfDO> list = tempMap.get(DateUtils.formatDate(monday));
                for (DwsBufferSafeInventoryDfDO item : list) {
                    DwsBufferSafeInventoryDfDO buffer = new DwsBufferSafeInventoryDfDO();
                    BeanUtils.copyProperties(item, buffer);
                    buffer.setStatTime(DateUtils.formatDate(start));
                    temp.add(buffer);
                }
            }
            start = start.plusDays(1);
        }
        Map<String, List<DwsBufferSafeInventoryDfDO>> bufferMapList = ListUtils.toMapList(temp,
                o -> String.join("@", o.getStatTime(), o.getZoneName(), o.getInstanceType()), o -> o);

        Set<String> keySet = new HashSet<>();
        if (ListUtils.isNotEmpty(safeMapList.keySet())) {
            keySet.addAll(safeMapList.keySet());
        }
        if (ListUtils.isNotEmpty(bufferMapList.keySet())) {
            keySet.addAll(bufferMapList.keySet());
        }
        if (ListUtils.isNotEmpty(manualMapList.keySet())) {
            keySet.addAll(manualMapList.keySet());
        }
        Map<String, BigDecimal> result = new HashMap<>();
        for (String key : keySet) {
            List<SafeInventoryDO> safeInventoryDO = safeMapList.get(key);
            List<DwsBufferSafeInventoryDfDO> bufferDO = bufferMapList.get(key);
            InventoryHealthManualConfigSnapshotDO snapshotDO = manualMapList.get(key);
            BigDecimal safeInv = NumberUtils.sum(safeInventoryDO, SafeInventoryDO::getSafeInv);
            BigDecimal bufferInv = NumberUtils.sum(bufferDO, DwsBufferSafeInventoryDfDO::getFinalBufferSafetyInv);
            BigDecimal manual = BigDecimal.ZERO;
            if (snapshotDO != null) {
                if (snapshotDO.getNum() != null) {
                    manual = snapshotDO.getNum();
                }
            }
            BigDecimal add = safeInv.add(bufferInv).add(manual);
            result.put(key, add.max(BigDecimal.ZERO));
        }
        return result;
    }

    @Data
    public static class ActualInventoryDO {

        @Column("stat_time")
        private String statTime;

        @Column("zone_name")
        private String zoneName;

        @Column("instance_type")
        private String instanceType;

        @Column("customhouse_title")
        private String customhouseTitle;

        @Column("region_name")
        private String regionName;

        @Column("area_name")
        private String areaName;

        @Column("sum")
        private Integer actualInv;
    }

    @Data
    public static class SafeInventoryDO {

        @Column("stat_time")
        private String statTime;

        @Column("zone_name")
        private String zoneName;

        @Column("instance_type")
        private String InstanceType;

        @Column("customhouse_title")
        private String customhouseTitle;

        @Column("region_name")
        private String regionName;

        @Column("area_name")
        private String areaName;

        @Column("monthly_safety_inv")
        private BigDecimal safeInv;
    }

    @Data
    public static class NotPreDeductInventoryDO {

        @Column("stat_time")
        private String statTime;

        @Column("zone_name")
        private String zoneName;

        @Column("instance_type")
        private String instanceType;

        @Column("not_pre_deduct_turnover_cores")
        private Integer notPreDeductInventory;
    }

    @Data
    public static class TurnoverInventoryData {

        private String statTime;

        private String zoneName;

        private String instanceType;

        private Integer notPreDeductTurnoverCores;

        private Integer preDeductInventory;

        private Integer TurnoverInventory;
    }

}
