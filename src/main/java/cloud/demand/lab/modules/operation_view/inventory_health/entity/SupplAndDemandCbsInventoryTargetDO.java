package cloud.demand.lab.modules.operation_view.inventory_health.entity;


import lombok.Data;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("suppl_and_demand_cbs_inventory_target")
public class SupplAndDemandCbsInventoryTargetDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 版本日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 云盘类型<br/>Column: [volume_type] */
    @Column(value = "volume_type")
    private String volumeType;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 目标库存<br/>Column: [target_inventory] */
    @Column(value = "target_inventory")
    private Integer targetInventory;

    /** 备注<br/>Column: [remark] */
    @Column(value = "remark")
    private String remark;

}
