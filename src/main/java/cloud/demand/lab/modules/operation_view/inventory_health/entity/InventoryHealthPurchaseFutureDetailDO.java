package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("inventory_health_purchase_future_detail")
public class InventoryHealthPurchaseFutureDetailDO extends BaseDO {

    /** 统计时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 腾讯云境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 腾讯云地域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 腾讯云区域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 腾讯云可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 星云子单号<br/>Column: [sub_id] */
    @Column(value = "sub_id")
    private String subId;

    /** 规划产品<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 计算类型(CPU/GPU)<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

    /** 承诺交付日期<br/>Column: [promise_delivery_date] */
    @Column(value = "promise_delivery_date")
    private String promiseDeliveryDate;

    /** 承诺交付日期-节假年<br/>Column: [promise_delivery_holiday_year] */
    @Column(value = "promise_delivery_holiday_year")
    private Integer promiseDeliveryHolidayYear;

    /** 承诺交付日期-节假月<br/>Column: [promise_delivery_holiday_month] */
    @Column(value = "promise_delivery_holiday_month")
    private Integer promiseDeliveryHolidayMonth;

    /** 承诺交付日期-节假周<br/>Column: [promise_delivery_holiday_week] */
    @Column(value = "promise_delivery_holiday_week")
    private Integer promiseDeliveryHolidayWeek;

    /** 设备台数<br/>Column: [num] */
    @Column(value = "num")
    private Integer num;

    /** 核心数<br/>Column: [core_num] */
    @Column(value = "core_num")
    private Integer coreNum;

}
