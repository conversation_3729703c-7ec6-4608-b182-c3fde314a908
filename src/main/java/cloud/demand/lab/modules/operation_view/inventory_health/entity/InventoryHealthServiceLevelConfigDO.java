package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.lab.common.excel.core.annotation.DotExcelField;
import cloud.demand.lab.modules.operation_view.inventory_health.constants.InventoryHealthExcelGroup;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;
import org.nutz.lang.Strings;

@Data
@ToString
@Table("inventory_health_service_level_config")
@DotExcelEntity
public class InventoryHealthServiceLevelConfigDO extends BaseDO {
    @Column(value = "region_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL, excelColumnName = "境内外")
    private String regionType;

    @Column(value = "zone_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL, excelColumnName = "可用区分类")
    private String zoneType;

    @Column(value = "bill_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL, excelColumnName = "计费类型")
    private String billType;

    @Column(value = "instance_type")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL, excelColumnName = "机型类型")
    private String instanceType;

    @Column(value = "num")
    @DotExcelField(group = InventoryHealthExcelGroup.INVENTORY_HEALTH_EXCEL_GROUP_SERVICE_LEVEL, excelColumnName = "服务水平目标")
    private BigDecimal num;

    @Column(value = "date")
    private Date date;

    public String getKey(){
        return Strings.join("@", regionType, zoneType, billType, instanceType);
    }

    public static InventoryHealthServiceLevelConfigDO copy(InventoryHealthServiceLevelConfigDO other) {
        InventoryHealthServiceLevelConfigDO serviceLevelConfigDO = new InventoryHealthServiceLevelConfigDO();
        serviceLevelConfigDO.setRegionType(other.getRegionType());
        serviceLevelConfigDO.setZoneType(other.getZoneType());
        serviceLevelConfigDO.setBillType(other.getBillType());
        serviceLevelConfigDO.setInstanceType(other.getInstanceType());
        serviceLevelConfigDO.setNum(other.getNum());
        serviceLevelConfigDO.setDate(other.getDate());
        return serviceLevelConfigDO;
    }
}
