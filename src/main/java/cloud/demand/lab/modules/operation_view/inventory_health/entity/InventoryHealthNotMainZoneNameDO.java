package cloud.demand.lab.modules.operation_view.inventory_health.entity;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 库存健康-在售非主力园区(可用区)配置表
 */
@Data
@ToString
@Table("inventory_health_not_main_zone_name")
public class InventoryHealthNotMainZoneNameDO extends BaseDO {

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

}
