package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.modules.operation_view.inventory_health.dto.safety_inventory.OperationViewSoeReq;
import cloud.demand.lab.modules.operation_view.inventory_health.service.TestInventoryService;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsActualInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.lab.modules.operation_view.operation_view.model.ActualInventoryListReq;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewReq2;
import cloud.demand.lab.modules.operation_view.operation_view.model.OperationViewResp2;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.operation_view_old.model.OperationViewReq;
import com.alibaba.fastjson.JSON;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class TestInventoryServiceImpl implements TestInventoryService {
    @Resource
    private OperationViewService2Impl operationViewService2;
    @Override
    public void getActualAndSafeInv(OperationViewSoeReq req, String operationViewAlgorithm) {
        Long start = System.currentTimeMillis();
        log.info("getActualAndSafeInv方法的req为: " + JSON.toJSON(req) + "| algorithm为: " + operationViewAlgorithm);
        WhereSQL condition = getCondition(req);
        ActualInventoryListReq params = new ActualInventoryListReq();
        String statTime = DateUtils.format(req.getStatTime());
        params.setStatTime(statTime);
        // 只看线上库存
        params.setLineType(req.getLineType());
        params.setMaterialType(req.getMaterialType());
        params.setInvDetailType(req.getInvDetailType());
        // 不组合机型
        params.setIsCombine(false);
        params.setZoneCategory(req.getZoneCategory());
        params.setInstanceTypeCategory(req.getInstanceTypeCategory());
        // null取statTime
        params.setCategoryDate(null);
        // 获取实际库存和弹性备货配额，复用运营视图的逻辑
        List<DwsActualInventoryDfDO> all = operationViewService2.getActualInventoryList(params, condition);
        List<DwsBufferSafeInventoryDfDO> bufferSafeInventoryDfDOS = operationViewService2.getBufferSafeInventoryList(params, condition);
        // 合并实际库存和弹性库存的结果，并汇总到总的结果中
        List<OperationViewResp2.Item> mergedItems = operationViewService2.mergeActualBufferInventoryData(statTime, all, bufferSafeInventoryDfDOS);
        // 5、构造实际库存接口返回
        OperationViewResp2 operationViewResp2 = constructResp(mergedItems);
        OperationViewReq2 operationViewReq2 = invReqToOpViewReq(req, params);
        // 获取并填充安全库存，日切片取选中的日期
        // 默认历史周峰
        if (operationViewAlgorithm == null) {
            operationViewAlgorithm = "historyWeekPeak";
        }
        Function<OperationViewResp2.Item, OperationViewResp2.SafetyInventoryResult> getter;
        switch (operationViewAlgorithm) {
            case "historyWeekPeak":
                operationViewService2.buildHistoryWeekPeak(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getHistoryWeekPeak;
                break;
            case "historyWeekDiff":
                operationViewService2.buildHistoryWeekDiff(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getHistoryWeekDiff;
                break;
            case "futureWeekPeak":
                operationViewService2.buildFutureWeekPeak(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getFutureWeekPeak;
                break;
            case "historyWeekPeakForecastWN":
                operationViewService2.buildHistoryWeekPeakDemand(operationViewReq2, operationViewResp2);
                getter = OperationViewResp2.Item::getHistoryWeekPeakForecastWN;
                break;
            default:
                throw BizException.makeThrow("该安全算法不存在：" + operationViewAlgorithm);
        }
    }

    private WhereSQL getCondition(OperationViewSoeReq req) {
        WhereSQL condition = new WhereSQL();
        if (ListUtils.isNotEmpty(req.getInstanceType())) {
            condition.and("instance_type in (?)", req.getInstanceType());
        }
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            condition.and("customhouse_title in (?)", req.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(req.getAreaName())) {
            condition.and("area_name in (?)", req.getAreaName());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            condition.and("region_name in (?)", req.getRegionName());
        }
        if (ListUtils.isNotEmpty(req.getZoneName())) {
            condition.and("zone_name in (?)", req.getZoneName());
        }
        return condition;
    }

    private OperationViewResp2 constructResp(List<OperationViewResp2.Item> mergedItems) {
        OperationViewResp2 opResp = new OperationViewResp2();
        opResp.setData(mergedItems.stream().map(item -> {
            // 安全库存信息此时还没生成，只转换实际存库数据
            OperationViewResp2.Item opItem = new OperationViewResp2.Item();
            opItem.setProductType("CVM"); // 固定 CVM
            opItem.setCustomhouseTitle(item.getCustomhouseTitle());
            opItem.setAreaName(item.getAreaName());
            opItem.setRegionName(item.getRegionName());
            opItem.setZoneName(item.getZoneName());
            opItem.setInstanceType(item.getInstanceType());
            //  总库存
            opItem.setInvTotalNum(BigDecimal.valueOf(item.getInvTotalNum().intValue()));
            //  弹性服务水平/系数
            opItem.setBufferServiceLevel(item.getBufferServiceLevel());
            opItem.setBufferServiceLevelFactor(item.getBufferServiceLevelFactor());
            //  弹性规模日均值
            opItem.setBufferAverageCore(item.getBufferAverageCore());
            //  弹性备货配额-三种算法目前结果完全相同，因此先抽取到这里
            opItem.setBufferSafetyInv(item.getBufferSafetyInv());
            return opItem;
        }).collect(Collectors.toList()));

        return opResp;
    }

    private OperationViewReq2 invReqToOpViewReq(OperationViewSoeReq req, ActualInventoryListReq tempReq) {
        // 构造运营视图参数，复用运营视图逻辑
        OperationViewReq2 opReq = new OperationViewReq2();
        opReq.setZoneCategory(req.getZoneCategory());
        opReq.setInstanceTypeCategory(req.getInstanceTypeCategory());
        opReq.setInstanceType(req.getInstanceType());
        opReq.setCustomhouseTitle(req.getCustomhouseTitle());
        opReq.setAreaName(req.getAreaName());
        opReq.setRegionName(req.getRegionName());
        opReq.setZoneName(req.getZoneName());
        // 默认不组合机型
        opReq.setIsCombine(tempReq.getIsCombine());
        opReq.setMaterialType(req.getMaterialType());
        // 不设置客户组
        opReq.setCustomerCustomGroup(null);
        opReq.setLineType(tempReq.getLineType());
        opReq.setDate(DateUtils.parse(tempReq.getStatTime()));
        opReq.setCategoryDate(tempReq.getCategoryDate());
        return opReq;
    }

}
