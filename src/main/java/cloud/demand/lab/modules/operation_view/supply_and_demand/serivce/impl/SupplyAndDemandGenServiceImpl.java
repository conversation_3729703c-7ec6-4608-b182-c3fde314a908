package cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.impl;

import cloud.demand.lab.common.utils.CkDBUtils;
import cloud.demand.lab.common.utils.ORMUtils;
import cloud.demand.lab.modules.common_dict.DO.SoeRegionNameCountryDO;
import cloud.demand.lab.modules.common_dict.DO.StaticZoneDO;
import cloud.demand.lab.modules.common_dict.service.CvmPlanService;
import cloud.demand.lab.modules.common_dict.service.DictService;
import cloud.demand.lab.modules.operation_view.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.lab.modules.operation_view.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.lab.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.lab.modules.operation_view.operation_view_old.entity.CvmType;
import cloud.demand.lab.modules.operation_view.operation_view_old.enums.Constant;
import cloud.demand.lab.modules.operation_view.operation_view_old.service.OutsideViewOldService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.AdjustSupplyPosConfigDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.AdsCrpPplJoinOrderVersionNewestDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.AdsInventoryHealthSupplySummaryDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasDeviceMemoryDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.BasProductDeviceLogicCapacityDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.DwdDbSaleScaleDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.DwsCrpPplJoinOrderVersionNewestCfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.PplAndOrderAdjustVersionDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataAdjustDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.ProductDemandDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplAndDemandInventoryTargetDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplAndDemandInventoryTargetDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.entity.SupplyOnTheWayDataDfDO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.DemandTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.ProductCategoryEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.enums.VolumeTypeEnum;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.DemandMarketDTO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SyncCbsDemandDataVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SyncCvmDemandDataVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SyncDbDemandDataVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.model.vo.SyncGpuDemandDataVO;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandDictService;
import cloud.demand.lab.modules.operation_view.supply_and_demand.serivce.SupplyAndDemandGenService;
import cloud.demand.lab.modules.operation_view.util.SoeCommonUtils;
import cloud.demand.lab.modules.order.service.filler.core.FillerService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SupplyAndDemandGenServiceImpl implements SupplyAndDemandGenService {

    @Resource
    DBHelper ckcubesDBHelper;

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    private DBHelper ckstdcrpDBHelper;

    @Resource
    OutsideViewOldService outsideViewOldService;

    @Resource
    DictService dictService;

    @Resource
    DBHelper yuntiDBHelper;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper planDbDBHelper;

    @Resource
    private DBHelper plancbsDBHelper;

    @Resource
    CvmPlanService cvmPlanService;

    @Resource
    private FillerService fillerService;

    @Resource
    private SupplyAndDemandDictService supplyAndDemandDictService;

    @Resource
    private DBHelper yuntidemandDBHelper;

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void genSupplyOnTheWayData(String statTime) {

        List<CloudDemandCsigResourceViewCategoryDO> categoryAll = yuntiDBHelper.getAll(
                CloudDemandCsigResourceViewCategoryDO.class);
        List<String> collect = categoryAll.stream().map(CloudDemandCsigResourceViewCategoryDO::getPlanProductName).distinct()
                .collect(Collectors.toList());
        Map<String, String> planToCategory5Map = ListUtils.toMap(categoryAll, CloudDemandCsigResourceViewCategoryDO::getPlanProductName,
                CloudDemandCsigResourceViewCategoryDO::getCategory5);

        Map<String, String> planToCategory3Map = ListUtils.toMap(categoryAll, CloudDemandCsigResourceViewCategoryDO::getPlanProductName,
                CloudDemandCsigResourceViewCategoryDO::getCategory3);

        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/get_all_supply_on_the_way_data.sql");
        String date = LocalDate.parse(statTime).plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        List<DemandMarketDTO> marketData = ckcubesDBHelper.getRaw(DemandMarketDTO.class, sql, date, collect);
        List<SupplyOnTheWayDataDfDO> result = new ArrayList<>();

        //获取物理机与实例类型的映射
        List<CvmType> allCvmType = outsideViewOldService.getAllCvmType();
        Map<String, String> deviceType = ListUtils.toMap(allCvmType, CvmType::getDeviceType, CvmType::getInstanceType);

        //获取campus与可用区的映射
        Map<String, StaticZoneDO> campus2ZoneInfoMap = dictService.getCampus2ZoneInfoMap();

        //获取campus与可用区的映射
        Map<String, String> deviceFamilyMap = dictService.getDeviceFamilyMap();

        //获取国家与可用区的映射
        Map<String, String> zoneName2RegionName = dictService.getZoneName2RegionName();
        Map<String, SoeRegionNameCountryDO> regionNameInfoMap = cvmPlanService.getRegionNameInfoMap();
        Map<String, String> ret = new HashMap<>();
        for (Entry<String, String> entry : zoneName2RegionName.entrySet()) {
            String zone = entry.getKey();
            String region = entry.getValue();
            SoeRegionNameCountryDO regionDO = regionNameInfoMap.get(region);
            if (regionDO != null) {
                ret.put(zone, regionDO.getCountryName());
            }
        }
        //获取机型族的映射
        Map<String, String> ginMap = ListUtils.toMap(allCvmType, CvmType::getInstanceType, CvmType::getGinsFamily);

        //获取CBS逻辑容量
        List<BasProductDeviceLogicCapacityDO> capacityList = yuntidemandDBHelper.getAll(BasProductDeviceLogicCapacityDO.class, "where plan_product_name = 'CBS'");
        Map<String, BigDecimal> capacityMap = ListUtils.toMap(capacityList,
                item -> StringUtils.joinWith("@", item.getPlanProductName(), item.getDeviceType()), BasProductDeviceLogicCapacityDO::getLogicCapacity);

        //获取数据库内存容量
        Map<String, BigDecimal> memoryMap = demandDBHelper.getAll(BasDeviceMemoryDO.class).stream().collect(Collectors.toMap(item -> StringUtils.joinWith("@", item.getPlanProductName(), item.getDeviceType()), BasDeviceMemoryDO::getTbSaleMemory, (k1, k2) -> k1));

        for (DemandMarketDTO market : marketData) {
            SupplyOnTheWayDataDfDO item = SupplyOnTheWayDataDfDO.genSupplyDataByMarket(market,
                    statTime);
            item.setProductType(planToCategory5Map.get(market.getQuotaPlanProductName()));
            item.setProduct(planToCategory3Map.get(market.getQuotaPlanProductName()));
            //容量
            String key = StringUtils.joinWith("@", item.getProduct(), item.getQuotaDeviceClass());
            BigDecimal capacity = capacityMap.getOrDefault(key, BigDecimal.ZERO);
            item.setLogicCapacity(SoeCommonUtils.multiply(capacity, item.getCpuNum()));
            String volumeName = VolumeTypeEnum.getNameByDeviceFamilyName(deviceFamilyMap.getOrDefault(item.getQuotaDeviceClass(), Constant.EMPTY_VALUE_STR));
            if (StringUtils.isEmpty(volumeName)) {
                item.setVolumeType(Constant.EMPTY_VALUE_STR);
            } else {
                item.setVolumeType(volumeName);
            }

            //内存
            BigDecimal memory = memoryMap.getOrDefault(key, BigDecimal.ZERO);
            //总内存=内存*台数
            item.setMemory(SoeCommonUtils.multiply(memory, item.getCpuNum()));
            //填充实例类型
            String instance = deviceType.get(item.getQuotaDeviceClass());
            item.setInstanceType(instance == null ? "(空值)" : instance);

            //填充机型族
            String gin = ginMap.get(item.getInstanceType());
            item.setGinFamily(StringUtils.isBlank(gin) ? "(空值)" : gin);
            //填充地域信息
            StaticZoneDO staticZoneDO = campus2ZoneInfoMap.get(item.getCampusName());
            if (staticZoneDO != null) {
                item.setZoneName(staticZoneDO.getZoneName());
                item.setRegionName(staticZoneDO.getRegionName());
                item.setAreaName(staticZoneDO.getAreaName());
                item.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
            } else {
                item.setZoneName("(空值)");
                item.setRegionName("(空值)");
                item.setAreaName("(空值)");
                item.setCustomhouseTitle("(空值)");
            }
            String country = ret.get(item.getZoneName());
            item.setCountry(StringUtils.isNotBlank(country) ? country : "(空值)");

            if (item.getDeliveryStatus().equals("在途")) {
                LocalDate localDate = item.getSlaDateExpect().equals("0000-00-00") ?
                        null : DateUtils.parseLocalDate(item.getSlaDateExpect());
                LocalDate firstDayOfCurrentMonth = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
                if (StringUtils.equals(item.getProduceStatus(), "完成") && StringUtils.equals(item.getIsDelivery(), "erp已交付")) {
                    //（生产状态：已完成，交付状态：erp已交付）：默认为当前月（ERP已交付代表随时可交）
                    item.setSlaDateMonth(YearMonth.of(LocalDate.now().getYear(), LocalDate.now().getMonthValue()).toString());
                } else if (localDate == null || item.getSlaDateExpect().equals("1900-01-01") || localDate.isBefore(firstDayOfCurrentMonth)) {
                    //（生产状态：剔除已完成、作废）：根据预计交付日期关联月份（异常数据1900&0000&历史日期未交付，统一刷成未知货期）
                    item.setSlaDateMonth("未知");
                } else {
                    item.setSlaDateMonth(YearMonth.of(localDate.getYear(), localDate.getMonthValue()).toString());
                }
            } else {
                //根据cloud交付时间关联交付月份
                LocalDate localDate = DateUtils.parseLocalDate(item.getCloudDeliveryTime());
                item.setSlaDateMonth(YearMonth.of(localDate.getYear(), localDate.getMonthValue()).toString());

            }
            result.add(item);
        }
        fillerService.fill(result);
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.supply_on_the_way_data_df_local ON CLUSTER default_cluster DROP PARTITION ?", statTime);

        if (ListUtils.isNotEmpty(result)) {
            ckcldDBHelper.insertBatchWithoutReturnId(result);
        }
        List<String> invalidSlaYearMonth = ListUtils.newArrayList("1900-01", "未知");
        String endYearMonth = result.stream().map(SupplyOnTheWayDataDfDO::getSlaDateMonth)
                .filter(item -> !invalidSlaYearMonth.contains(item)).distinct().max(String::compareTo).get();
        String startYearMonth = result.stream().map(SupplyOnTheWayDataDfDO::getSlaDateMonth)
                .filter(item -> !invalidSlaYearMonth.contains(item)).distinct().min(String::compareTo).get();

        List<String> categoryList = categoryAll.stream().map(item -> item.getCategory5()).distinct().collect(Collectors.toList());
        for (String category : categoryList) {
            supplyAndDemandDictService.insert(statTime, startYearMonth, endYearMonth, "supply", category);
        }
    }

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void synchronizePositionConfig(String statTime, String productType) {

        LocalDate localDate = DateUtils.parseLocalDate(statTime);
        //获取前一天的机位配置
        LocalDate yesterday = localDate.plusDays(-1);
        List<AdjustSupplyPosConfigDO> all = demandDBHelper.getAll(AdjustSupplyPosConfigDO.class,
                "where version_date = ? and product_type = ? and valid_status = 'VALID'",
                DateUtils.formatDate(yesterday), productType);
        all.forEach(o -> o.setVersionDate(statTime));
        demandDBHelper.insert(all);
    }


    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void syncDemandData(LocalDate statTime, List<String> productCategoryList) {
        List<ProductDemandDataDfDO> retList = ListUtils.newArrayList();
        for (String productCategory : productCategoryList) {
            if (StringUtils.equals(productCategory, ProductCategoryEnum.CVM.getName())) {
                //CVM
                retList.addAll(getCvmDemandData(statTime));
            }
            if (StringUtils.equals(productCategory, ProductCategoryEnum.CBS.getName())) {
                //CBS
                retList.addAll(getCbsDemandData(statTime));
            }
            if (StringUtils.equals(productCategory, ProductCategoryEnum.DB.getName())) {
                //数据库
                retList.addAll(getDbDemandData(statTime));
            }
            if (StringUtils.equals(productCategory, ProductCategoryEnum.GPU.getName())) {
                //GPU
                retList.addAll(getGpuDemandData(statTime));
            }
        }

        Map<String, String> regionMap = dictService.getRegionNameMap();
        retList.forEach(o -> o.setRegion(regionMap.getOrDefault(o.getRegionName(), Constant.EMPTY_VALUE_STR)));

        List<ProductDemandDataDfDO> existingData = ckstdcrpDBHelper.getAll(ProductDemandDataDfDO.class, " where stat_time = ? ",
                statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));
        existingData.removeIf(item -> productCategoryList.contains(item.getProductCategory()));

        retList.addAll(existingData);
        // 删除分区
        CkDBUtils.delete(ckstdcrpDBHelper, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), ProductDemandDataDfDO.class);

        // 添加
        log.info(" size :" + retList.size());
        CkDBUtils.saveBatch(ckstdcrpDBHelper, retList);

        //添加版本号
        String endYearMonth = retList.stream().map(ProductDemandDataDfDO::getYearMonth).distinct().max(String::compareTo).get();
        String startYearMonth = retList.stream().map(ProductDemandDataDfDO::getYearMonth).distinct().min(String::compareTo).get();
        for (String product : productCategoryList) {
            supplyAndDemandDictService.insert(statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), startYearMonth, endYearMonth, "demand", product);
        }
    }

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void syncCvmDemandData(LocalDate statTime, boolean forceSync) {
        PplAndOrderAdjustVersionDO versionDO = demandDBHelper.getOne(PplAndOrderAdjustVersionDO.class, " where version_code = ? and product_category = 'CVM' ", statTime);
        if (Objects.isNull(versionDO)) {
            //没有版本数据,无需同步
            return;
        }
        //备份拼接表
        syncPplJoinOrderNewestData(statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), forceSync);

        boolean isExist = ckstdcrpDBHelper.isExist(ProductDemandDataDfDO.class, " where stat_time = ? and product_category = 'CVM' ",
                statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));
        if (isExist && !forceSync) {
            return;
        }
        List<String> productCategory = ListUtils.newArrayList(ProductCategoryEnum.CVM.getName());
        syncDemandData(statTime, productCategory);
    }

    @Override
    public void syncGpuDemandData(LocalDate statTime, boolean forceSync) {
        PplAndOrderAdjustVersionDO versionDO = demandDBHelper.getOne(PplAndOrderAdjustVersionDO.class, " where version_code = ? and product_category = 'GPU' ", statTime);
        if (Objects.isNull(versionDO)) {
            //没有版本数据,无需同步
            return;
        }
        //备份拼接表
        syncPplJoinOrderNewestData(statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), forceSync);

        boolean isExist = ckstdcrpDBHelper.isExist(ProductDemandDataDfDO.class, " where stat_time = ? and product_category = 'GPU' ",
                statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));
        if (isExist && !forceSync) {
            return;
        }
        List<String> productCategory = ListUtils.newArrayList(ProductCategoryEnum.GPU.getName());
        syncDemandData(statTime, productCategory);
    }

    @Override
    public void syncRandomZoneName(LocalDate statTime) {
        syncDemandRandomZoneName(statTime);
        syncDemandAdjustRandomZoneName(statTime);
    }

    public void syncDemandAdjustRandomZoneName(LocalDate statTime) {
        List<ProductDemandDataAdjustDO> list = ckstdcrpDBHelper.getAll(ProductDemandDataAdjustDO.class, "where stat_time = ?", statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));
        if (ListUtils.isEmpty(list)) {
            return;
        }
        for (ProductDemandDataAdjustDO item : list) {
            if (StringUtils.equals(item.getIndustryDept(), "内部业务部") && StringUtils.equals(item.getCommonCustomerShortName(), "IEGG")) {
                item.setCommonCustomerShortName("深圳市腾讯计算机系统有限公司");
            }
            if (!StringUtils.equals(item.getRegionName(), "南京") && StringUtils.equals(item.getZoneName(), "南京三区")) {
                InventoryHealthMainZoneNameConfigDO mainZoneConfig = dictService.queryMainZoneByRegionName(item.provideRegionName());
                if (Objects.nonNull(mainZoneConfig)) {
                    item.fillZone(mainZoneConfig.getZone());
                    item.fillZoneName(mainZoneConfig.getZoneName());
                    item.fillIsMainZone(StringUtils.equals(mainZoneConfig.getType(), InventoryHealthZoneType.PRINCIPAL.getCode()));
                    continue;
                }
                List<YuntiStategyZoneDO> zoneList = dictService.getYunTiStategyZoneDOList();
                Map<String, YuntiStategyZoneDO> zoneGroup = ListUtils.toMap(zoneList, YuntiStategyZoneDO::getRegionName, Function.identity());
                YuntiStategyZoneDO yuntiStategyZoneDO = zoneGroup.get(item.provideRegionName());
                if (Objects.nonNull(yuntiStategyZoneDO)) {
                    item.fillZone(yuntiStategyZoneDO.getZone());
                    item.fillZoneName(yuntiStategyZoneDO.getZoneName());
                    item.fillIsMainZone(false);
                } else {
                    item.fillZone(cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant.EMPTY_STR);
                    item.fillZoneName(cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant.EMPTY_STR);
                    item.fillIsMainZone(false);
                }
            }
        }
        // 删除分区
        CkDBUtils.delete(ckstdcrpDBHelper, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), ProductDemandDataAdjustDO.class);

        // 添加
        log.info(" size :" + list.size());
        CkDBUtils.saveBatch(ckstdcrpDBHelper, list);
    }

    public void syncDemandRandomZoneName(LocalDate statTime) {
        List<ProductDemandDataDfDO> list = ckstdcrpDBHelper.getAll(ProductDemandDataDfDO.class, "where stat_time = ?", statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));
        if (ListUtils.isEmpty(list)) {
            return;
        }
        for (ProductDemandDataDfDO item : list) {
            if (StringUtils.equals(item.getIndustryDept(), "内部业务部") && StringUtils.equals(item.getCommonCustomerShortName(), "IEGG")) {
                item.setCommonCustomerShortName("深圳市腾讯计算机系统有限公司");
            }
            if (!StringUtils.equals(item.getRegionName(), "南京") && StringUtils.equals(item.getZoneName(), "南京三区")) {
                InventoryHealthMainZoneNameConfigDO mainZoneConfig = dictService.queryMainZoneByRegionName(item.provideRegionName());
                if (Objects.nonNull(mainZoneConfig)) {
                    item.fillZone(mainZoneConfig.getZone());
                    item.fillZoneName(mainZoneConfig.getZoneName());
                    item.fillIsMainZone(StringUtils.equals(mainZoneConfig.getType(), InventoryHealthZoneType.PRINCIPAL.getCode()));
                    continue;
                }
                List<YuntiStategyZoneDO> zoneList = dictService.getYunTiStategyZoneDOList();
                Map<String, YuntiStategyZoneDO> zoneGroup = ListUtils.toMap(zoneList, YuntiStategyZoneDO::getRegionName, Function.identity());
                YuntiStategyZoneDO yuntiStategyZoneDO = zoneGroup.get(item.provideRegionName());
                if (Objects.nonNull(yuntiStategyZoneDO)) {
                    item.fillZone(yuntiStategyZoneDO.getZone());
                    item.fillZoneName(yuntiStategyZoneDO.getZoneName());
                    item.fillIsMainZone(false);
                } else {
                    item.fillZone(cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant.EMPTY_STR);
                    item.fillZoneName(cloud.demand.lab.modules.operation_view.supply_and_demand.constants.Constant.EMPTY_STR);
                    item.fillIsMainZone(false);
                }
            }
        }
        // 删除分区
        CkDBUtils.delete(ckstdcrpDBHelper, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), ProductDemandDataDfDO.class);

        // 添加
        log.info(" size :" + list.size());
        CkDBUtils.saveBatch(ckstdcrpDBHelper, list);
    }

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void syncPplJoinOrderNewestData(String statTime, boolean forceSync) {
        boolean isExist = ckstdcrpDBHelper.isExist(AdsCrpPplJoinOrderVersionNewestDfDO.class, " where stat_time = ? ", statTime);
        if (isExist && !forceSync) {
            return;
        }
        List<DwsCrpPplJoinOrderVersionNewestCfDO> list = ckstdcrpDBHelper.getAll(DwsCrpPplJoinOrderVersionNewestCfDO.class);
        List<AdsCrpPplJoinOrderVersionNewestDfDO> retList = ListUtils.transform(list, item -> AdsCrpPplJoinOrderVersionNewestDfDO.transform(item, statTime));

        // 删除分区
        CkDBUtils.delete(ckstdcrpDBHelper, statTime, AdsCrpPplJoinOrderVersionNewestDfDO.class);
        // 添加
        log.info(" size :" + retList.size());
        CkDBUtils.syncSaveBatch(ckstdcrpDBHelper, retList, ListUtils.newArrayList(AdsCrpPplJoinOrderVersionNewestDfDO::getStatTime));
    }

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void syncInventoryData(String statTime) {

        List<AdsInventoryHealthSupplySummaryDfDO> retList = ListUtils.newArrayList();
        retList.addAll(getCvmInventoryData(statTime));
        retList.addAll(getCbsInventoryData(statTime));
        retList.addAll(getDataBaseInventoryData(statTime));

        Map<String, String> regionMap = dictService.getRegionNameMap();
        retList.forEach(o -> o.setRegion(regionMap.getOrDefault(o.getRegionName(), Constant.EMPTY_VALUE_STR)));

        Map<String, String> zoneNameMap = dictService.getZoneNameMap();
        retList.forEach(o -> o.setZone(zoneNameMap.getOrDefault(o.getZoneName(), Constant.EMPTY_VALUE_STR)));

        fillerService.fill(retList);
        // 删除分区
        CkDBUtils.doDelete(ckcldDBHelper, statTime, AdsInventoryHealthSupplySummaryDfDO.class, "cloud_demand");
        // 添加
        log.info(" size :" + retList.size());
        CkDBUtils.saveBatch(ckcldDBHelper, retList);

        //添加版本号
        List<String> prodctCategory = ListUtils.newArrayList("CVM", "CBS", "数据库");
        for (String product : prodctCategory) {
            supplyAndDemandDictService.insert(statTime, "1900-01", "1900-01", "inventory", product);
        }
    }

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void genInventoryTargetData(String statTime) {
        List<SupplAndDemandInventoryTargetDO> targetList = demandDBHelper.getAll(SupplAndDemandInventoryTargetDO.class);

        LocalDate statDate = LocalDate.parse(statTime);
        List<SupplAndDemandInventoryTargetDfDO> ret = ListUtils.transform(targetList, item -> SupplAndDemandInventoryTargetDfDO.transform(item, statDate));
        // 删除分区
        CkDBUtils.doDelete(ckcldDBHelper, statTime, SupplAndDemandInventoryTargetDfDO.class, "cloud_demand");
        // 添加
        log.info(" size :" + ret.size());
        CkDBUtils.saveBatch(ckcldDBHelper, ret);
    }

    @Override
    @Synchronized(waitLockMillisecond = 1800)
    public void genDbSaleScaleData(String statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_db_sale_scale_data.sql");
        List<DwdDbSaleScaleDfDO> retList = planDbDBHelper.getRaw(DwdDbSaleScaleDfDO.class, sql, LocalDate.parse(statTime).format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        // 删除分区
        CkDBUtils.delete(ckstdcrpDBHelper, statTime, DwdDbSaleScaleDfDO.class);
        // 添加
        log.info(" size :" + retList.size());
        CkDBUtils.saveBatch(ckstdcrpDBHelper, retList);
    }

    @Override
    public void repairData(String statTime) {
        List<ProductDemandDataAdjustDO> list = ckstdcrpDBHelper.getAll(ProductDemandDataAdjustDO.class, "where stat_time = ?", statTime);
        if (ListUtils.isEmpty(list)) {
            return;
        }
        for (ProductDemandDataAdjustDO item : list) {
            DemandTypeEnum demandTypeEnum = DemandTypeEnum.getByName(item.getDemandType());
            if (Objects.nonNull(demandTypeEnum)) {
                item.setDemandType(demandTypeEnum.getCode());
            }
        }
        // 删除分区
        CkDBUtils.delete(ckstdcrpDBHelper, statTime, ProductDemandDataAdjustDO.class);

        // 添加
        log.info(" size :" + list.size());
        CkDBUtils.saveBatch(ckstdcrpDBHelper, list);
    }

    private List<ProductDemandDataDfDO> getCvmDemandData(LocalDate statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_cvm_demand_data.sql");
        String minYearMonth = statTime.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        List<SyncCvmDemandDataVO> dbList = ckstdcrpDBHelper.getRaw(SyncCvmDemandDataVO.class, sql, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE), minYearMonth);

        List<ProductDemandDataDfDO> retList = ListUtils.transform(dbList, SyncCvmDemandDataVO::transform);
        fillerService.fill(retList);

        //添加跨月数据
        retList.addAll(SyncCvmDemandDataVO.buildCrossMonthData(retList, statTime));
        // 删除 retList 内 yearMonth 值在 statTime 前的 历史月份数据
        YearMonth statYearMonth = YearMonth.from(statTime);
        retList.removeIf(item -> {
            try {
                return YearMonth.parse(item.getYearMonth()).isBefore(statYearMonth);
            } catch (Exception e) {
                // 解析失败时记录日志并保留该数据项（不删除）
                log.warn("解析yearMonth失败，保留数据项: {}, 错误: {}", item.getYearMonth(), e.getMessage());
                return false;
            }
        });

        return retList;
    }

    private List<ProductDemandDataDfDO> getGpuDemandData(LocalDate statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_gpu_demand_data.sql");
        List<SyncGpuDemandDataVO> dbList = ckstdcrpDBHelper.getRaw(SyncGpuDemandDataVO.class, sql, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));

        List<ProductDemandDataDfDO> retList = ListUtils.transform(dbList, SyncGpuDemandDataVO::transform);
        fillerService.fill(retList);

        //添加跨月数据
        retList.addAll(SyncGpuDemandDataVO.buildCrossMonthData(retList, statTime));

        return retList;
    }

    private List<ProductDemandDataDfDO> getCbsDemandData(LocalDate statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_cbs_demand_data.sql");
        List<SyncCbsDemandDataVO> dbList = ckstdcrpDBHelper.getRaw(SyncCbsDemandDataVO.class, sql, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));

        List<ProductDemandDataDfDO> retList = SyncCbsDemandDataVO.builder(dbList);
        fillerService.fill(retList);
        //添加系统校准数据
        retList.addAll(SyncCbsDemandDataVO.buildSystemData(retList));
        return retList;
    }

    private List<ProductDemandDataDfDO> getDbDemandData(LocalDate statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_db_demand_data.sql");
        List<SyncDbDemandDataVO> dbList = ckstdcrpDBHelper.getRaw(SyncDbDemandDataVO.class, sql, statTime.format(DateTimeFormatter.ISO_LOCAL_DATE));

        List<ProductDemandDataDfDO> retList = ListUtils.transform(dbList, SyncDbDemandDataVO::transform);
        fillerService.fill(retList);
        //添加跨月数据
        retList.addAll(SyncDbDemandDataVO.buildCrossMonthData(retList));
        return retList;
    }

    private List<AdsInventoryHealthSupplySummaryDfDO> getCvmInventoryData(String statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_cvm_inventory_data.sql");

        return ckcldDBHelper.getRaw(AdsInventoryHealthSupplySummaryDfDO.class, sql, statTime);

    }

    private List<AdsInventoryHealthSupplySummaryDfDO> getCbsInventoryData(String statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_cbs_inventory_data.sql");
        return plancbsDBHelper.getRaw(AdsInventoryHealthSupplySummaryDfDO.class, sql, statTime, statTime, statTime, statTime);
    }

    private List<AdsInventoryHealthSupplySummaryDfDO> getDataBaseInventoryData(String statTime) {
        String sql = ORMUtils.getSql("/sql/operation_view/supply_and_demand/sync_db_crs_inventory_data.sql");
        return ckcldDBHelper.getRaw(AdsInventoryHealthSupplySummaryDfDO.class, sql, statTime, statTime, statTime, statTime);
    }

}
