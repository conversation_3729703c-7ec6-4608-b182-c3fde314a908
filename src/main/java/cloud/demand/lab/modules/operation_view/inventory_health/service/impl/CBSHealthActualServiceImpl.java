package cloud.demand.lab.modules.operation_view.inventory_health.service.impl;

import cloud.demand.lab.common.excel.LocalDateStringConverter;
import cloud.demand.lab.common.excel.LocalTimeStringConverter;
import cloud.demand.lab.common.utils.SpringUtil;
import cloud.demand.lab.modules.operation_view.entity.web.common.DownloadBean;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.cbs_actual.*;
import cloud.demand.lab.modules.operation_view.inventory_health.dto.config.QueryZoneConfigReq;
import cloud.demand.lab.modules.operation_view.inventory_health.entity.*;
import cloud.demand.lab.modules.operation_view.inventory_health.service.CBSHealthActualService;
import cloud.demand.lab.modules.operation_view.operation_view.service.impl.OperationViewService2Impl;
import cloud.demand.lab.modules.operation_view.util.SopDateUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class CBSHealthActualServiceImpl implements CBSHealthActualService {

    @Resource
    DBHelper ckcldDBHelper;

    @Resource
    DBHelper demandDBHelper;

    @Override
    public List<CBSHealthActualData> queryCBSHealthActualTrendReport(CBSHealthActualReq req) {

        Map<String, String> dateMap = new HashMap<>();
        Map<String, Integer> countMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                countMap = getWeekCountMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                countMap = getMonthCountMap(req.getStart(),
                        req.getEnd());
                break;
        }
        WhereSQL condition = req.genBasicCondition();
        WhereSQL inventoryCondition = req.genInventoryCondition();
        WhereSQL configCondition = req.genConfigCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
            inventoryCondition.and("zone_name in (?)", zoneNameCategory);
        }
        //获取数据
        //1.服务水平数据
        List<DwsCbsServiceLevelDataDfDO> all = ckcldDBHelper.getAll(DwsCbsServiceLevelDataDfDO.class,
                condition.getSQL(), condition.getParams());
        //2.库存数据
        List<DwsCbsSafeInventoryDataDfDO> inventoryAll = ckcldDBHelper.getAll(DwsCbsSafeInventoryDataDfDO.class,
                inventoryCondition.getSQL(), inventoryCondition.getParams());
        configCondition.and("version_code in (?)", genWeekInfo(req.getStart(), req.getEnd()));
        List<CbsInventoryAdjustVersionDataDO> configAll = demandDBHelper.getAll(CbsInventoryAdjustVersionDataDO.class,
                configCondition.getSQL(), configCondition.getParams());
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList, InventoryHealthMainZoneNameConfigDO::getZoneName,
                o -> o);
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsCbsServiceLevelDataDfDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName()), o -> o);
        List<CBSHealthActualData> result = new ArrayList<>();
        Map<String, List<DwsCbsSafeInventoryDataDfDO>> inventoryMap = ListUtils.toMapList(inventoryAll,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName()), o -> o);
        Map<String, List<CbsInventoryAdjustVersionDataDO>> configInventoryMap = ListUtils.toMapList(configAll,
                CbsInventoryAdjustVersionDataDO::getZoneName, o -> o);
        Set<String> keySet = new HashSet<>();
        if (ListUtils.isNotEmpty(mapList.keySet())) {
            keySet.addAll(mapList.keySet());
        }
        if (ListUtils.isNotEmpty(inventoryMap.keySet())) {
            keySet.addAll(inventoryMap.keySet());
        }
        for (String key : keySet) {
            CBSHealthActualData data = new CBSHealthActualData();
            List<DwsCbsServiceLevelDataDfDO> serviceList = mapList.get(key);
            List<DwsCbsSafeInventoryDataDfDO> inventoryList = inventoryMap.get(key);
            if (ListUtils.isNotEmpty(serviceList)) {
                DwsCbsServiceLevelDataDfDO temp = serviceList.get(0);
                data.setStatTime(dateMap.get(temp.getStatTime()));
                data.setZoneName(temp.getZoneName());
                data.setRegionName(temp.getRegionName());
                data.setAreaName(temp.getAreaName());
                data.setCustomhouseTitle(temp.getCustomhouseTitle());
                if (StringUtils.isNotBlank(req.getVolumeType())) {
                    data.setVolumeType(temp.getVolumeType());
                }
            }else if(ListUtils.isNotEmpty(inventoryList)) {
                DwsCbsSafeInventoryDataDfDO temp = inventoryList.get(0);
                data.setStatTime(dateMap.get(temp.getStatTime()));
                data.setZoneName(temp.getZoneName());
                data.setRegionName(temp.getRegionName());
                data.setAreaName(temp.getAreaName());
                data.setCustomhouseTitle(temp.getCustomhouseTitle());
                if (StringUtils.isNotBlank(req.getVolumeType())) {
                    data.setVolumeType(temp.getVolumeType());
                }
            }
            List<CbsInventoryAdjustVersionDataDO> configInventoryList = configInventoryMap.get(data.getZoneName());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(data.getZoneName());
            if (configItem != null) {
                data.setZoneCategory(configItem.getTypeName());
            }else {
                data.setZoneCategory("未分类");
            }
            data.setFailedCount(NumberUtils.sum(serviceList,
                    DwsCbsServiceLevelDataDfDO::getFailedCount));
            data.setSuccessCount(NumberUtils.sum(serviceList,
                    DwsCbsServiceLevelDataDfDO::getSuccessCount));
            data.setSuccessDiskSize(NumberUtils.sum(serviceList,
                    DwsCbsServiceLevelDataDfDO::getSuccessDiskSize));
            data.setFailedDiskSize(NumberUtils.sum(serviceList,
                    DwsCbsServiceLevelDataDfDO::getFailDiskSize));
            BigDecimal totalSize = data.getFailedDiskSize().add(data.getSuccessDiskSize());
            if (totalSize.compareTo(BigDecimal.ZERO) > 0) {
                data.setServiceLevel(data.getSuccessDiskSize()
                        .divide(totalSize, 4, RoundingMode.HALF_UP));
            }
            int size = 1;
            if (req.getDateType().equals("week") || req.getDateType().equals("month")) {
                size = countMap.get(data.getStatTime());
            }
            Set<String> dates = new HashSet<>();
            if (ListUtils.isNotEmpty(inventoryList)) {
                dates.addAll(inventoryList.stream().map(DwsCbsSafeInventoryDataDfDO::getStatTime).
                        distinct().sorted(String::compareTo).collect(Collectors.toList()));
            }
            BigDecimal safeInv = BigDecimal.ZERO;
            for (String date : dates) {
                LocalDate localDate = DateUtils.parseLocalDate(date);
                int isoWeek = localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
                int weekBasedYear = localDate.get(IsoFields.WEEK_BASED_YEAR);
                String weekInfo = weekBasedYear + "-W" + SopDateUtils.fixNumber(isoWeek);
                if (ListUtils.isNotEmpty(configInventoryList)) {
                    List<CbsInventoryAdjustVersionDataDO> temp = configInventoryList.stream().
                            filter(o -> o.getVersionCode().equals(weekInfo)).collect(Collectors.toList());
                    safeInv = safeInv.add(NumberUtils.sum(temp, CbsInventoryAdjustVersionDataDO::getAdjustSafeInventory));
                }
            }
            BigDecimal actInv = BigDecimal.ZERO;
            if (ListUtils.isEmpty(req.getMaterialType())) {
                req.setMaterialType(Arrays.asList("好料", "差料", "呆料"));
            }
            for (String material : req.getMaterialType()) {
                switch (material) {
                    case "好料":
                        actInv = actInv.add(getGoodMaterialInv(inventoryList, req, size, configInventoryList));
                        break;
                    case "差料":
                        actInv = actInv.add(NumberUtils.sum(inventoryList, DwsCbsSafeInventoryDataDfDO::getOnlineBadStock)
                                .divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP));
                        break;
                    case "呆料":
                        actInv = actInv.add(NumberUtils.sum(inventoryList, DwsCbsSafeInventoryDataDfDO::getOnlineIdleStock)
                                .divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP));
                        break;
                }
            }
            data.setSafeInventory(safeInv.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP));
            data.setActualInventory(actInv);
            if (data.getZoneCategory().equals("主力可用区") && data.getCustomhouseTitle().equals("境内")) {
                if (StringUtils.isBlank(req.getVolumeType())) {
                    data.setThreshold(BigDecimal.valueOf(1.5));
                }else if (req.getVolumeType().equals("premium")) {
                    data.setThreshold(BigDecimal.valueOf(1));
                }else {
                    data.setThreshold(BigDecimal.valueOf(0.5));
                }
            }else if (data.getCustomhouseTitle().equals("境外") ||
                    (data.getCustomhouseTitle().equals("境内") && !data.getZoneCategory().equals("主力可用区"))) {
                if (StringUtils.isBlank(req.getVolumeType())) {
                    data.setThreshold(BigDecimal.valueOf(0.75));
                }else if (req.getVolumeType().equals("premium")) {
                    data.setThreshold(BigDecimal.valueOf(0.5));
                }else {
                    data.setThreshold(BigDecimal.valueOf(0.25));
                }
            }
            result.add(data);
        }
        return result;
    }

    public BigDecimal getGoodMaterialInv(List<DwsCbsSafeInventoryDataDfDO> invenotryList,
                                         CBSHealthActualReq req, int size,
                                         List<CbsInventoryAdjustVersionDataDO> configInventoryList) {
        BigDecimal actInv = BigDecimal.ZERO;
        if (req.getGoodMaterialTag().equals("goodMaterialType")) {
            BigDecimal temp = NumberUtils.sum(invenotryList, DwsCbsSafeInventoryDataDfDO::getOnlineGoodStock)
                    .add(NumberUtils.sum(invenotryList, DwsCbsSafeInventoryDataDfDO::getOfflineStock));
            actInv = temp.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
            if (ListUtils.isNotEmpty(req.getGoodMaterialType())) {
                BigDecimal move = BigDecimal.ZERO;
                BigDecimal old = BigDecimal.ZERO;
                Set<String> dates = new HashSet<>();
                if (ListUtils.isNotEmpty(invenotryList)) {
                    dates.addAll(invenotryList.stream().map(DwsCbsSafeInventoryDataDfDO::getStatTime).
                            distinct().sorted(String::compareTo).collect(Collectors.toList()));
                }
                for (String date : dates) {
                    LocalDate localDate = DateUtils.parseLocalDate(date);
                    int isoWeek = localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
                    int weekBasedYear = localDate.get(IsoFields.WEEK_BASED_YEAR);
                    String weekInfo = weekBasedYear + "-W" + SopDateUtils.fixNumber(isoWeek);
                    if (ListUtils.isNotEmpty(configInventoryList)) {
                        List<CbsInventoryAdjustVersionDataDO> inventoryTemp = configInventoryList.stream().
                                filter(o -> o.getVersionCode().equals(weekInfo)).collect(Collectors.toList());
                        move = move.add(NumberUtils.sum(inventoryTemp, CbsInventoryAdjustVersionDataDO::getGoodStockRemove));
                        old = old.add(NumberUtils.sum(inventoryTemp, CbsInventoryAdjustVersionDataDO::getGoodStockOld));
                    }
                }
                move = move.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
                old = old.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
                BigDecimal actSold = actInv.subtract(move).subtract(old);
                actInv = BigDecimal.ZERO;
                for (String str : req.getGoodMaterialType()) {
                    if (str.equals("实际可售")) {
                        actInv = actInv.add(actSold);
                    }else if (str.equals("云徙")) {
                        actInv = actInv.add(move);
                    }else {
                        actInv = actInv.add(old);
                    }
                }
            }
        }else if (req.getGoodMaterialTag().equals("goodMaterialStatus")) {
            if (ListUtils.isEmpty(req.getGoodMaterialStatus()) || req.getGoodMaterialStatus().size() == 2) {
                BigDecimal temp = NumberUtils.sum(invenotryList, DwsCbsSafeInventoryDataDfDO::getOnlineGoodStock)
                        .add(NumberUtils.sum(invenotryList, DwsCbsSafeInventoryDataDfDO::getOfflineStock));
                actInv = temp.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
            }else if (req.getGoodMaterialStatus().contains("线上可售卖")) {
                BigDecimal temp = NumberUtils.sum(invenotryList, DwsCbsSafeInventoryDataDfDO::getOnlineGoodStock);
                actInv = temp.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
            }else {
                BigDecimal temp = NumberUtils.sum(invenotryList, DwsCbsSafeInventoryDataDfDO::getOfflineStock);
                actInv = temp.divide(BigDecimal.valueOf(size), 4, RoundingMode.HALF_UP);
            }
        }
        return actInv;
    }

    public List<String> genWeekInfo(String start, String end) {
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        Set<String> weekInfo = new HashSet<>();
        while(!startDate.isAfter(endDate)) {
            int isoWeek = startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            int weekBasedYear = startDate.get(IsoFields.WEEK_BASED_YEAR);
            weekInfo.add(weekBasedYear + "-W" + SopDateUtils.fixNumber(isoWeek));
            startDate = startDate.plusDays(1);
        }
        return new ArrayList<>(weekInfo);
    }

    @Override
    public DownloadBean exportCBSHealthActualTrendReport(CBSHealthActualReq req) {
        Map<String, String> dateMap = new HashMap<>();
        switch (req.getDateType()) {
            case "day":
                dateMap = getDayMap(req.getStart(), req.getEnd());
                break;
            case "week":
                dateMap = getWeekMap(req.getStart(), req.getEnd());
                break;
            case "month":
                dateMap = getMonthMap(req.getStart(), req.getEnd());
                break;
        }
        WhereSQL condition = req.genBasicCondition();
        OperationViewService2Impl bean = SpringUtil.getBean(OperationViewService2Impl.class);
        if (ListUtils.isNotEmpty(req.getZoneCategory())) {
            List<String> zoneNameCategory = bean.getZoneNamesByZoneCategory(req.getZoneCategory(),
                    DateUtils.formatDate(DateUtils.yesterday()));
            condition.and("zone_name in (?)", zoneNameCategory);
        }
        //获取数据
        List<DwsCbsServiceLevelDataDfDO> all = ckcldDBHelper.getAll(DwsCbsServiceLevelDataDfDO.class,
                condition.getSQL(), condition.getParams());
        QueryZoneConfigReq tempReq = new QueryZoneConfigReq();
        tempReq.setDate(DateUtils.formatDate(DateUtils.yesterday()));
        WhereSQL cond = tempReq.genCondition();
        List<InventoryHealthMainZoneNameConfigDO> configDOList = demandDBHelper.getAll(InventoryHealthMainZoneNameConfigDO.class, cond.getSQL(), cond.getParams());
        Map<String, InventoryHealthMainZoneNameConfigDO> configMap = ListUtils.toMap(configDOList,
                InventoryHealthMainZoneNameConfigDO::getZoneName,
                o -> o);
        Map<String, String> finalDateMap = dateMap;
        Map<String, List<DwsCbsServiceLevelDataDfDO>> mapList = ListUtils.toMapList(all,
                o -> String.join("@", finalDateMap.get(o.getStatTime()), o.getZoneName(), o.getAppId(),
                        o.getIndustryDept(), o.getCustomerGroup()), o -> o);
        List<CBSHealthActualTrendExportData> result = new ArrayList<>();
        for (Entry<String, List<DwsCbsServiceLevelDataDfDO>> entry : mapList.entrySet()) {
            CBSHealthActualTrendExportData data = new CBSHealthActualTrendExportData();
            DwsCbsServiceLevelDataDfDO temp = entry.getValue().get(0);
            data.setStatTime(dateMap.get(temp.getStatTime()));
            data.setZoneName(temp.getZoneName());
            data.setRegionName(temp.getRegionName());
            data.setAreaName(temp.getAreaName());
            data.setCustomhouseTitle(temp.getCustomhouseTitle());
            data.setAppId(temp.getAppId());
            data.setCustomerShortName(temp.getCustomerShortName());
            data.setIndustryDept(temp.getIndustryDept());
            data.setCustomerGroup(temp.getCustomerGroup());
            InventoryHealthMainZoneNameConfigDO configItem = configMap.get(data.getZoneName());
            if (configItem != null) {
                data.setZoneCategory(configItem.getTypeName());
            }else {
                data.setZoneCategory("未分类");
            }
            if (StringUtils.isNotBlank(req.getVolumeType())) {
                data.setVolumeType(temp.getVolumeType());
            }
            data.setFailedCount(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getFailedCount).intValue());
            data.setSuccessCount(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getSuccessCount).intValue());
            data.setSuccessDiskSize(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getSuccessDiskSize).intValue());
            data.setFailedDiskSize(NumberUtils.sum(entry.getValue(),
                    DwsCbsServiceLevelDataDfDO::getFailDiskSize).intValue());
            result.add(data);
        }
        result.sort(Comparator.comparing(CBSHealthActualTrendExportData::getStatTime)
                .thenComparing(CBSHealthActualTrendExportData::getZoneName)
                .thenComparing(CBSHealthActualTrendExportData::getAppId));
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/cbs_health_actual_trend.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("item", result), writeSheet).finish();
        String fileName = "CBS服务水平趋势数据明细" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public List<CBSAdjustInventoryData> queryCBSAdjustInventoryReport(CBSAdjustInventoryReportReq req) {
        WhereSQL condition = req.genBasicCondition();
        List<CbsInventoryAdjustVersionDataDO> all = demandDBHelper.getAll(CbsInventoryAdjustVersionDataDO.class, condition.getSQL(), condition.getParams());
        return all.stream().map(o -> {
            CBSAdjustInventoryData data = new CBSAdjustInventoryData();
            BeanUtils.copyProperties(o, data);
            return data;
        }).collect(Collectors.toList());
    }

    @Override
    public void adjustCBSInventory(CBSAdjustInventoryReq req) {
        WhereSQL condition = req.genBasicCondition();
        CbsInventoryAdjustVersionDataDO item = demandDBHelper.getOne(CbsInventoryAdjustVersionDataDO.class, condition.getSQL(), condition.getParams());
        item.setAdjustSafeInventory(req.getAdjustSafeInventory());
        item.setGoodStockOld(req.getGoodStockOld());
        item.setGoodStockRemove(req.getGoodStockRemove());
        demandDBHelper.update(item);
    }

    @Override
    public DownloadBean exportCBSAdjustInventoryReport(CBSAdjustInventoryReportReq req) {
        List<CBSAdjustInventoryData> data = queryCBSAdjustInventoryReport(req);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/inventory_health/cbs_adjust_inventory_report.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();
        String fileName = "CBS库存策略配置明细" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public List<CBSAdjustVersionData> getVersion() {
        List<CbsInventoryAdjustVersionDO> all = demandDBHelper.getAll(CbsInventoryAdjustVersionDO.class);
        return all.stream().map(CbsInventoryAdjustVersionDO::transform)
                .sorted(Comparator.comparing(CBSAdjustVersionData::getVersionCode)).collect(Collectors.toList());
    }


    public Map<String, String> getDayMap(String startDate, String endDate) {
        Map<String, String> result = new HashMap<>();
        LocalDate start = DateUtils.parseLocalDate(startDate);
        LocalDate end = DateUtils.parseLocalDate(endDate);
        while(!start.isAfter(end)) {
            result.put(DateUtils.formatDate(start), DateUtils.formatDate(start));
            start = start.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getWeekMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            result.put(startDate.toString(), startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR));
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, String> getMonthMap(String start, String end) {
        Map<String, String> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(DateUtils.formatDate(startDate), yearMonth.toString());
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, Integer> getWeekCountMap(String start, String end) {
        Map<String, Integer> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end).isAfter(DateUtils.yesterday())?
                DateUtils.yesterday() : DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            String key = startDate.getYear() + "W" + startDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
            result.put(key, result.getOrDefault(key, 0) + 1);
            startDate = startDate.plusDays(1);
        }
        return result;
    }

    public Map<String, Integer> getMonthCountMap(String start, String end) {
        Map<String, Integer> result = new HashMap<>();
        LocalDate startDate = DateUtils.parseLocalDate(start);
        LocalDate endDate = DateUtils.parseLocalDate(end).isAfter(DateUtils.yesterday())?
                DateUtils.yesterday() : DateUtils.parseLocalDate(end);
        while(!startDate.isAfter(endDate)) {
            YearMonth yearMonth = YearMonth.of(startDate.getYear(), startDate.getMonthValue());
            result.put(yearMonth.toString(), result.getOrDefault(yearMonth.toString(), 0)  + 1);
            startDate = startDate.plusDays(1);
        }
        return result;
    }
}
