package cloud.demand.lab.modules.order.entity.std_table;


import cloud.demand.lab.common.entity.BaseDO;
import cloud.demand.lab.modules.order.entity.IOrderItemSatisfyGroupKey;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("order_consensus_demand_detail")
public class OrderConsensusDemandDetailDO extends BaseDO implements IOrderItemSatisfyGroupKey {

    /** 共识需求版本id<br/>Column: [version_id] */
    @Column(value = "version_id")
    private Long versionId;

    /** 业务订单号，一个订单的唯一标识<br/>Column: [order_number] */
    @Column(value = "order_number")
    private String orderNumber;

    /** 共识需求可用状态:available,not_available<br/>Column: [available_status] */
    @Column(value = "available_status")
    private String availableStatus;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 实例类型<br/>Column: [demand_instance_type] */
    @Column(value = "demand_instance_type")
    private String demandInstanceType;

    /** gpu卡型<br/>Column: [demand_gpu_type] */
    @Column(value = "demand_gpu_type")
    private String demandGpuType;

    /** 可用区<br/>Column: [demand_zone_name] */
    @Column(value = "demand_zone_name")
    private String demandZoneName;

    /** 地域<br/>Column: [demand_region_name] */
    @Column(value = "demand_region_name")
    private String demandRegionName;

    /** 区域<br/>Column: [demand_area_name] */
    @Column(value = "demand_area_name")
    private String demandAreaName;

    /** 境内外<br/>Column: [demand_customhouse_title] */
    @Column(value = "demand_customhouse_title")
    private String demandCustomhouseTitle;

    /** 共识开始购买时间<br/>Column: [consensus_begin_buy_date] */
    @Column(value = "consensus_begin_buy_date")
    private LocalDate consensusBeginBuyDate;

    /** 共识结束购买时间<br/>Column: [consensus_end_buy_date] */
    @Column(value = "consensus_end_buy_date")
    private LocalDate consensusEndBuyDate;

    /** 共识需求量-cpu核数<br/>Column: [consensus_demand_cpu_num] */
    @Column(value = "consensus_demand_cpu_num")
    private Integer consensusDemandCpuNum;

    /** 共识需求量-gpu卡数<br/>Column: [consensus_demand_gpu_num] */
    @Column(value = "consensus_demand_gpu_num")
    private Integer consensusDemandGpuNum;

    /** 已满足量-cpu核数<br/>Column: [satisfied_cpu_num] */
    @Column(value = "satisfied_cpu_num")
    private BigDecimal satisfiedCpuNum;

    /** 已满足量-gpu卡数<br/>Column: [satisfied_gpu_num] */
    @Column(value = "satisfied_gpu_num")
    private BigDecimal satisfiedGpuNum;

    /** 待满足量-cpu核数<br/>Column: [wait_satisfied_cpu_num] */
    @Column(value = "wait_satisfied_cpu_num")
    private BigDecimal waitSatisfiedCpuNum;

    /** 待满足量-gpu卡数<br/>Column: [wait_satisfied_gpu_num] */
    @Column(value = "wait_satisfied_gpu_num")
    private BigDecimal waitSatisfiedGpuNum;

    /** 已履约量<br/>Column: [buy_num] */
    @Column(value = "buy_num")
    private BigDecimal buyNum;

    /**
     * 已满足未履约量 = 满足量 - 已履约量
     * <br/>Column: [satisfied_wait_buy_num] */
    @Column(value = "satisfied_wait_buy_num")
    private BigDecimal satisfiedWaitBuyNum;

    /**
     * 总未履约量 = 共识需求量 - 未履约量
     * <br/>Column: [total_wait_buy_num] */
    @Column(value = "total_wait_buy_num")
    private BigDecimal totalWaitBuyNum;

    /** 供应方案版本id<br/>Column: [plan_version_id] */
    @Column(value = "plan_version_id")
    private Long planVersionId;

    /** 共识需求id<br/>Column: [consensus_demand_version_id] */
    @Column(value = "consensus_demand_version_id")
    private String consensusDemandVersionId;


    @Override
    public String getZoneName() {
        return demandZoneName;
    }

    @Override
    public String getInstanceType() {
        return demandInstanceType;
    }

    @Override
    public LocalDate getBeginBuyDate() {
        return getConsensusBeginBuyDate();
    }
}
