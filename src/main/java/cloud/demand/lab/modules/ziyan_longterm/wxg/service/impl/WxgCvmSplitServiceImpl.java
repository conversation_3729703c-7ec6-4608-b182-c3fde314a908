package cloud.demand.lab.modules.ziyan_longterm.wxg.service.impl;

import cloud.demand.lab.common.utils.EStream;
import cloud.demand.lab.common.utils.ListUtils2;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.RegionRatioDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.*;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.WxgCvmSplitService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryWxgDeviceTypeSplitDetailReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryWxgDeviceTypeSplitDetailResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.stream.Collectors;

/**
 * WXG CVM拆分服务实现类
 * 提供WXG云服务器拆分相关的业务功能实现，支持多层级数据拆分
 * 包括按地域拆分、按设备类型拆分等功能，支持多时间维度的数据聚合和展示
 * 集成历史数据查询、占比计算、拆分版本管理等完整功能
 */
@Service
@Slf4j
public class WxgCvmSplitServiceImpl implements WxgCvmSplitService {

    @Resource
    private DBHelper ckcubesDBHelper;
    @Resource
    private DBHelper cdLabDbHelper;



    /**
     * 查询WXG设备类型拆分详情
     * 根据拆分版本ID查询设备类型拆分的详细数据，按策略类型分组展示，支持多时间维度的数据聚合
     * 
     * @param req 查询请求参数，包含任务ID和拆分版本ID
     * @return QueryWxgDeviceTypeSplitDetailResp 拆分详情响应，包含按策略类型分组的设备类型拆分数据
     * @throws IllegalArgumentException 当splitVersionId为空时抛出
     * @throws RuntimeException 当未找到拆分版本或任务时抛出
     */
    public QueryWxgDeviceTypeSplitDetailResp queryWxgDeviceTypeSplitDetail(QueryWxgDeviceTypeSplitDetailReq req) {
        // 1. 参数验证
        if (req.getSplitVersionId() == null) {
            throw new IllegalArgumentException("splitVersionId不能为空");
        }

        // 2. 获取拆分版本信息
        ZiyanCvmLongtermPredictOutputSplitVersionDO versionDO = 
            cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictOutputSplitVersionDO.class, req.getSplitVersionId());
        if (versionDO == null) {
            throw new RuntimeException("未找到拆分版本: " + req.getSplitVersionId());
        }

        // 3. 获取任务信息
        ZiyanCvmLongtermPredictTaskDO taskDO = 
            cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, versionDO.getTaskId());
        if (taskDO == null) {
            throw new RuntimeException("未找到任务: " + versionDO.getTaskId());
        }

        // 4. 查询拆分详情数据
        List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitDetailList = 
            cdLabDbHelper.getAll(ZiyanCvmLongtermPredictOutputScaleSplitDO.class, 
                "where split_version_id=?", req.getSplitVersionId());

        // 5. 构建响应对象
        QueryWxgDeviceTypeSplitDetailResp resp = new QueryWxgDeviceTypeSplitDetailResp();
        
        // 设置显示列名
        resp.setDisplayKeyColumn(Arrays.asList("bg", "regionName", "instanceFamily", "instanceType", "deviceType"));
        resp.setDisplayKeyColumnName(Arrays.asList("事业群", "地域名称", "机型族", "实例类型", "设备类型"));

        // 6. 按策略类型分组处理数据
        EStream.of(splitDetailList).groupAndConsume(
            ZiyanCvmLongtermPredictOutputScaleSplitDO::getStrategyType,
            (strategyType, list) -> {
                // 构建日期列信息
                QueryWxgDeviceTypeSplitDetailResp.WxgDateColumList dateColumList = buildWxgDateColumList(list);
                
                // 创建策略类型项
                QueryWxgDeviceTypeSplitDetailResp.WxgStrategyTypeItem strategyTypeItem = new QueryWxgDeviceTypeSplitDetailResp.WxgStrategyTypeItem();
                strategyTypeItem.setStrategyType(strategyType);
                strategyTypeItem.setStrategyTypeName(getStrategyTypeName(strategyType));
                strategyTypeItem.setDateKey(dateColumList);
                
                // 按业务维度分组聚合数据
                Collection<QueryWxgDeviceTypeSplitDetailResp.WxgScaleItem> scaleItems = ListUtils2.groupThenApply(
                    list, 
                    ZiyanCvmLongtermPredictOutputScaleSplitDO::getKeyWithOutDateStr,
                    (keyStr, dateList) -> buildWxgScaleItem(dateList, dateColumList)
                ).values();
                
                strategyTypeItem.setItems(new ArrayList<>(scaleItems));
                resp.getStrategyTypeItems().add(strategyTypeItem);
            }
        );

        return resp;
    }


    /**
     * 构建WXG规模数据项
     * 将同一业务维度下的多个时间点数据聚合为一个规模数据项，支持季度、半年、年度、月度四个时间维度的数据聚合
     * 
     * @param dateList 同一业务维度下的拆分数据列表，包含不同时间点的预测规模数据
     * @param dateColumList 日期列信息，定义了各时间维度的日期范围
     * @return WxgScaleItem 聚合后的规模数据项，包含各时间维度的预测规模列表
     */
    private QueryWxgDeviceTypeSplitDetailResp.WxgScaleItem buildWxgScaleItem(List<ZiyanCvmLongtermPredictOutputScaleSplitDO> dateList,
                                                                             QueryWxgDeviceTypeSplitDetailResp.WxgDateColumList dateColumList) {
        QueryWxgDeviceTypeSplitDetailResp.WxgScaleItem item = QueryWxgDeviceTypeSplitDetailResp.WxgScaleItem.from(dateList.get(0));
        
        // 按季度聚合数据
        Map<String, BigDecimal> quarterMap = EStream.of(dateList)
            .groupBy(this::getQuarterName)
            .toMap(kv -> kv.getKey(), 
                   kv -> EStream.of(kv.getValue()).sum(ZiyanCvmLongtermPredictOutputScaleSplitDO::getPredictScale));
        EStream.of(dateColumList.getQuarterDateNotStrict())
            .map(quarter -> quarterMap.getOrDefault(quarter, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP))
            .consumeAsList(item::setQuarterPredictScale);
        
        // 按半年聚合数据
        Map<String, BigDecimal> halfYearMap = EStream.of(dateList)
            .groupBy(this::getHalfYearName)
            .toMap(kv -> kv.getKey(), 
                   kv -> EStream.of(kv.getValue()).sum(ZiyanCvmLongtermPredictOutputScaleSplitDO::getPredictScale));
        EStream.of(dateColumList.getHalfYearDateNotStrict())
            .map(halfYear -> halfYearMap.getOrDefault(halfYear, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP))
            .consumeAsList(item::setHalfYearPredictScale);
        
        // 按年度聚合数据
        Map<String, BigDecimal> yearMap = EStream.of(dateList)
            .groupBy(this::getYearName)
            .toMap(kv -> kv.getKey(), 
                   kv -> EStream.of(kv.getValue()).sum(ZiyanCvmLongtermPredictOutputScaleSplitDO::getPredictScale));
        EStream.of(dateColumList.getYearDateNotStrict())
            .map(year -> yearMap.getOrDefault(year, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP))
            .consumeAsList(item::setYearPredictScale);
        
        // 按月度聚合数据
        Map<String, BigDecimal> monthMap = EStream.of(dateList)
            .groupBy(ZiyanCvmLongtermPredictOutputScaleSplitDO::getYearMonthStr)
            .toMap(kv -> kv.getKey(), 
                   kv -> EStream.of(kv.getValue()).sum(ZiyanCvmLongtermPredictOutputScaleSplitDO::getPredictScale));
        EStream.of(dateColumList.getMonthDate())
            .map(month -> monthMap.getOrDefault(month, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP))
            .consumeAsList(item::setMonthPredictScale);
        
        return item;
    }

    /**
     * 获取季度名称
     * 根据拆分数据中的年份和季度信息生成季度名称
     * 
     * @param splitDO 拆分数据对象，包含年份和季度信息
     * @return String 季度名称，格式为"YYYYQX"（如：2024Q1）
     */
    private String getQuarterName(ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO) {
        return splitDO.getYear() + "Q" + splitDO.getQuarter();
    }

    /**
     * 获取半年名称
     * 根据拆分数据中的年份和半年信息生成半年名称
     * 
     * @param splitDO 拆分数据对象，包含年份和半年信息
     * @return String 半年名称，格式为"YYYYHX"（如：2024H1）
     */
    private String getHalfYearName(ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO) {
        return splitDO.getYear() + "H" + splitDO.getHalfYear();
    }

    /**
     * 获取年度名称
     * 根据拆分数据中的年份信息生成年度名称
     * 
     * @param splitDO 拆分数据对象，包含年份信息
     * @return String 年度名称，格式为"YYYY"（如：2024）
     */
    private String getYearName(ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO) {
        return splitDO.getYear().toString();
    }

    /**
     * 获取策略类型名称
     * 将英文策略类型代码转换为中文名称，用于前端展示
     * 
     * @param strategyType 策略类型代码（EXTREME/MIDDLE/CAUTIOUS）
     * @return String 策略类型中文名称（激进/中立/保守），如果未匹配则返回原代码
     */
    private String getStrategyTypeName(String strategyType) {
        // 简化的策略类型名称映射，实际可以从枚举或配置中获取
        switch (strategyType) {
            case "EXTREME": return "激进";
            case "MIDDLE": return "中立";
            case "CAUTIOUS": return "保守";
            default: return strategyType;
        }
    }



    /**
     * 执行数据拆分
     * 将预测规模数据按地域和设备类型进行两层拆分，生成详细的拆分结果并保存到数据库
     * 
     * @param outputScaleDOs 原始预测规模数据列表，包含各策略类型的预测核心数
     * @param task 长期预测任务对象，包含任务基础信息
     * @return List<ZiyanCvmLongtermPredictOutputScaleSplitDO> 拆分后的详细数据列表
     */
    @Override
    public List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitData(List<ZiyanCvmLongtermPredictInputScaleDO> inputScaleDOs,
                                                                     List<ZiyanCvmLongtermPredictOutputScaleDO> outputScaleDOs,
                                                                     ZiyanCvmLongtermPredictTaskDO task) {


        if (outputScaleDOs == null || outputScaleDOs.isEmpty()) {
            return Collections.emptyList();
        }

        List<ZiyanCvmLongtermPredictOutputScaleSplitDO> result = new ArrayList<>();
        List<ZiyanCvmLongtermPredictOutputScaleSplitTreeDO> treeNodes = new ArrayList<>();

        //1.获取地域占比
        Map<String, BigDecimal> regionRatioMap = getRegionRatioHistory(task);

        //2.获取机型族占比
        //Map<String, BigDecimal> instanceFamilyRatioMap = getInstanceFamilyRatioHistory();

        //3.获取实例类型占比
        //Map<String, BigDecimal> instanceTypeRatioMap = getInstanceTypeRatioHistory();

        //4.获取机型规格占比
        Map<String, BigDecimal> deviceTypeRatioMap = getDeviceTypeRatioHistory(inputScaleDOs);

        //5.保存拆分版本信息
        ZiyanCvmLongtermPredictOutputSplitVersionDO splitVersionDO = createSplitVersion(regionRatioMap, deviceTypeRatioMap, task);

        cdLabDbHelper.insert(splitVersionDO);

        //6.执行拆分（按层执行）,每拆一层保存到ZiyanCvmLongtermPredictOutputScaleSplitTreeDO中，并存入数据库
        for (ZiyanCvmLongtermPredictOutputScaleDO scaleDO : outputScaleDOs) {
            // 第一层：按地域拆分
            List<ZiyanCvmLongtermPredictOutputScaleSplitDO> regionSplitList = 
                splitByRegion(scaleDO, regionRatioMap);
            
            for (ZiyanCvmLongtermPredictOutputScaleSplitDO regionSplit : regionSplitList) {
                // 保存地域拆分到树结构
                ZiyanCvmLongtermPredictOutputScaleSplitTreeDO regionTreeNode = 
                    ZiyanCvmLongtermPredictOutputScaleSplitTreeDO.createFromSplitDO(regionSplit);
                treeNodes.add(regionTreeNode);
                
                // 第二层：按机型规格拆分
                List<ZiyanCvmLongtermPredictOutputScaleSplitDO> deviceSplitList = 
                    splitByDeviceType(regionSplit, deviceTypeRatioMap);
                
                for (ZiyanCvmLongtermPredictOutputScaleSplitDO deviceSplit : deviceSplitList) {
                    // 保存机型规格拆分到树结构
                    ZiyanCvmLongtermPredictOutputScaleSplitTreeDO deviceTreeNode = 
                        ZiyanCvmLongtermPredictOutputScaleSplitTreeDO.createFromSplitDO(deviceSplit);
                    treeNodes.add(deviceTreeNode);
                    
                    // 添加到最终结果
                    deviceSplit.setSplitVersionId(splitVersionDO.getId());
                    result.add(deviceSplit);
                }
            }
        }

        // 批量保存树节点到数据库
        if (!treeNodes.isEmpty()) {
            treeNodes.forEach(o->o.setSplitVersionId(splitVersionDO.getId()));
            cdLabDbHelper.insertBatchWithoutReturnId(treeNodes);
        }

        //7.拆分完成
        return result;
    }

    /**
     * 创建拆分版本记录
     * 根据地域占比和设备类型占比数据创建拆分版本记录，用于记录拆分参数和版本信息
     * 
     * @param regionRatioMap 地域占比映射，key为地域名称，value为占比
     * @param deviceTypeRatioMap 设备类型占比映射，key为设备类型，value为占比
     * @param task 长期预测任务对象，提供任务基础信息
     * @return ZiyanCvmLongtermPredictOutputSplitVersionDO 拆分版本记录对象
     */
    private ZiyanCvmLongtermPredictOutputSplitVersionDO createSplitVersion(Map<String, BigDecimal> regionRatioMap,
                                                                                 Map<String, BigDecimal> deviceTypeRatioMap,
                                                                                 ZiyanCvmLongtermPredictTaskDO task) {
        // 将两层拆分数据分别转换为JSON，避免混合存储

        // 分别存储地域占比和设备类型占比
        Map<String, Object> splitArgMap = new HashMap<>();
        splitArgMap.put("regionRatio", regionRatioMap);
        splitArgMap.put("deviceTypeRatio", deviceTypeRatioMap);

        String splitArgJson = JSON.toJson(splitArgMap);

        // 创建两层拆分版本记录
        ZiyanCvmLongtermPredictOutputSplitVersionDO splitVersion = new ZiyanCvmLongtermPredictOutputSplitVersionDO();
        splitVersion.setTaskId(task.getId());
        splitVersion.setCreator(task.getCreator());
        splitVersion.setBg("WXG"); // 根据业务设置
        splitVersion.setName("默认拆分");
        splitVersion.setNote("系统自动生成拆分版本");
        splitVersion.setSplitArg(splitArgJson);

        return splitVersion;
    }


    /**
     * 获取地域历史占比数据
     * 从历史数据中获取各地域的占比信息，用于地域维度的数据拆分
     * 当前使用测试数据，包含华东、华南、新加坡、香港四个地域
     * 
     * @return Map<String, BigDecimal> 地域占比映射，key为地域名称，value为占比（0-1之间的小数）
     */
    @SneakyThrows
    private Map<String, BigDecimal> getRegionRatioHistory(ZiyanCvmLongtermPredictTaskDO task) {
        String predictStart = task.getPredictStart().minusMonths(1).toString();
        String oneYearAgo = task.getPredictStart().minusMonths(1).minusYears(1).toString();
        Map<String,Object> param=  new HashMap<>();
        param.put("predictStart", predictStart);
        param.put("oneYearAgo", oneYearAgo);
        Map<String,BigDecimal> map= new HashMap<>();
        String sql = IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_region_ratio.sql");
        List<RegionRatioDTO> raw = ckcubesDBHelper.getRaw(RegionRatioDTO.class, sql, param);
        //筛选region包含华东、华南、新加坡、香港的数据
        for (RegionRatioDTO dto : raw) {
            if(dto.getRegionName().contains("华东")){
                dto.setRegionName("华东");
            }else if (dto.getRegionName().contains("华南")){
                dto.setRegionName("华南");
            }else if (dto.getRegionName().contains("新加坡")){
                dto.setRegionName("新加坡");
            }else if (dto.getRegionName().contains("香港")){
                dto.setRegionName("香港");
            }
        }
        //将raw按照regionname，stattime聚合，core累加
        Map<String, Map<String, BigDecimal>> regionTimeMap = new HashMap<>();
        
        // 按regionName和statTime分组聚合core
        for (RegionRatioDTO dto : raw) {
            String regionName = dto.getRegionName();
            String statTime = dto.getStatTime();
            BigDecimal core = dto.getCur_core();
            
            if (core != null && regionName != null && statTime != null) {
                regionTimeMap.computeIfAbsent(regionName, k -> new HashMap<>())
                    .merge(statTime, core, BigDecimal::add);
            }
        }
        
        // 计算各个regionName的增量（最新时间的core - 一年前的core）
        Map<String, BigDecimal> regionIncrementMap = new HashMap<>();
        BigDecimal totalIncrement = BigDecimal.ZERO;
        
        for (Map.Entry<String, Map<String, BigDecimal>> entry : regionTimeMap.entrySet()) {
            String regionName = entry.getKey();
            Map<String, BigDecimal> timeMap = entry.getValue();
            
            BigDecimal currentCore = timeMap.get(predictStart);
            BigDecimal previousCore = timeMap.get(oneYearAgo);
            
            if (currentCore != null && previousCore != null) {
                BigDecimal increment = currentCore.subtract(previousCore);
                // 只考虑正增长的地域
                if (increment.compareTo(BigDecimal.ZERO) > 0) {
                    regionIncrementMap.put(regionName, increment);
                    totalIncrement = totalIncrement.add(increment);
                }
            }
        }
        
        // 计算各地域的占比
        if (totalIncrement.compareTo(BigDecimal.ZERO) > 0) {
            for (Map.Entry<String, BigDecimal> entry : regionIncrementMap.entrySet()) {
                String regionName = entry.getKey();
                BigDecimal increment = entry.getValue();
                BigDecimal ratio = increment.divide(totalIncrement, 4, RoundingMode.HALF_UP);
                map.put(regionName, ratio);
            }
        } else {
            // 如果没有正增长，使用最新时间点的存量占比
            BigDecimal totalCurrent = BigDecimal.ZERO;
            Map<String, BigDecimal> currentCoreMap = new HashMap<>();
            
            for (Map.Entry<String, Map<String, BigDecimal>> entry : regionTimeMap.entrySet()) {
                String regionName = entry.getKey();
                BigDecimal currentCore = entry.getValue().get(predictStart);
                if (currentCore != null && currentCore.compareTo(BigDecimal.ZERO) > 0) {
                    currentCoreMap.put(regionName, currentCore);
                    totalCurrent = totalCurrent.add(currentCore);
                }
            }
            
            if (totalCurrent.compareTo(BigDecimal.ZERO) > 0) {
                for (Map.Entry<String, BigDecimal> entry : currentCoreMap.entrySet()) {
                    String regionName = entry.getKey();
                    BigDecimal currentCore = entry.getValue();
                    BigDecimal ratio = currentCore.divide(totalCurrent, 4, RoundingMode.HALF_UP);
                    map.put(regionName, ratio);
                }
            }
        }
        
        log.info("地域占比计算完成，共{}个地域: {}", map.size(), map);
        return map;
    }



    /**
     * 获取设备类型历史占比数据
     * 从历史数据中获取各设备类型的占比信息，用于设备类型维度的数据拆分
     * 设备类型格式为：机型大类@机型族@实例规格
     * 
     * @return Map<String, BigDecimal> 设备类型占比映射，key为设备类型（格式：机型大类@机型族@实例规格），value为占比
     */
    @SneakyThrows
    private Map<String, BigDecimal> getDeviceTypeRatioHistory(List<ZiyanCvmLongtermPredictInputScaleDO> inputScaleDOs) {
        // 如果输入数据为空，返回默认配置
        if (inputScaleDOs == null || inputScaleDOs.isEmpty()) {
            throw new RuntimeException("输入数据为空，无法获取设备类型占比");
        }
        // 第一步：根据scaleDO的比例算出机型族的占比
        Map<String, BigDecimal> instanceFamilyRatioMap = calculateInstanceFamilyRatio(inputScaleDOs);


        return instanceFamilyRatioMap;
    }
    
    /**
     * 计算机型族占比
     * 根据输入数据中各机型族的核心数计算占比
     */
    private Map<String, BigDecimal> calculateInstanceFamilyRatio(List<ZiyanCvmLongtermPredictInputScaleDO> inputScaleDOs) {
        //计算占比时，按照存量最新时间往前倒推一年的机型增量占比来计算
        
        if (inputScaleDOs == null || inputScaleDOs.isEmpty()) {
            return new HashMap<>();
        }
        
        // 1. 找到最新时间
        LocalDate maxDate = inputScaleDOs.stream()
            .map(ZiyanCvmLongtermPredictInputScaleDO::getStatTime)
            .filter(Objects::nonNull)
            .max(LocalDate::compareTo)
            .orElse(null);
            
        if (maxDate == null) {
            log.warn("输入数据中没有有效的统计时间");
            return new HashMap<>();
        }
        
        // 2. 计算一年前的时间
        LocalDate oneYearAgo = maxDate.minusYears(1);
        
        // 3. 筛选最近一年的数据
        List<ZiyanCvmLongtermPredictInputScaleDO> recentYearData = inputScaleDOs.stream()
            .filter(item -> item.getStatTime().equals(maxDate) || item.getStatTime().equals(oneYearAgo))
            .collect(Collectors.toList());
        
        if (recentYearData.isEmpty()) {
            log.warn("最近一年内没有有效数据");
            return new HashMap<>();
        }
        
        //计算出来存量最后一年的各个机型大类的增量
        
        // 4. 按机型族分组，计算每个机型族在最近一年的核心数变化
        Map<String, List<ZiyanCvmLongtermPredictInputScaleDO>> familyGroupMap = recentYearData.stream()
            .filter(item -> item.getInstanceFamily() != null)
            .collect(Collectors.groupingBy(ZiyanCvmLongtermPredictInputScaleDO::getInstanceFamily));
        
        Map<String, BigDecimal> familyIncrementMap = new HashMap<>();
        
        for (Map.Entry<String, List<ZiyanCvmLongtermPredictInputScaleDO>> entry : familyGroupMap.entrySet()) {
            String instanceFamily = entry.getKey();
            List<ZiyanCvmLongtermPredictInputScaleDO> familyData = entry.getValue();
            
            // 按时间排序
            familyData.sort(Comparator.comparing(ZiyanCvmLongtermPredictInputScaleDO::getStatTime));
            
            // 计算增量：最新值 - 最早值
            BigDecimal latestCore = familyData.stream().filter(o->o.getStatTime().equals(maxDate)).map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal earliestCore = familyData.stream().filter(o->o.getStatTime().equals(oneYearAgo)).map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal increment = latestCore.subtract(earliestCore);
            // 只考虑正增长的机型族
            if (increment.compareTo(BigDecimal.ZERO) > 0) {
                familyIncrementMap.put(instanceFamily, increment);
            }
        }
        
        //计算占比
        
        // 5. 计算总增量
        BigDecimal totalIncrement = familyIncrementMap.values().stream()
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        // 6. 计算各机型族占比
        Map<String, BigDecimal> ratioMap = new HashMap<>();
        
        if (totalIncrement.compareTo(BigDecimal.ZERO) > 0) {
            for (Map.Entry<String, BigDecimal> entry : familyIncrementMap.entrySet()) {
                String instanceFamily = entry.getKey();
                BigDecimal increment = entry.getValue();
                
                // 计算占比，保留4位小数
                BigDecimal ratio = increment.divide(totalIncrement, 4, RoundingMode.HALF_UP);
                ratioMap.put(instanceFamily, ratio);
            }
        }
        Map<String, BigDecimal> result = new HashMap<>();
        for (Map.Entry<String, BigDecimal> familyEntry : ratioMap.entrySet()) {
            String instanceFamily = familyEntry.getKey();
            BigDecimal familyRatio = familyEntry.getValue();

            // 对于高IO型特殊处理
            if (instanceFamily.equals("高IO型")) {
                // 高IO按ITA5t和非ITA5t分配，根据inputScaleDOs中的实际占比
                Map<String, BigDecimal> highIOSubRatio = calculateHighIOSubRatio(recentYearData,maxDate,oneYearAgo);
                for (Map.Entry<String, BigDecimal> subEntry : highIOSubRatio.entrySet()) {
                    String deviceTypeKey = subEntry.getKey();
                    BigDecimal subRatio = subEntry.getValue();
                    result.put(deviceTypeKey, familyRatio.multiply(subRatio));
                }
            } else {
                // 其他机型族按配置直接分配
                String deviceTypeKey = getDeviceTypeKeyByInstanceFamily(instanceFamily);
                result.put(deviceTypeKey, familyRatio);
            }
        }
        //返回占比
        return result;
    }

    
    /**
     * 计算高IO型的子类型占比（ITA5t和非ITA5t）
     * 根据inputScaleDOs中高IO的实例类型占比进行分配
     */
    private Map<String, BigDecimal> calculateHighIOSubRatio(List<ZiyanCvmLongtermPredictInputScaleDO> inputScaleDOs,LocalDate maxDate,LocalDate oneYearAgo) {
        Map<String, BigDecimal> subRatioMap = new HashMap<>();
        List<ZiyanCvmLongtermPredictInputScaleDO> highIOData = inputScaleDOs.stream().filter(o->o.getInstanceFamily().equals("高IO型")).collect(Collectors.toList());
        BigDecimal totalIncrease = highIOData.stream().map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal ITA5Increase= highIOData.stream().filter(o->o.getStatTime().equals(maxDate)).map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add)
                .subtract(highIOData.stream().filter(o->o.getStatTime().equals(oneYearAgo)).map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal nonITA5Increase=totalIncrease.subtract(ITA5Increase);
        subRatioMap.put("高IO型@ITA5t@58XLARGE836",ITA5Increase.divide(totalIncrease,4,RoundingMode.HALF_UP));
        subRatioMap.put("高IO型@I9@72XLARGE1152",nonITA5Increase.divide(totalIncrease,4,RoundingMode.HALF_UP));
        return subRatioMap;
    }
    
    /**
     * 根据机型族获取设备类型key
     * 机型族按照配置直接分配，如果配置中一个机型族只有一个实例类型，那就全部分配
     */
    private String getDeviceTypeKeyByInstanceFamily(String instanceFamily) {

        if (instanceFamily.equals("高密度热存储")){
            return "高密度热存储@I6@33XLARGE688";
        } else if (instanceFamily.equals("内存型")){
            return "内存型@I6@32XLARGE1952-hs";
        } else if (instanceFamily.equals("大数据型")) {
            return "大数据型@DA5@12XLARGE144";
        } else if(instanceFamily.equals("标准型")){
            return "标准型@SA9@48XLARGE512";
        }else return null;
    }
    

    /**
     * 按地域拆分数据
     * 将单个预测规模数据按地域占比进行拆分，生成多个地域维度的拆分数据
     * 
     * @param scaleDO 原始预测规模数据，包含总的预测核心数
     * @param regionRatioMap 地域占比映射，定义各地域的分配比例
     * @return List<ZiyanCvmLongtermPredictOutputScaleSplitDO> 按地域拆分后的数据列表
     */
    private List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitByRegion(
            ZiyanCvmLongtermPredictOutputScaleDO scaleDO, 
            Map<String, BigDecimal> regionRatioMap) {
        
        List<ZiyanCvmLongtermPredictOutputScaleSplitDO> result = new ArrayList<>();
        
        if (regionRatioMap.isEmpty()) {
            log.warn("按地域拆分数据失败：没有地域信息");
        }
        
        for (Map.Entry<String, BigDecimal> entry : regionRatioMap.entrySet()) {
            ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO = createBaseSplitDO(scaleDO);
            splitDO.setRegionName(entry.getKey());
            
            // 按占比分配预测规模
            BigDecimal splitScale = scaleDO.getCurCore().multiply(entry.getValue());
            splitDO.setPredictScale(splitScale);
            
            result.add(splitDO);
        }
        
        return result;
    }



    /**
     * 按机型规格拆分数据
     * 将地域拆分后的数据进一步按设备类型占比进行拆分，生成最终的详细拆分数据
     * 解析设备类型格式：机型大类@机型族@实例规格，并分别设置到对应字段
     * 
     * @param parentSplit 父级拆分数据（已按地域拆分），包含地域信息和预测规模
     * @param deviceTypeRatioMap 设备类型占比映射，定义各设备类型的分配比例
     * @return List<ZiyanCvmLongtermPredictOutputScaleSplitDO> 按设备类型拆分后的最终数据列表
     */
    private List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitByDeviceType(
            ZiyanCvmLongtermPredictOutputScaleSplitDO parentSplit, 
            Map<String, BigDecimal> deviceTypeRatioMap) {
        
        List<ZiyanCvmLongtermPredictOutputScaleSplitDO> result = new ArrayList<>();
        
        if (deviceTypeRatioMap.isEmpty()) {
            log.warn("按机型规格拆分数据失败：没有机型规格");
            return result;
        }
        
        for (Map.Entry<String, BigDecimal> entry : deviceTypeRatioMap.entrySet()) {
            ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO = copySplitDO(parentSplit);
            
            // 解析机型大类@机型族@实例规格格式的字符串
            String deviceTypeKey = entry.getKey();
            String[] parts = deviceTypeKey.split("@");
            
            if (parts.length >= 3) {
                // 按照机型大类@机型族@实例规格的顺序设置
                splitDO.setInstanceFamily(parts[0]);    // 机型大类
                splitDO.setInstanceType(parts[1]);  // 机型族
                splitDO.setDeviceType(parts[2]);      // 实例规格
            } else {
                // 如果格式不正确，使用原始字符串作为deviceType
                splitDO.setDeviceType(deviceTypeKey);
            }
            
            // 按占比分配预测规模
            BigDecimal splitScale = parentSplit.getPredictScale().multiply(entry.getValue());
            splitDO.setPredictScale(splitScale);
            
            result.add(splitDO);
        }
        
        return result;
    }

    /**
     * 从原始数据创建基础拆分对象
     * 将原始预测规模数据转换为拆分数据对象，复制基础字段并计算时间相关字段
     * 
     * @param scaleDO 原始预测规模数据，包含任务ID、事业群、策略类型等基础信息
     * @return ZiyanCvmLongtermPredictOutputScaleSplitDO 基础拆分数据对象，包含复制的基础字段和计算的时间字段
     */
    private ZiyanCvmLongtermPredictOutputScaleSplitDO createBaseSplitDO(ZiyanCvmLongtermPredictOutputScaleDO scaleDO) {
        ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO = new ZiyanCvmLongtermPredictOutputScaleSplitDO();
        
        // 复制基础字段
        splitDO.setTaskId(scaleDO.getTaskId());
        splitDO.setBg(scaleDO.getBg());
        splitDO.setStrategyType(scaleDO.getStrategyType());
        splitDO.setDate(scaleDO.getStatTime());
        splitDO.setYearMonthStr(scaleDO.getYearMonthStr());
        
        // 设置时间相关字段
        if (scaleDO.getStatTime() != null) {
            splitDO.setYear(scaleDO.getStatTime().getYear());
            splitDO.setQuarter((scaleDO.getStatTime().getMonthValue() - 1) / 3 + 1);
            splitDO.setHalfYear(scaleDO.getStatTime().getMonthValue() <= 6 ? 1 : 2);
        }
        
        return splitDO;
    }

    /**
     * 复制拆分对象
     * 深度复制拆分数据对象的所有字段，用于在多层拆分过程中保持数据完整性
     * 
     * @param source 源拆分数据对象，包含所有需要复制的字段
     * @return ZiyanCvmLongtermPredictOutputScaleSplitDO 复制后的新拆分数据对象
     */
    private ZiyanCvmLongtermPredictOutputScaleSplitDO copySplitDO(ZiyanCvmLongtermPredictOutputScaleSplitDO source) {
        ZiyanCvmLongtermPredictOutputScaleSplitDO target = new ZiyanCvmLongtermPredictOutputScaleSplitDO();
        
        // 复制所有字段
        target.setTaskId(source.getTaskId());
        target.setBg(source.getBg());
        target.setStrategyType(source.getStrategyType());
        target.setDate(source.getDate());
        target.setYear(source.getYear());
        target.setYearMonthStr(source.getYearMonthStr());
        target.setQuarter(source.getQuarter());
        target.setHalfYear(source.getHalfYear());
        target.setRegionName(source.getRegionName());
        target.setInstanceFamily(source.getInstanceFamily());
        target.setInstanceType(source.getInstanceType());
        target.setDeviceType(source.getDeviceType());
        target.setPredictScale(source.getPredictScale());
        
        return target;
    }

    /**
     * 构建WXG日期列信息
     * 根据拆分数据列表构建各时间维度的日期列信息，包括季度、半年、年度、月度四个维度
     * 每个维度都提供普通版本和严格版本，严格版本会显示实际的月份范围
     * 
     * @param list 拆分数据列表，包含各时间点的数据
     * @return WxgDateColumList 日期列信息对象，包含各时间维度的日期列表
     */
    private QueryWxgDeviceTypeSplitDetailResp.WxgDateColumList buildWxgDateColumList(List<ZiyanCvmLongtermPredictOutputScaleSplitDO> list) {
        QueryWxgDeviceTypeSplitDetailResp.WxgDateColumList dateColumList = new QueryWxgDeviceTypeSplitDetailResp.WxgDateColumList();

        // 构建季度日期列
        EStream.of(list).map(this::getQuarterName).distinct().sorted()
                .consumeAsList(dateColumList::setQuarterDateNotStrict);

        // 构建半年日期列
        EStream.of(list).map(this::getHalfYearName).distinct().sorted()
                .consumeAsList(dateColumList::setHalfYearDateNotStrict);

        // 构建年度日期列
        EStream.of(list).map(this::getYearName).distinct().sorted()
                .consumeAsList(dateColumList::setYearDateNotStrict);

        // 构建月度日期列
        EStream.of(list).map(ZiyanCvmLongtermPredictOutputScaleSplitDO::getYearMonthStr).distinct().sorted()
                .consumeAsList(dateColumList::setMonthDate);

        // 构建严格的季度日期列（带月份范围）
        EStream.of(list).groupBy(this::getQuarterName).map(kv -> {
            List<YearMonth> ym = EStream.of(kv.getValue())
                    .map(o -> YearMonth.of(o.getYear(), o.getDate().getMonthValue()))
                    .distinct().sorted().toList();
            if (ym.size() != 3) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList(dateColumList::setQuarterDate);

        // 构建严格的半年日期列（带月份范围）
        EStream.of(list).groupBy(this::getHalfYearName).map(kv -> {
            List<YearMonth> ym = EStream.of(kv.getValue())
                    .map(o -> YearMonth.of(o.getYear(), o.getDate().getMonthValue()))
                    .distinct().sorted().toList();
            if (ym.size() != 6) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList(dateColumList::setHalfYearDate);

        // 构建严格的年度日期列（带月份范围）
        EStream.of(list).groupBy(this::getYearName).map(kv -> {
            List<YearMonth> ym = EStream.of(kv.getValue())
                    .map(o -> YearMonth.of(o.getYear(), o.getDate().getMonthValue()))
                    .distinct().sorted().toList();
            if (ym.size() != 12) {
                int minMonth = ym.get(0).getMonthValue();
                int maxMonth = ym.get(ym.size() - 1).getMonthValue();
                String monthRange = minMonth == maxMonth ? minMonth + "月" : minMonth + "-" + maxMonth + "月";
                return kv.getKey() + "(" + monthRange + ")";
            }
            return kv.getKey();
        }).sorted().consumeAsList(dateColumList::setYearDate);

        return dateColumList;
    }

}