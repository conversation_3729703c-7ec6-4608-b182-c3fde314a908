package cloud.demand.lab.modules.ziyan_longterm.wxg.service.impl;

import cloud.demand.lab.modules.ziyan_longterm.wxg.algorithm.OLS;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DailyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DemandTotalDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.ReturnTotalDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.*;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.ScaleHistoryAndPredictService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.ScheduledQueryService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.utils.QuarterUtils;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.HalfYearAccumulateReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.PredictTotalReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryPredictResultByTaskReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.queryScaleHistoryReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.*;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 规模历史和预测服务实现类
 * 提供规模历史数据查询、预测数据查询、需求总量统计等功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ScaleHistoryAndPredictServiceImpl implements ScaleHistoryAndPredictService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private DBHelper yuntidemandDBHelper;

    @Resource
    private ScheduledQueryService scheduledQueryService;

    // ==================== 规模历史数据查询相关方法 ====================

    /**
     * 查询带机型的历史规模数据
     * 
     * @param req 查询请求，包含任务ID
     * @return 历史规模数据响应，包含按机型分组的核数和占比
     */
    @Override
    public queryScaleHistoryResp queryScaleHistoryWithInstance(queryScaleHistoryReq req) {
        Long taskId = req.getTaskId();
        List<ZiyanCvmLongtermPredictInputScaleDO> all = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictInputScaleDO.class, 
            "where task_id = ?", 
            taskId
        );
        
        if (all == null || all.isEmpty()) {
            return new queryScaleHistoryResp();
        }
        // 按年月+机型分组，对curCore进行求和
        Map<String, Map<String, BigDecimal>> groupedByYearMonth = all.stream()
            .collect(Collectors.groupingBy(
                ZiyanCvmLongtermPredictInputScaleDO::getYearMonthStr,
                Collectors.groupingBy(
                    ZiyanCvmLongtermPredictInputScaleDO::getInstanceFamily,
                    Collectors.reducing(BigDecimal.ZERO,
                        ZiyanCvmLongtermPredictInputScaleDO::getCurCore,
                        BigDecimal::add)
                )
            ));

        // 计算每个年月的总量，用于计算比例
        Map<String, BigDecimal> monthlyTotals = groupedByYearMonth.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().values().stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
            ));

        // 转换为响应对象
        List<queryScaleHistoryResp.Item> items = groupedByYearMonth.entrySet().stream()
            .flatMap(yearMonthEntry -> {
                String yearMonth = yearMonthEntry.getKey();
                BigDecimal monthTotal = monthlyTotals.get(yearMonth);

                return yearMonthEntry.getValue().entrySet().stream()
                    .map(instanceEntry -> {
                        queryScaleHistoryResp.Item item = new queryScaleHistoryResp.Item();
                        item.setDate(yearMonth);
                        item.setInstanceFamily(instanceEntry.getKey());
                        item.setTotalCore(instanceEntry.getValue());

                        // 计算比例，保留4位小数
                        BigDecimal rate = BigDecimal.ZERO;
                        if (monthTotal.compareTo(BigDecimal.ZERO) > 0) {
                            rate = instanceEntry.getValue()
                                .divide(monthTotal, 4, RoundingMode.HALF_UP);
                        }
                        item.setRate(rate);

                        return item;
                    });
            })
            .sorted((a, b) -> {
                // 首先按年月排序
                int yearMonthCompare = a.getDate().compareTo(b.getDate());
                if (yearMonthCompare != 0) {
                    return yearMonthCompare;
                }
                // 年月相同时，按机型核心数从大到小排序
                return b.getTotalCore().compareTo(a.getTotalCore());
            })
            .collect(Collectors.toList());

        queryScaleHistoryResp resp = new queryScaleHistoryResp();
        items.forEach(item -> item.setDate(item.getDate() + "-01"));
        resp.setData(items);
        return resp;
    }


    /**
     * 查询历史规模数据
     * 
     * @param req 查询请求，包含任务ID和预测月份等参数
     * @return 历史规模数据响应，包含按月聚合的总核数、环比增量、线性拟合参数等
     */
    @Override
    public queryScaleHistoryResp queryScaleHistory(queryScaleHistoryReq req) {
        Long taskId = req.getTaskId();
        List<ZiyanCvmLongtermPredictInputScaleDO> all = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictInputScaleDO.class, 
            "where task_id = ?", 
            taskId
        );
        
        if (all == null || all.isEmpty()) {
            return new queryScaleHistoryResp();
        }

        // 按年月分组，对curCore进行求和
        Map<String, BigDecimal> groupedData = all.stream()
            .collect(Collectors.groupingBy(
                ZiyanCvmLongtermPredictInputScaleDO::getYearMonthStr,
                Collectors.reducing(BigDecimal.ZERO,
                    ZiyanCvmLongtermPredictInputScaleDO::getCurCore,
                    BigDecimal::add)
            ));

        // 转换为响应对象并按年月排序
        List<queryScaleHistoryResp.Item> items = groupedData.entrySet().stream()
            .map(entry -> {
                queryScaleHistoryResp.Item item = new queryScaleHistoryResp.Item();
                item.setDate(entry.getKey());
                item.setTotalCore(entry.getValue());
                return item;
            })
            .sorted(Comparator.comparing(queryScaleHistoryResp.Item::getDate))
            .collect(Collectors.toList());

        // 计算increaseFromLastMonth字段（环比增量：当前月相比上个月的变化量）
        // 注意：此计算必须在筛选之前进行，确保所有月份数据的完整性
        for (int i = 0; i < items.size(); i++) {
            if (i == 0) {
                // 第一个月没有上个月数据作为基准，设置为null
                items.get(i).setIncreaseFromLastMonth(null);
            } else {
                // 计算环比增量：当前月总核数 - 上个月总核数
                BigDecimal currentCore = items.get(i).getTotalCore();
                BigDecimal lastMonthCore = items.get(i - 1).getTotalCore();
                BigDecimal increase = currentCore.subtract(lastMonthCore);
                items.get(i).setIncreaseFromLastMonth(increase);
            }
        }

        // 如果指定了predictMonth，则筛选对应的历史线性拟合月份
        if (req.getPredictMonth() != null && !req.getPredictMonth().trim().isEmpty()) {
            items = filterHistoryByPredictMonth(items, req.getPredictMonth());
        }
        
        items.forEach(item -> item.setDate(item.getDate() + "-01"));
        queryScaleHistoryResp resp = new queryScaleHistoryResp();
        resp.setData(items);

        // 计算年增速（只有当predictMonth为空时才计算）
        if (req.getPredictMonth() == null || req.getPredictMonth().trim().isEmpty()) {
            List<queryScaleHistoryResp.IncreaseRateItem> increaseRateData = calculateYearlyIncreaseRateFromData(items);
            resp.setIncreaseRateData(increaseRateData);
        }

        // 计算线性拟合参数（斜率a、截距b、决定系数r²）
        if (items != null && items.size() >= 2&&req.getPredictMonth()!=null&& !req.getPredictMonth().isEmpty()) {
            OLS.LinearRegressionResult regressionResult = calculateLinearRegression(items);
            resp.setSlope(regressionResult.getSlope());
            resp.setIntercept(regressionResult.getIntercept());
            resp.setR2(regressionResult.getR2());
        } else {
            // 数据点不足，设置默认值
            resp.setSlope(null);
            resp.setIntercept(null);
            resp.setR2(null);
        }

        return resp;
    }

    // ==================== 规模预测数据查询相关方法 ====================

    /**
     * 查询规模预测数据
     * 
     * @param req 查询请求，包含任务ID和预测月份等参数
     * @return 预测规模数据响应，包含按策略类型分组的预测数据
     */
    @Override
    public queryScalePredictResp queryScalePredict(queryScaleHistoryReq req) {
        queryScalePredictResp resp = new queryScalePredictResp();
        List<ZiyanCvmLongtermPredictOutputScaleDO> all = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictOutputScaleDO.class, 
            "where task_id = ?", 
            req.getTaskId()
        );
        
        if (all == null || all.isEmpty()) {
            return new queryScalePredictResp();
        }

        // 按年月+策略类型分组，对curCore进行求和
        Map<String, Map<String, BigDecimal>> coreGrouped = all.stream()
            .collect(Collectors.groupingBy(
                ZiyanCvmLongtermPredictOutputScaleDO::getYearMonthStr,
                Collectors.groupingBy(
                    ZiyanCvmLongtermPredictOutputScaleDO::getStrategyType,
                    Collectors.reducing(BigDecimal.ZERO,
                        ZiyanCvmLongtermPredictOutputScaleDO::getCurCore,
                        BigDecimal::add)
                )
            ));

        // 同时收集changeCurCoreFromLastMonth的聚合数据
        Map<String, Map<String, BigDecimal>> increaseGrouped = all.stream()
            .collect(Collectors.groupingBy(
                ZiyanCvmLongtermPredictOutputScaleDO::getYearMonthStr,
                Collectors.groupingBy(
                    ZiyanCvmLongtermPredictOutputScaleDO::getStrategyType,
                    Collectors.reducing(BigDecimal.ZERO,
                        data -> data.getChangeCurCoreFromLastMonth() != null ? 
                            data.getChangeCurCoreFromLastMonth() : BigDecimal.ZERO,
                        BigDecimal::add)
                )
            ));

        // 转换为响应对象
        // 首先按年月排序，年月相同时按策略类型排序
        List<queryScalePredictResp.Item> items = coreGrouped.entrySet().stream()
            .flatMap(yearMonthEntry -> {
                String yearMonth = yearMonthEntry.getKey();
                return yearMonthEntry.getValue().entrySet().stream()
                    .map(strategyEntry -> {
                        queryScalePredictResp.Item item = new queryScalePredictResp.Item();
                        item.setDate(yearMonth);
                        item.setStrategyType(strategyEntry.getKey());
                        item.setTotalCore(strategyEntry.getValue());

                        // 设置increaseFromLastMonth字段
                        BigDecimal increase = increaseGrouped.get(yearMonth).get(strategyEntry.getKey());
                        item.setIncreaseFromLastMonth(increase);

                        return item;
                    });
            })
            .sorted(Comparator.comparing(queryScalePredictResp.Item::getDate)
                .thenComparing(queryScalePredictResp.Item::getStrategyType))
            .collect(Collectors.toList());

        // 如果指定了predictMonth，则筛选对应Q值月份的数据
        if (req.getPredictMonth() != null && !req.getPredictMonth().trim().isEmpty()) {
            items = filterPredictByQuarter(items, req.getPredictMonth());
        }

        // 计算年增速数据
        if (req.getPredictMonth() == null || req.getPredictMonth().trim().isEmpty()) {
            List<queryScalePredictResp.IncreaseRateItem> increaseRateData = 
                calculateYearlyIncreaseRate(req.getTaskId(), coreGrouped);
            resp.setIncreaseRateData(increaseRateData);
        }

        items.forEach(item -> item.setDate(item.getDate() + "-01"));
        resp.setData(items);
        return resp;
    }

    // ==================== 需求累计查询相关方法 ====================

    /**
     * 查询半年累计需求数据
     * 
     * @param req 查询请求，包含任务ID
     * @return 半年累计需求响应，包含各策略类型的半年、一年、一年半需求量
     */
    @Override
    public HalfYearAccumulateResp QueryHalfYearAccumulate(HalfYearAccumulateReq req) {
        Long taskId = req.getTaskId();

        // 1. 获取任务信息，获得预测开始日期
        ZiyanCvmLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new RuntimeException("任务不存在，taskId: " + taskId);
        }

        LocalDate predictStart = taskDO.getPredictStart();

        // 2. 计算基准日期（预测开始前一个月）
        LocalDate baselineDate = predictStart.minusMonths(1);
        String baselineDateStr = baselineDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 3. 查询基准月份的存量数据
        List<ZiyanCvmLongtermPredictInputScaleDO> baselineData = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictInputScaleDO.class,
            "where task_id = ? and year_month_str = ?",
            taskId, baselineDateStr
        );

        // 计算基准存量
        BigDecimal baselineStock = baselineData.stream()
            .map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 4. 查询预测数据
        List<ZiyanCvmLongtermPredictOutputScaleDO> predictData = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictOutputScaleDO.class,
            "where task_id = ?",
            taskId
        );

        if (predictData == null || predictData.isEmpty()) {
            throw new RuntimeException("预测数据不存在，taskId: " + taskId);
        }

        // 5. 按策略类型+年月分组求和预测数据
        Map<String, Map<String, BigDecimal>> predictByStrategyAndMonth = predictData.stream()
            .collect(Collectors.groupingBy(
                ZiyanCvmLongtermPredictOutputScaleDO::getStrategyType,
                Collectors.groupingBy(
                    ZiyanCvmLongtermPredictOutputScaleDO::getYearMonthStr,
                    Collectors.reducing(BigDecimal.ZERO,
                        ZiyanCvmLongtermPredictOutputScaleDO::getCurCore,
                        BigDecimal::add)
                )
            ));

        // 6. 计算未来半年、一年、一年半的目标日期
        LocalDate halfYearTarget = predictStart.plusMonths(5);
        LocalDate oneYearTarget = predictStart.plusMonths(11);
        LocalDate oneAndHalfYearTarget = predictStart.plusMonths(17);

        String halfYearTargetStr = halfYearTarget.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String oneYearTargetStr = oneYearTarget.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        String oneAndHalfYearTargetStr = oneAndHalfYearTarget.format(DateTimeFormatter.ofPattern("yyyy-MM"));

        // 7. 按策略类型计算需求量
        List<HalfYearAccumulateResp.StrategyDemandItem> strategyDemandList = 
            predictByStrategyAndMonth.entrySet().stream()
                .map(strategyEntry -> {
                    String strategyType = strategyEntry.getKey();
                    Map<String, BigDecimal> monthlyData = strategyEntry.getValue();

                    // 获取目标月份的预测存量
                    BigDecimal halfYearStock = monthlyData.getOrDefault(halfYearTargetStr, BigDecimal.ZERO);
                    BigDecimal oneYearStock = monthlyData.getOrDefault(oneYearTargetStr, BigDecimal.ZERO);
                    BigDecimal oneAndHalfYearStock = monthlyData.getOrDefault(oneAndHalfYearTargetStr, BigDecimal.ZERO);

                    // 计算净需求量（目标存量 - 基准存量）
                    BigDecimal halfYearDemand = halfYearStock.subtract(baselineStock);
                    BigDecimal oneYearDemand = oneYearStock.subtract(baselineStock);
                    BigDecimal oneAndHalfYearDemand = oneAndHalfYearStock.subtract(baselineStock);

                    HalfYearAccumulateResp.StrategyDemandItem item = new HalfYearAccumulateResp.StrategyDemandItem();
                    item.setStrategyType(strategyType);
                    item.setHalfYearDemand(halfYearDemand);
                    item.setOneYearDemand(oneYearDemand);
                    item.setOneAndHalfYearDemand(oneAndHalfYearDemand);

                    return item;
                })
                .sorted(Comparator.comparing(HalfYearAccumulateResp.StrategyDemandItem::getStrategyType))
                .collect(Collectors.toList());

        // 8. 构建响应对象
        HalfYearAccumulateResp resp = new HalfYearAccumulateResp();
        resp.setStrategyDemandList(strategyDemandList);
        resp.setPredictStartDate(predictStart.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        resp.setBaselineDate(baselineDateStr);
        resp.setBaselineStock(baselineStock);

        // 设置各时间段的起始和终止年月
        resp.setHalfYearStartDate(predictStart.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        resp.setHalfYearEndDate(halfYearTargetStr);

        resp.setOneYearStartDate(predictStart.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        resp.setOneYearEndDate(oneYearTargetStr);

        resp.setOneAndHalfYearStartDate(predictStart.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        resp.setOneAndHalfYearEndDate(oneAndHalfYearTargetStr);

        return resp;
    }

    /**
     * 查询预测总量数据
     * 
     * @param req 查询请求，包含任务ID
     * @return 预测总量响应，包含各年度各策略的净增量和进度信息
     */
    @Override
    public PredictTotalResp QueryPredictTotal(PredictTotalReq req) {
        Long taskId = req.getTaskId();

        // 1. 获取任务信息，获得预测开始日期
        ZiyanCvmLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new RuntimeException("任务不存在，taskId: " + taskId);
        }

        LocalDate predictStart = taskDO.getPredictStart();

        // 2. 确定当年、次年、后年的年份
        int currentYear = predictStart.getYear();
        int nextYear = currentYear + 1;
        int thirdYear = currentYear + 2;
        int fourthYear = currentYear + 3;

        // 3. 构建各年1月1日的日期字符串
        String currentYearJan = currentYear + "-01";
        String nextYearJan = nextYear + "-01";
        String thirdYearJan = thirdYear + "-01";
        String fourthYearJan = fourthYear + "-01";

        // 4. 查询存量数据（当年1月1日）
        List<ZiyanCvmLongtermPredictInputScaleDO> currentYearStockData = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictInputScaleDO.class,
            "where task_id = ? and year_month_str = ?",
            taskId, currentYearJan
        );
        // 计算当年1月1日存量
        BigDecimal currentYearStock = currentYearStockData.stream()
            .map(ZiyanCvmLongtermPredictInputScaleDO::getCurCore)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 5. 从cubes数据库查询最新一天的数据
        DailyCoreDTO lastDayStockDto = scheduledQueryService.getLastDayStock();
        String latestStockDay = lastDayStockDto.getDay().toString();
        BigDecimal latestStockTotal = lastDayStockDto.getSumCore();
        // 6. 查询预测数据
        List<ZiyanCvmLongtermPredictOutputScaleDO> predictData = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictOutputScaleDO.class,
            "where task_id = ? and year_month_str in (?, ?, ?)",
            taskId, nextYearJan, thirdYearJan, fourthYearJan
        );
        if (predictData == null || predictData.isEmpty()) {
            throw new RuntimeException("预测数据不存在，taskId: " + taskId);
        }

        // 7. 按策略类型+年月分组求和预测数据
        Map<String, Map<String, BigDecimal>> predictByStrategyAndMonth = predictData.stream()
            .collect(Collectors.groupingBy(
                ZiyanCvmLongtermPredictOutputScaleDO::getStrategyType,
                Collectors.groupingBy(
                    ZiyanCvmLongtermPredictOutputScaleDO::getYearMonthStr,
                    Collectors.reducing(BigDecimal.ZERO,
                        ZiyanCvmLongtermPredictOutputScaleDO::getCurCore,
                        BigDecimal::add)
                )
            ));

        // 8. 计算各年度各策略的净增量
        // 当年净增 = 次年1月1日数据 - 当年1月1日数据
        // 次年净增 = 后年1月1日数据 - 次年1月1日数据
        // 后年净增 = 第四年1月1日数据 - 后年1月1日数据
        Map<String, BigDecimal> currentYearNetIncreaseByStrategy = new java.util.HashMap<>();
        Map<String, BigDecimal> nextYearNetIncreaseByStrategy = new java.util.HashMap<>();
        Map<String, BigDecimal> thirdYearNetIncreaseByStrategy = new java.util.HashMap<>();

        for (Map.Entry<String, Map<String, BigDecimal>> strategyEntry : predictByStrategyAndMonth.entrySet()) {
            String strategyType = strategyEntry.getKey();
            Map<String, BigDecimal> monthlyData = strategyEntry.getValue();

            // 获取各年1月1日的预测存量
            BigDecimal nextYearStock = monthlyData.getOrDefault(nextYearJan, BigDecimal.ZERO);
            BigDecimal thirdYearStock = monthlyData.getOrDefault(thirdYearJan, BigDecimal.ZERO);
            BigDecimal fourthYearStock = monthlyData.getOrDefault(fourthYearJan, BigDecimal.ZERO);

            // 计算年度净增量
            currentYearNetIncreaseByStrategy.put(strategyType, nextYearStock.subtract(currentYearStock));
            nextYearNetIncreaseByStrategy.put(strategyType, thirdYearStock.subtract(nextYearStock));
            thirdYearNetIncreaseByStrategy.put(strategyType, fourthYearStock.subtract(thirdYearStock));
        }

        // 9. 构建按年份分组的响应数据
        List<PredictTotalResp.WxgYearItem> wxgYearItems = java.util.Arrays.asList(
            createWxgYearItem(currentYear, currentYearNetIncreaseByStrategy, currentYearStock, latestStockTotal, latestStockDay, currentYear),
            createWxgYearItem(nextYear, nextYearNetIncreaseByStrategy, currentYearStock, latestStockTotal, latestStockDay, currentYear),
            createWxgYearItem(thirdYear, thirdYearNetIncreaseByStrategy, currentYearStock, latestStockTotal, latestStockDay, currentYear)
        );

        // 10. 构建响应对象
        PredictTotalResp resp = new PredictTotalResp();
        resp.setWxgYearItems(wxgYearItems);

        // 11. 查询业务提报与执行
        resp = queryDemandTotal(currentYear, resp);
        resp = queryBufferTotal(currentYear, resp);
        return resp;
    }

    // ==================== 私有工具方法 ====================

    /**
     * 查询缓冲总量数据
     * 
     * @param currentYear 当年
     * @param resp 响应对象
     * @return 更新后的响应对象
     */
    @SneakyThrows
    private PredictTotalResp queryBufferTotal(int currentYear, PredictTotalResp resp) {
        List<DemandTotalDTO> demandBuffer = scheduledQueryService.getDemandBuffer(currentYear);
        Map<Integer, DemandTotalDTO> demandTotalDTOMap = 
            ListUtils.toMap(demandBuffer, DemandTotalDTO::getYear, dto -> dto);
        
        List<ReturnTotalDTO> returnBuffer = scheduledQueryService.getReturnBuffer(currentYear);
        Map<Integer, ReturnTotalDTO> returnPlanDTOMap = 
            ListUtils.toMap(returnBuffer, ReturnTotalDTO::getYear, dto -> dto);


        if (resp != null && resp.getWxgYearItems() != null) {
            for (PredictTotalResp.WxgYearItem yearItem : resp.getWxgYearItems()) {
                Integer year = yearItem.getYear();
                DemandTotalDTO demandTotalDTO = demandTotalDTOMap.getOrDefault(year, null);
                ReturnTotalDTO returnPlanDTO = returnPlanDTOMap.getOrDefault(year, null);

                // 设置缓冲需求
                if (demandTotalDTO != null) {
                    yearItem.getWxgStrategyItems().forEach(item -> 
                        item.setBufferDemand(demandTotalDTO.getNotFinishedSum()));
                } else {
                    yearItem.getWxgStrategyItems().forEach(item -> 
                        item.setBufferDemand(BigDecimal.ZERO));
                }

                // 设置缓冲退回
                if (returnPlanDTO != null) {
                    yearItem.getWxgStrategyItems().forEach(item -> 
                        item.setBufferReturn(returnPlanDTO.getDemandReturnTotal()));
                } else {
                    yearItem.getWxgStrategyItems().forEach(item -> 
                        item.setBufferReturn(BigDecimal.ZERO));
                }

                // 计算缓冲总量
                yearItem.getWxgStrategyItems().forEach(item -> 
                    item.setBufferTotal(item.getBufferDemand().subtract(item.getBufferReturn())));
            }
        }
        return resp;
    }


    /**
     * 根据算法查询预测结果
     * 
     * @param req 查询请求，包含任务ID
     * @return 按算法分组的预测结果数据
     */
    @Override
    public QueryPredictResultByTaskResp queryPredictResultByAlgorithm(QueryPredictResultByTaskReq req) {
        Long taskId = req.getTaskId();

        // 查询指定任务的预测结果数据，只查询bg为wxg的数据
        List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictResults = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO.class,
            "where task_id = ? and bg = ?",
            taskId, "wxg"
        );

        if (predictResults == null || predictResults.isEmpty()) {
            QueryPredictResultByTaskResp resp = new QueryPredictResultByTaskResp();
            resp.setData(new ArrayList<>());
            return resp;
        }

        // 按算法类型分组聚合数据
        Map<String, List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO>> groupedByAlgorithm =
            predictResults.stream()
                .collect(Collectors.groupingBy(
                    ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO::getAlgorithm
                ));

        // 转换为响应对象
        List<QueryPredictResultByTaskResp.AlgorithmResultItem> algorithmItems = groupedByAlgorithm.entrySet().stream()
                .map(algorithmEntry -> {
                    String algorithm = algorithmEntry.getKey();
                    List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> algorithmData = algorithmEntry.getValue();

                    // 按年月分组并聚合数据
                    Map<String, List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO>> groupedByYearMonth =
                            algorithmData.stream()
                                    .collect(Collectors.groupingBy(
                                            ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO::getYearMonthStr
                                    ));

                    // 转换为预测数据项
                    List<QueryPredictResultByTaskResp.PredictDataItem> predictDataItems = groupedByYearMonth.entrySet().stream()
                            .map(yearMonthEntry -> {
                                String yearMonth = yearMonthEntry.getKey();
                                List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> yearMonthData = yearMonthEntry.getValue();

                                // 对同一年月的数据进行聚合
                                BigDecimal totalCurCore = yearMonthData.stream()
                                    .map(ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO::getCurCore)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                                BigDecimal totalChangeCore = yearMonthData.stream()
                                    .map(ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO::getChangeCurCoreFromLastMonth)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                                QueryPredictResultByTaskResp.PredictDataItem dataItem =
                                        new QueryPredictResultByTaskResp.PredictDataItem();
                                dataItem.setYearMonthStr(yearMonth);
                                dataItem.setCurCore(totalCurCore);
                                dataItem.setChangeCurCoreFromLastMonth(totalChangeCore);

                                return dataItem;
                            })
                            .sorted(Comparator.comparing(QueryPredictResultByTaskResp.PredictDataItem::getYearMonthStr))
                            .collect(Collectors.toList());

                    QueryPredictResultByTaskResp.AlgorithmResultItem algorithmItem =
                            new QueryPredictResultByTaskResp.AlgorithmResultItem();
                    algorithmItem.setAlgorithm(algorithm);
                    algorithmItem.setPredictData(predictDataItems);

                    return algorithmItem;
                })
                .sorted(Comparator.comparing(QueryPredictResultByTaskResp.AlgorithmResultItem::getAlgorithm))
                .collect(Collectors.toList());

        QueryPredictResultByTaskResp resp = new QueryPredictResultByTaskResp();
        resp.setData(algorithmItems);
        return resp;
    }


    /**
     * 创建年度数据项
     * 
     * @param year 年份
     * @param netIncreaseByStrategy 按策略类型分组的净增量
     * @param currentYearStock 当年存量
     * @param latestStockTotal 最新存量总量
     * @param latestStockDay   最新存量日期
     * @param predictStartYear 预测开始年份
     * @return 年度数据项
     */
    private PredictTotalResp.WxgYearItem createWxgYearItem(Integer year, Map<String, BigDecimal> netIncreaseByStrategy,
                                                          BigDecimal currentYearStock, BigDecimal latestStockTotal, String latestStockDay, Integer predictStartYear) {
        PredictTotalResp.WxgYearItem yearItem = new PredictTotalResp.WxgYearItem();
        yearItem.setYear(year);

        List<PredictTotalResp.WxgStrategyItem> strategyItems = netIncreaseByStrategy.entrySet().stream()
            .map(entry -> {
                PredictTotalResp.WxgStrategyItem strategyItem = new PredictTotalResp.WxgStrategyItem();
                strategyItem.setStrategyType(entry.getKey());
                strategyItem.setIncreasePredictTotal(entry.getValue());

                // 只有预测当年才计算净增进度相关字段
                if (year.equals(predictStartYear)) {
                    // 计算净增相减量（存量最后一个月1日数据 - 当年1月1日数据）
                    BigDecimal increasePredictTotalProgress = latestStockTotal.subtract(currentYearStock);
                    strategyItem.setIncreasePredictTotalProgress(increasePredictTotalProgress);

                    // 计算净增相减进度（相减的量/预测总量）
                    BigDecimal increasePredictTotalProgressRatio = BigDecimal.ZERO;
                    if (entry.getValue().compareTo(BigDecimal.ZERO) != 0) {
                        increasePredictTotalProgressRatio = increasePredictTotalProgress
                            .divide(entry.getValue(), 4, RoundingMode.HALF_UP);
                    }
                    strategyItem.setIncreasePredictTotalProgressRatio(increasePredictTotalProgressRatio);

                    // 设置分别相减的值和对应日期
                    strategyItem.setLatestStockValue(latestStockTotal);
                    strategyItem.setCurrentYearStockValue(currentYearStock);
                    strategyItem.setLatestStockDate(latestStockDay);
                    strategyItem.setCurrentYearStockDate(year + "-01-01");
                } else {
                    // 非预测当年，设置为null
                    strategyItem.setIncreasePredictTotalProgress(null);
                    strategyItem.setIncreasePredictTotalProgressRatio(null);
                    strategyItem.setLatestStockValue(null);
                    strategyItem.setCurrentYearStockValue(null);
                    strategyItem.setLatestStockDate(null);
                    strategyItem.setCurrentYearStockDate(null);
                }

                return strategyItem;
            })
            .sorted(Comparator.comparing(PredictTotalResp.WxgStrategyItem::getStrategyType))
            .collect(Collectors.toList());

        yearItem.setWxgStrategyItems(strategyItems);
        return yearItem;
    }

    // ==================== 数据筛选和计算工具方法 ====================

    /**
     * 根据预测月份筛选对应的历史线性拟合月份
     * 如所在月在26年Q1返回24Q1-Q4的历史数据，在26年Q2返回24Q2-25Q1的历史数据
     * 
     * @param items 历史数据项列表
     * @param predictMonth 预测月份
     * @return 筛选后的历史数据项列表
     */
    private List<queryScaleHistoryResp.Item> filterHistoryByPredictMonth(List<queryScaleHistoryResp.Item> items, String predictMonth) {
        try {
            QuarterUtils.Quarter predictQuarter = QuarterUtils.getQuarter(predictMonth);
            String lastActualMonth = getLastActualMonthFromHistory(items);

            if (lastActualMonth == null) {
                return getOriginalHistoryFilterResult(predictQuarter, items);
            }

            // 计算预测季度对应的12个月窗口
            TimeWindow predictWindow = calculateTwelveMonthWindow(predictQuarter);
            YearMonth lastActualYM = YearMonth.parse(lastActualMonth, DateTimeFormatter.ofPattern("yyyy-MM"));

            // 判断窗口是否超出真实数据范围
            if (predictWindow.getEndMonth().isAfter(lastActualYM)) {
                // 寻找最近可用的完整窗口
                Set<String> fallbackMonths = findLatestValidWindow(predictQuarter, lastActualYM);
                if (fallbackMonths.isEmpty()) {
                    return getOriginalHistoryFilterResult(predictQuarter, items);
                }
                return filterItemsByMonths(items, fallbackMonths);
            } else {
                // 窗口在真实数据范围内，使用原逻辑
                return getOriginalHistoryFilterResult(predictQuarter, items);
            }

        } catch (Exception e) {
            // 如果解析失败，返回原始数据
            return items;
        }
    }

    /**
     * 根据预测月份筛选对应Q值月份的预测数据
     * 返回该月份所在季度的所有预测数据
     */
    private List<queryScalePredictResp.Item> filterPredictByQuarter(List<queryScalePredictResp.Item> items, String predictMonth) {
        try {
            // 解析预测月份，获取对应的季度
            QuarterUtils.Quarter predictQuarter = QuarterUtils.getQuarter(predictMonth);

            // 收集该季度的所有月份
            Set<String> quarterMonths = predictQuarter.getMonthList().stream()
                    .map(QuarterUtils.MonthInfo::getMonthStr)
                    .collect(Collectors.toSet());

            // 筛选匹配的预测数据
            return items.stream()
                    .filter(item -> quarterMonths.contains(item.getDate()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            // 如果解析失败，返回原始数据
            return items;
        }
    }

    /**
     * 从历史数据中获取最后真实月份
     */
    private String getLastActualMonthFromHistory(List<queryScaleHistoryResp.Item> items) {
        if (items == null || items.isEmpty()) {
            return null;
        }

        // 从历史数据中找到最大的年月
        return items.stream()
                .map(queryScaleHistoryResp.Item::getDate)
                .max(String::compareTo)
                .orElse(null);
    }

    /**
     * 获取原始历史过滤结果（原逻辑）
     */
    private List<queryScaleHistoryResp.Item> getOriginalHistoryFilterResult(QuarterUtils.Quarter predictQuarter, List<queryScaleHistoryResp.Item> items) {
        Set<String> historyMonths = calculateHistoryMonthsByQuarters(predictQuarter);
        return filterItemsByMonths(items, historyMonths);
    }

    /**
     * 根据季度标识获取季度开始月份
     */
    private int quarterStartMonth(String quarter) {
        switch (quarter) {
            case "Q1": return 1;
            case "Q2": return 4;
            case "Q3": return 7;
            case "Q4": return 10;
            default: throw new IllegalArgumentException("Invalid quarter: " + quarter);
        }
    }

    /**
     * 时间窗口类，用于表示12个月的时间范围
     */
    @Getter
    private static class TimeWindow {
        private final YearMonth startMonth;
        private final YearMonth endMonth;

        public TimeWindow(YearMonth startMonth, YearMonth endMonth) {
            this.startMonth = startMonth;
            this.endMonth = endMonth;
        }

        /**
         * 生成窗口内所有月份的字符串集合
         */
        public Set<String> generateMonthStrings() {
            Set<String> months = new HashSet<>();
            YearMonth current = startMonth;
            while (!current.isAfter(endMonth)) {
                months.add(current.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                current = current.plusMonths(1);
            }
            return months;
        }
    }

    /**
     * 计算预测季度对应的12个月窗口（预测季度-2年开始）
     */
    private TimeWindow calculateTwelveMonthWindow(QuarterUtils.Quarter predictQuarter) {
        int startMonth = quarterStartMonth(predictQuarter.getQuarter());
        YearMonth winStart = YearMonth.of(predictQuarter.getYear() - 2, startMonth);
        YearMonth winEnd = winStart.plusMonths(11);
        return new TimeWindow(winStart, winEnd);
    }

    /**
     * 寻找最近可用的完整12个月窗口
     */
    private Set<String> findLatestValidWindow(QuarterUtils.Quarter predictQuarter, YearMonth lastActualYM) {
        QuarterUtils.Quarter currentQuarter = predictQuarter;

        while (currentQuarter != null) {
            TimeWindow window = calculateTwelveMonthWindow(currentQuarter);

            // 如果窗口完全在真实数据范围内，返回该窗口的月份
            if (!window.getEndMonth().isAfter(lastActualYM)) {
                return window.generateMonthStrings();
            }

            // 回溯到上一个季度
            currentQuarter = QuarterUtils.getLastQuarter(currentQuarter.getYear(), currentQuarter.getQuarter());

            // 防止无限回溯
            if (currentQuarter.getYear() < lastActualYM.getYear() - 3) {
                break;
            }
        }

        return new HashSet<>();
    }

    /**
     * 通过季度计算历史月份集合（原逻辑的提取）
     */
    private Set<String> calculateHistoryMonthsByQuarters(QuarterUtils.Quarter predictQuarter) {
        // 计算历史拟合窗口：预测年份-2的对应季度开始，连续12个月
        int historyStartYear = predictQuarter.getYear() - 2;
        String historyStartQuarter = predictQuarter.getQuarter();

        // 获取历史拟合窗口的起始季度
        QuarterUtils.Quarter startQuarter = new QuarterUtils.Quarter(historyStartYear, historyStartQuarter);

        // 获取连续4个季度（12个月）的历史数据
        List<QuarterUtils.Quarter> historyQuarters = new ArrayList<>();
        QuarterUtils.Quarter current = startQuarter;
        for (int i = 0; i < 4; i++) {
            historyQuarters.add(current);
            current = QuarterUtils.getNextQuarter(current.getYear(), current.getQuarter());
        }

        // 收集所有历史月份
        Set<String> historyMonths = new HashSet<>();
        for (QuarterUtils.Quarter quarter : historyQuarters) {
            for (QuarterUtils.MonthInfo monthInfo : quarter.getMonthList()) {
                historyMonths.add(monthInfo.getMonthStr());
            }
        }

        return historyMonths;
    }

    /**
     * 根据月份集合筛选历史数据项
     */
    private List<queryScaleHistoryResp.Item> filterItemsByMonths(List<queryScaleHistoryResp.Item> items, Set<String> targetMonths) {
        return items.stream()
                .filter(item -> targetMonths.contains(item.getDate()))
                .collect(Collectors.toList());
    }

    /**
     * 计算线性回归参数
     * @param items 历史数据点
     * @return 线性回归结果
     */
    private OLS.LinearRegressionResult calculateLinearRegression(List<queryScaleHistoryResp.Item> items) {
        if (items == null || items.size() < 2) {
            return new OLS.LinearRegressionResult(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
        // 将年月字符串转换为数值（从1开始的序号）
        List<Integer> xs = new ArrayList<>();
        List<Double> ys = new ArrayList<>();

        for (int i = 0; i < items.size(); i++) {
            xs.add(i + 1); // x值从1开始
            ys.add(items.get(i).getTotalCore().doubleValue()); // y值为核心数
        }

        // 使用OLS工具类计算线性回归结果
        return OLS.calculateLinearRegression(xs, ys);
    }

    /**
     * 计算年增速数据
     * @param taskId 任务ID
     * @param predictCoreGrouped 预测数据按年月+策略类型分组的数据
     * @return 年增速数据列表
     */
    private List<queryScalePredictResp.IncreaseRateItem> calculateYearlyIncreaseRate(Long taskId,
                                                                                    Map<String, Map<String, BigDecimal>> predictCoreGrouped) {
        List<queryScalePredictResp.IncreaseRateItem> increaseRateItems = new ArrayList<>();

        try {
            // 1. 获取所有预测数据的年份
            Set<Integer> predictYears = predictCoreGrouped.keySet().stream()
                    .map(yearMonth -> Integer.parseInt(yearMonth.substring(0, 4)))
                    .collect(Collectors.toSet());

            if (predictYears.isEmpty()) {
                return increaseRateItems;
            }

            // 2. 获取最小年份作为基准年，需要查询存量数据
            Integer minYear = predictYears.stream().min(Integer::compareTo).orElse(null);
            if (minYear == null) {
                return increaseRateItems;
            }

            // 3. 查询存量数据，获取基准年及之前年份的1月数据
            List<ZiyanCvmLongtermPredictInputScaleDO> stockData = cdLabDbHelper.getAll(
                ZiyanCvmLongtermPredictInputScaleDO.class,
                "where task_id = ? and year_month_str like '%-01'",
                taskId
            );

            // 4. 按年月分组存量数据
            Map<String, BigDecimal> stockDataByYearMonth = stockData.stream()
                    .collect(Collectors.groupingBy(
                            ZiyanCvmLongtermPredictInputScaleDO::getYearMonthStr,
                            Collectors.reducing(BigDecimal.ZERO,
                                    ZiyanCvmLongtermPredictInputScaleDO::getCurCore,
                                    BigDecimal::add)
                    ));

            // 5. 按年月分组预测数据（只取1月数据）
            Map<String, BigDecimal> predictDataByYearMonth = predictCoreGrouped.entrySet().stream()
                    .filter(entry -> entry.getKey().endsWith("-01")) // 只取1月数据
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue().values().stream()
                                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    ));

            // 6. 合并存量数据和预测数据
            Map<String, BigDecimal> allJanuaryData = new java.util.HashMap<>(stockDataByYearMonth);
            allJanuaryData.putAll(predictDataByYearMonth);

            // 7. 计算每年的增速
            List<Integer> sortedYears = predictYears.stream().sorted().collect(Collectors.toList());

            for (Integer currentYear : sortedYears) {
                String currentYearJan = currentYear + "-01";
                String previousYearJan = (currentYear - 1) + "-01";

                BigDecimal currentYearData = allJanuaryData.get(currentYearJan);
                BigDecimal previousYearData = allJanuaryData.get(previousYearJan);

                if (currentYearData != null && previousYearData != null &&
                    previousYearData.compareTo(BigDecimal.ZERO) > 0) {

                    // 计算增速：(次年1月数据 - 当年1月数据) / 当年1月数据
                    BigDecimal increaseRate = currentYearData.subtract(previousYearData)
                            .divide(previousYearData, 4, RoundingMode.HALF_UP);

                    queryScalePredictResp.IncreaseRateItem rateItem = new queryScalePredictResp.IncreaseRateItem();
                    rateItem.setStartDate(previousYearJan + "-01");
                    rateItem.setEndDate(currentYearJan + "-01");
                    rateItem.setIncreaseRate(increaseRate);

                    increaseRateItems.add(rateItem);
                }
            }

        } catch (Exception e) {
            // 如果计算过程中出现异常，返回空列表
            log.info("计算年增速时发生异常: " + e.getMessage());
        }

        return increaseRateItems.stream()
                .sorted(Comparator.comparing(queryScalePredictResp.IncreaseRateItem::getStartDate))
                .collect(Collectors.toList());
    }

    /**
     * 基于已有的历史数据计算年增速
     * @param items 历史数据项列表（已按时间排序）
     * @return 年增速数据列表
     */
    private List<queryScaleHistoryResp.IncreaseRateItem> calculateYearlyIncreaseRateFromData(List<queryScaleHistoryResp.Item> items) {
        List<queryScaleHistoryResp.IncreaseRateItem> increaseRateItems = new ArrayList<>();

        try {
            if (items == null || items.isEmpty()) {
                return increaseRateItems;
            }

            // 1. 筛选出1月份的数据（日期格式为 "yyyy-MM-01"）
            List<queryScaleHistoryResp.Item> januaryItems = items.stream()
                    .filter(item -> item.getDate() != null && item.getDate().endsWith("-01-01"))
                    .sorted(Comparator.comparing(queryScaleHistoryResp.Item::getDate))
                    .collect(Collectors.toList());

            // 2. 计算每年1月相对于去年1月的增速
            for (int i = 1; i < januaryItems.size(); i++) {
                queryScaleHistoryResp.Item currentYearItem = januaryItems.get(i);
                queryScaleHistoryResp.Item previousYearItem = januaryItems.get(i - 1);

                BigDecimal currentYearData = currentYearItem.getTotalCore();
                BigDecimal previousYearData = previousYearItem.getTotalCore();

                // 确保数据有效且前一年数据不为0
                if (currentYearData != null && previousYearData != null && 
                    previousYearData.compareTo(BigDecimal.ZERO) > 0) {

                    // 计算增速：(当年1月数据 - 去年1月数据) / 去年1月数据
                    BigDecimal increaseRate = currentYearData.subtract(previousYearData)
                            .divide(previousYearData, 4, RoundingMode.HALF_UP);

                    queryScaleHistoryResp.IncreaseRateItem rateItem = new queryScaleHistoryResp.IncreaseRateItem();
                    rateItem.setStartDate(previousYearItem.getDate());
                    rateItem.setEndDate(currentYearItem.getDate());
                    rateItem.setIncreaseRate(increaseRate);

                    increaseRateItems.add(rateItem);
                }
            }

        } catch (Exception e) {
            // 如果计算过程中出现异常，返回空列表
            log.info("计算历史数据年增速时发生异常: " + e.getMessage());
        }

        return increaseRateItems.stream()
                .sorted(Comparator.comparing(queryScaleHistoryResp.IncreaseRateItem::getStartDate))
                .collect(Collectors.toList());
    }

    /**
     * 查询需求总量数据
     * 
     * @param currentYear 当年
     * @param resp 响应对象
     * @return 更新后的响应对象
     */
    @SneakyThrows
    public PredictTotalResp queryDemandTotal(Integer currentYear, PredictTotalResp resp) {

        // 执行需求总量查询
        List<DemandTotalDTO> demandResult = scheduledQueryService.getDemandTotal(currentYear);

        // 执行退库总量查询
        List<ReturnTotalDTO> returnResult = scheduledQueryService.getReturnTotal(currentYear);

        // 执行计划外退回查询
        List<ReturnTotalDTO> outPlanReturnResult= scheduledQueryService.getOutPlanReturnTotal(currentYear);

        // 将需求数据按年份分组
        Map<Integer, DemandTotalDTO> demandByYear = demandResult.stream()
            .collect(Collectors.toMap(DemandTotalDTO::getYear, dto -> dto, (existing, replacement) -> existing));

        // 将退库数据按年份分组
        Map<Integer, ReturnTotalDTO> returnByYear = returnResult.stream()
            .collect(Collectors.toMap(ReturnTotalDTO::getYear, dto -> dto, (existing, replacement) -> existing));

        //  将计划外退回数据按年份分组
        Map<Integer, ReturnTotalDTO> outPlanReturnByYear = outPlanReturnResult.stream()
            .collect(Collectors.toMap(ReturnTotalDTO::getYear, dto -> dto, (existing, replacement) -> existing));

        // 在resp的对应字段中设置数据
        if (resp != null && resp.getWxgYearItems() != null) {
            for (PredictTotalResp.WxgYearItem yearItem : resp.getWxgYearItems()) {
                Integer year = yearItem.getYear();

                // 获取对应年份的需求数据和退库数据
                DemandTotalDTO demandData = demandByYear.get(year);
                ReturnTotalDTO returnData = returnByYear.get(year);
                ReturnTotalDTO outPlanReturnData = outPlanReturnByYear.get(year);

                // 如果没有数据，使用默认值0
                BigDecimal demandNetTotal = demandData != null ? demandData.getDemandNetTotal() : BigDecimal.ZERO;
                BigDecimal executeNetTotal = demandData != null ? demandData.getExecuteNetTotal() : BigDecimal.ZERO;
                //重点：业务要求：业务提报的退回数据需要加上计划外的已执行
                BigDecimal demandReturnTotal = returnData != null ? returnData.getDemandReturnTotal().add(outPlanReturnData != null ? outPlanReturnData.getExecuteReturnTotal() : BigDecimal.ZERO) : BigDecimal.ZERO;
                BigDecimal executeReturnTotal = returnData != null ? returnData.getExecuteReturnTotal() : BigDecimal.ZERO;

                // 计算总量 = 净需求 - 退回
                BigDecimal demandTotal = demandNetTotal.subtract(demandReturnTotal);
                BigDecimal executeTotal = executeNetTotal.subtract(executeReturnTotal);

                yearItem.getWxgStrategyItems().forEach(item -> {
                    item.setDemandTotal(demandTotal);
                    item.setExecuteTotal(executeTotal);
                    item.setDemandNetTotal(demandNetTotal);
                    item.setDemandReturnTotal(demandReturnTotal);
                    item.setExecuteNetTotal(executeNetTotal);
                    item.setExecuteReturnTotal(executeReturnTotal);
                    item.setDemandTotalProgressRatio(
                        demandTotal.divide(item.getIncreasePredictTotal(), 4, RoundingMode.HALF_UP));
                    item.setExecuteTotalProgressRatio(
                        executeTotal.divide(item.getIncreasePredictTotal(), 4, RoundingMode.HALF_UP));
                });
            }
        }
        return resp;
    }

    /**
     * 查询带机型的规模预测数据
     * 
     * @param req 查询请求，包含任务ID
     * @return 带机型的预测规模数据响应
     */
    @Override
    public queryScalePredictResp queryScalePredictWithInstance(queryScaleHistoryReq req) {
        List<ZiyanCvmLongtermPredictOutputSplitVersionDO> splitDOs = cdLabDbHelper.getAll(
            ZiyanCvmLongtermPredictOutputSplitVersionDO.class, 
            "where task_id = ?", 
            req.getTaskId()
        );
        
        if (splitDOs == null || splitDOs.size() != 1) {
            throw new RuntimeException("queryScalePredictWithInstance接口数据异常,多个拆分版本");
        }
        
        Long splitVersionId = splitDOs.get(0).getId();
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("split_version_id = ?", splitVersionId);
        whereSQL.and("task_id = ?", req.getTaskId());
        whereSQL.and("bg = ?", "WXG");
        whereSQL.addGroupBy("instance_type");
        whereSQL.addGroupBy("year_month_str");
        whereSQL.addGroupBy("strategy_type");
        List<ZiyanCvmLongtermPredictOutputScaleSplitDO> all = cdLabDbHelper.getAll(ZiyanCvmLongtermPredictOutputScaleSplitDO.class, whereSQL.getSQL(), whereSQL.getParams());

        queryScalePredictResp resp = new queryScalePredictResp();
        for (ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO : all) {
            queryScalePredictResp.Item item = new queryScalePredictResp.Item();
            item.setInstanceFamily(splitDO.getInstanceFamily());
            item.setDate(splitDO.getYearMonthStr() + "-01");
            item.setStrategyType(splitDO.getStrategyType());
            item.setTotalCore(splitDO.getPredictScale());
            resp.getData().add(item);
        }
        return resp;
    }

}