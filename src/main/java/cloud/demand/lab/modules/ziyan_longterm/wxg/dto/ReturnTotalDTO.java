package cloud.demand.lab.modules.ziyan_longterm.wxg.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 退库总量 DTO
 * 用于映射 wxg_cvm_query_return_total.sql 查询结果
 */
@Data
public class ReturnTotalDTO {
    
    /**
     * 提报退回总量
     */
    @Column("demand_return_total")
    private BigDecimal demandReturnTotal;
    
    /**
     * 已执行退库总量
     */
    @Column("execute_return_total")
    private BigDecimal executeReturnTotal;
    
    /**
     * 年份
     */
    @Column("year")
    private Integer year;

    /**
     * 获取退回提报总量
     * @return
     */
    public BigDecimal getDemandReturnTotal(){
        if(demandReturnTotal == null){
            return BigDecimal.ZERO;
        }
        return demandReturnTotal;
    }

    /**
     * 获取已执行退回总量
     */
    public BigDecimal getExecuteReturnTotal(){
        if(executeReturnTotal == null){
            return BigDecimal.ZERO;
        }
        return executeReturnTotal;
    }
}