package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QueryPredictResultByTaskResp {

    /** 按算法类型聚合的预测结果数据 */
    private List<AlgorithmResultItem> data;

    @Data
    public static class AlgorithmResultItem {
        
        /** 算法名称，例如ARIMA、linregress、Prophet */
        private String algorithm;
        
        /** 该算法下的预测数据列表 */
        private List<PredictDataItem> predictData;
    }
    
    @Data
    public static class PredictDataItem {
        
        /** 年月表达式 */
        private String yearMonthStr;
        
        /** 预测出来的当前存量(单位:核) */
        private BigDecimal curCore;
        
        /** 对比上个月的增量核心(单位:核) */
        private BigDecimal changeCurCoreFromLastMonth;
    }
}