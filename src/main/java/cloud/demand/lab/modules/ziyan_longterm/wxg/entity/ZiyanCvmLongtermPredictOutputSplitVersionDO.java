package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;


import lombok.Data;
import lombok.ToString;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_output_split_version")
public class ZiyanCvmLongtermPredictOutputSplitVersionDO extends BaseDO {

    @Column(value = "creator")
    private String creator;

    @Column(value = "task_id")
    private Long taskId;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    @Column(value = "name")
    private String name;

    @Column(value = "note")
    private String note;

    @Column(value = "split_arg")
    private String splitArg;

    @Column(value = "change_log")
    private String changeLog;

}