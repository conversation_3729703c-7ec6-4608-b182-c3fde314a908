package cloud.demand.lab.modules.ziyan_longterm.wxg.utils;

import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.dto.InputArgDateRangeDTO;
import com.pugwoo.wooutils.lang.DateUtils;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

/**
 * wxg日期范围工具类
 */
public class DateRangeUtils {

    /**都以月初为表示*/
    public static LocalDate getPredictStartDate(ZiyanCvmLongtermPredictCategoryConfigDO categoryConfigDO) {
        if ("CUR_MONTH".equals(categoryConfigDO.getPredictStart())) {
            return LocalDate.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        } else {
            LocalDate startDate = DateUtils.parseLocalDate(categoryConfigDO.getPredictStart());
            if (startDate == null) {
                return LocalDate.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth()); // 默认当月
            }
            return startDate.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        }
    }

    /**都以月初为表示*/
    public static LocalDate getPredictEndDate(ZiyanCvmLongtermPredictCategoryConfigDO categoryConfigDO) {
        return DateUtils.parseLocalDate(categoryConfigDO.getPredictEnd()).plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
    }

    /**
     * 获取日期范围列表，按照间隔月份分组
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param intervalMonth 间隔月份
     * @return 日期范围列表
     */
    public static List<InputArgDateRangeDTO> getDateRange(LocalDate startDate, LocalDate endDate, Integer intervalMonth) {
        if (intervalMonth == 6) { // 每半年作为1个录入周期
            if (startDate.getMonthValue() >= 7) {
                startDate = LocalDate.of(startDate.getYear(), 6, 30);
            } else {
                startDate = LocalDate.of(startDate.getYear() - 1, 12, 31);
            }
            if (endDate.getMonthValue() >= 7) {
                endDate = LocalDate.of(endDate.getYear(), 12, 31);
            } else {
                endDate = LocalDate.of(endDate.getYear(), 6, 30);
            }

            List<InputArgDateRangeDTO> ranges = new ArrayList<>();

            while (startDate.isBefore(endDate)) {
                InputArgDateRangeDTO range = new InputArgDateRangeDTO();
                if (startDate.getMonthValue() == 6) {
                    range.setDateName(startDate.getYear() + "下半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(rangeStartDate);
                    range.setEndDate(rangeStartDate.plusMonths(6).minusDays(1));
                } else {
                    range.setDateName((startDate.getYear() + 1) + "上半年");
                    LocalDate rangeStartDate = startDate.plusDays(1);
                    range.setStartDate(rangeStartDate);
                    range.setEndDate(rangeStartDate.plusMonths(6).minusDays(1));
                }
                ranges.add(range);

                startDate = startDate.plusDays(1).plusMonths(6).minusDays(1);
            }

            return ranges;
        } else if (intervalMonth == 12) { // 每年作为1个录入周期
            startDate = LocalDate.of(startDate.getYear(), 1, 1);
            endDate = LocalDate.of(endDate.getYear(), 12, 31);
            List<InputArgDateRangeDTO> ranges = new ArrayList<>();
            while (startDate.isBefore(endDate)) {
                InputArgDateRangeDTO range = new InputArgDateRangeDTO();
                range.setDateName(startDate.getYear() + "年");
                range.setStartDate(startDate);
                range.setEndDate(LocalDate.of(startDate.getYear(), 12, 31));
                ranges.add(range);

                startDate = startDate.plusYears(1);
            }
            return ranges;
        } else {
            return new ArrayList<>();
        }
    }
}