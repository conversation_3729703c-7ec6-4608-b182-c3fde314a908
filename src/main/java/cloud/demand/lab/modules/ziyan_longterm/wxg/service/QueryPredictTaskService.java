package cloud.demand.lab.modules.ziyan_longterm.wxg.service;

import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QuerySplitVersionReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryTaskInfoReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QuerySplitVersionResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryTaskInfoResp;

/**
 * 查询WXG中长期预测任务信息
 */
public interface QueryPredictTaskService {

    /**
     * 查询已经存在的WXG预测任务列表
     * 根据查询条件获取WXG预测任务的分类和任务列表信息
     * @param req 查询请求参数，包含筛选条件如时间范围、任务状态等
     * @return 分类和任务列表响应结果，包含任务基本信息和分类信息
     */
    QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req);

    /**
     * 查询任务详细信息
     * 根据任务ID获取预测任务的详细配置和执行状态信息
     * @param req 查询任务信息的请求参数，包含任务ID
     * @return 任务详细信息响应结果，包含任务配置、执行状态、预测结果等
     */
    QueryTaskInfoResp queryTaskInfo(QueryTaskInfoReq req);

    /**
     * 查询WXG拆分版本信息
     * 根据任务ID和拆分版本ID获取拆分配置的详细信息
     * @param req 查询请求参数，包含taskId（任务ID）和splitVersionId（拆分版本ID）
     * @return 拆分版本信息响应结果，包含拆分规则、比例配置等详细信息
     */
    QuerySplitVersionResp querySplitVersion(QuerySplitVersionReq req);
}
