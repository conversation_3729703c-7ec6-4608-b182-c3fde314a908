package cloud.demand.lab.modules.ziyan_longterm.wxg.service;

import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleSplitDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictTaskDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryWxgDeviceTypeSplitDetailReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryWxgDeviceTypeSplitDetailResp;

import java.util.List;

/**
 * WXG CVM拆分服务接口
 * 提供WXG云服务器拆分相关的业务功能
 */
public interface WxgCvmSplitService {

    /**
     * 执行数据拆分操作
     * 根据输入的规模数据、输出规模数据和任务信息，执行拆分逻辑
     * @param scaleDO 输入规模数据列表
     * @param outputScaleDO 输出规模数据列表
     * @param task 预测任务信息
     * @return 拆分后的输出规模数据列表
     */
    List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitData(List<ZiyanCvmLongtermPredictInputScaleDO> scaleDO,
                                                              List<ZiyanCvmLongtermPredictOutputScaleDO> outputScaleDO,
                                                              ZiyanCvmLongtermPredictTaskDO task);
    
    /**
     * 查询WXG设备类型拆分详情
     * 根据查询条件获取设备类型的拆分详细信息
     * @param req 查询请求参数，包含查询条件
     * @return 设备类型拆分详情响应数据
     */
    QueryWxgDeviceTypeSplitDetailResp queryWxgDeviceTypeSplitDetail(QueryWxgDeviceTypeSplitDetailReq req);
}
