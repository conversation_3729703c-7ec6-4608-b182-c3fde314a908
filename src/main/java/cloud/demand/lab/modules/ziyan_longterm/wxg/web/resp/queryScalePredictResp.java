package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class queryScalePredictResp {
    private List<Item> data=new ArrayList<>();
    
    // 年增速数据
    private List<IncreaseRateItem> increaseRateData=new ArrayList<>();

    @Data
    public static class Item{
        private String date;

        private String strategyType;

        private String instanceFamily;

        private BigDecimal totalCore;

        private BigDecimal increaseFromLastMonth;

    }

    //年增速
    @Data
    public static class IncreaseRateItem{
        private String startDate;
        private String endDate;
        private BigDecimal increaseRate;
    }

}
