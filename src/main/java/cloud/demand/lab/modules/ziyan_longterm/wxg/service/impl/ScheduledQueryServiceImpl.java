package cloud.demand.lab.modules.ziyan_longterm.wxg.service.impl;

import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DailyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DemandTotalDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.ReturnTotalDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.ScheduledQueryService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.io.IOUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 定时查询服务实现类
 * 提供各种定时任务相关的数据查询功能实现
 * 包括库存查询、需求数据查询、退还数据查询等
 */
@Service
@Slf4j
public class ScheduledQueryServiceImpl implements ScheduledQueryService {

    @Resource
    private DBHelper ckcubesDBHelper;

    @Resource
    private DBHelper yuntidemandDBHelper;

    /**
     * 获取最近一天的库存数据
     * 从ClickHouse数据库查询最新的库存信息，支持缓存机制
     * @return 最近一天的核心库存数据，如果没有数据则返回null
     */
    @Override
    @SneakyThrows
    @HiSpeedCache(expireSecond = 1200, continueFetchSecond = 3600)
    public DailyCoreDTO getLastDayStock() {
        String sql= IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_query_last_day_stock.sql");
        DailyCoreDTO raw = ckcubesDBHelper.getRawOne(DailyCoreDTO.class, sql);
        if (raw==null){
            log.warn("没有找到最近一天的库存数据");
            return null;
        }
        return raw;
    }

    /**
     * 获取指定年份的需求总量数据
     * 查询从当前年份到第三年的需求数据，支持缓存机制
     * @param currentYear 查询的起始年份
     * @return 需求总量数据列表，如果没有数据则返回空列表
     */
    @Override
    @SneakyThrows
    @HiSpeedCache(expireSecond = 1200, continueFetchSecond = 3600,keyScript = "args[0]")
    public List<DemandTotalDTO> getDemandTotal(Integer currentYear) {
        // 读取需求总量 SQL 文件
        String sql1 = IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_query_demand_total.sql");
        int thirdYear=currentYear+2;
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("current_year", currentYear);
        params.put("third_year", thirdYear);
        // 执行需求总量查询
        List<DemandTotalDTO> demandResult = yuntidemandDBHelper.getRaw(DemandTotalDTO.class, sql1, params);
        if(demandResult==null){
            log.warn("没有找到"+currentYear+"年需求数据");
            return Collections.emptyList();
        }
        return demandResult;
    }

    /**
     * 获取指定年份的退还总量数据
     * 查询从当前年份到第三年的退还数据，支持缓存机制
     * @param currentYear 查询的起始年份
     * @return 退还总量数据列表，如果没有数据则返回空列表
     */
    @Override
    @SneakyThrows
    @HiSpeedCache(expireSecond = 1200, continueFetchSecond = 3600,keyScript = "args[0]")
    public List<ReturnTotalDTO> getReturnTotal(Integer currentYear) {
        // 读取需求总量 SQL 文件
        String sql2 = IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_query_return_total.sql");
        int thirdYear=currentYear+2;
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("current_year", currentYear);
        params.put("third_year", thirdYear);
        // 执行需求总量查询
        List<ReturnTotalDTO> returnResult = yuntidemandDBHelper.getRaw(ReturnTotalDTO.class, sql2, params);
        if(returnResult==null){
            log.warn("没有找到"+currentYear+"年退回数据");
            return Collections.emptyList();
        }
        return returnResult;
    }

    /**
     * 获取指定年份的需求缓冲数据
     * 查询从当前年份到第三年的需求缓冲数据，支持缓存机制
     * @param currentYear 查询的起始年份
     * @return 需求缓冲数据列表，如果没有数据则返回空列表
     */
    @Override
    @SneakyThrows
    @HiSpeedCache(expireSecond = 1200, continueFetchSecond = 3600,keyScript = "args[0]")
    public List<DemandTotalDTO> getDemandBuffer(Integer currentYear) {
        // 读取需求总量 SQL 文件
        String sql1 = IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_query_buffer_demand.sql");
        int thirdYear=currentYear+2;
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("current_year", currentYear);
        params.put("third_year", thirdYear);
        // 执行需求总量查询
        List<DemandTotalDTO> demandBuffer = yuntidemandDBHelper.getRaw(DemandTotalDTO.class, sql1, params);
        if(demandBuffer==null){
            log.warn("没有找到"+currentYear+"年buffer需求数据");
            return Collections.emptyList();
        }
        return demandBuffer;
    }
    /**
     * 获取指定年份的退还缓冲数据
     * 查询从当前年份到第三年的退还缓冲数据，支持缓存机制
     * @param currentYear 查询的起始年份
     * @return 退还缓冲数据列表，如果没有数据则返回空列表
     */
    @Override
    @SneakyThrows
    @HiSpeedCache(expireSecond = 1200, continueFetchSecond = 3600,keyScript = "args[0]")
    public List<ReturnTotalDTO> getReturnBuffer(Integer currentYear) {
        // 读取需求总量 SQL 文件
        String sql2 = IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_query_buffer_return.sql");
        int thirdYear=currentYear+2;
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("current_year", currentYear);
        params.put("third_year", thirdYear);
        // 执行需求总量查询
        List<ReturnTotalDTO> returnBuffer= yuntidemandDBHelper.getRaw(ReturnTotalDTO.class, sql2, params);
        if(returnBuffer==null){
            log.warn("没有找到"+currentYear+"年buffer退回数据");
            return Collections.emptyList();
        }
        return returnBuffer;
    }

    /**
     * 获取指定年份的计划外退还总量数据
     * 查询从当前年份到第三年的计划外退还数据，支持缓存机制
     * @param currentYear 查询的起始年份
     * @return 计划外退还总量数据列表，如果没有数据则返回空列表
     */
    @Override
    @SneakyThrows
    @HiSpeedCache(expireSecond = 1200, continueFetchSecond = 3600,keyScript = "args[0]")
    public List<ReturnTotalDTO> getOutPlanReturnTotal(Integer currentYear) {
        String sql = IOUtils.readClasspathResourceAsString("sql/ziyan_longterm_predict/wxg/wxg_cvm_query_out_plan.sql");
        int thirdYear=currentYear+2;
        // 设置参数
        Map<String, Object> params = new HashMap<>();
        params.put("current_year", currentYear);
        params.put("third_year", thirdYear);
        // 执行需求总量查询
        List<ReturnTotalDTO> returnResult = yuntidemandDBHelper.getRaw(ReturnTotalDTO.class, sql, params);
        if(returnResult==null){
            log.warn("没有找到"+currentYear+"年退回数据");
            return Collections.emptyList();
        }
        return returnResult;
    }

}
