package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class HalfYearAccumulateResp {

    /** 按策略类型分组的需求量数据 */
    private List<StrategyDemandItem> strategyDemandList;

    /** 预测开始日期 */
    private String predictStartDate;

    /** 计算基准日期（预测开始前一个月的存量） */
    private String baselineDate;

    /** 基准存量 */
    private BigDecimal baselineStock;

    /** 半年期间起始年月 */
    private String halfYearStartDate;

    /** 半年期间终止年月 */
    private String halfYearEndDate;

    /** 一年期间起始年月 */
    private String oneYearStartDate;

    /** 一年期间终止年月 */
    private String oneYearEndDate;

    /** 一年半期间起始年月 */
    private String oneAndHalfYearStartDate;

    /** 一年半期间终止年月 */
    private String oneAndHalfYearEndDate;

    @Data
    public static class StrategyDemandItem {
        /** 策略类型 */
        private String strategyType;

        /** 半年需求量 */
        private BigDecimal halfYearDemand;

        /** 一年需求量 */
        private BigDecimal oneYearDemand;

        /** 一年半需求量 */
        private BigDecimal oneAndHalfYearDemand;
    }
}