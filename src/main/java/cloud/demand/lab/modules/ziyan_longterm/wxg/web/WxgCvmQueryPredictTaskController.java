package cloud.demand.lab.modules.ziyan_longterm.wxg.web;

import cloud.demand.lab.common.exception.WrongWebParameterException;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.QueryPredictTaskService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QuerySplitVersionReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryTaskInfoReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QuerySplitVersionResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryTaskInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 负责查询wxg中长期预测任务信息
 */
@JsonrpcController("/ziyan_longterm_wxg")
@Slf4j
public class WxgCvmQueryPredictTaskController {

    @Resource
    private QueryPredictTaskService queryPredictTaskService;

    /**
     * 查询已经存在的WXG预测任务列表
     */
    @RequestMapping
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(@JsonrpcParam QueryCategoryAndTaskListReq req) {
        return queryPredictTaskService.queryCategoryAndTaskList(req);
    }
    /**
     * 查询任务信息
     */
    @RequestMapping
    public QueryTaskInfoResp queryTaskInfo(@JsonrpcParam QueryTaskInfoReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        if (req.getTaskId() == null) {
            throw new WrongWebParameterException("任务id不能为空");
        }
        return queryPredictTaskService.queryTaskInfo(req);
    }

    /**
     * 查询拆分详情
     */
    @RequestMapping
    public QuerySplitVersionResp querySplitVersion(@JsonrpcParam QuerySplitVersionReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        if (req.getTaskId() == null) {
            throw new WrongWebParameterException("任务id不能为空");
        }
        if (req.getSplitVersionId()==null){
            throw new WrongWebParameterException("拆分版本id不能为空");
        }
        return queryPredictTaskService.querySplitVersion(req);
    }
}
