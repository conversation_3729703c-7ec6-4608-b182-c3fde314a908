package cloud.demand.lab.modules.ziyan_longterm.wxg.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 需求总量 DTO
 * 用于映射 wxg_cvm_query_demand_total.sql 查询结果
 */
@Data
public class DemandTotalDTO {
    
    /**
     * 未完成需求总量
     */
    @Column("not_finished_sum")
    private BigDecimal notFinishedSum;
    
    /**
     * 已完成需求总量（需求执行净需求）
     */
    @Column("finished_sum")
    private BigDecimal finishedSum;
    
    /**
     * 年份
     */
    @Column("year")
    private Integer year;
    
    /**
     * 计算业务提报净需求总量
     * @return 业务提报净需求总量 = finishedSum + notFinishedSum
     */
    public BigDecimal getDemandNetTotal() {
        if (finishedSum == null && notFinishedSum == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal finished = finishedSum != null ? finishedSum : BigDecimal.ZERO;
        BigDecimal notFinished = notFinishedSum != null ? notFinishedSum : BigDecimal.ZERO;
        return finished.add(notFinished);
    }
    
    /**
     * 获取需求执行净需求
     * @return 需求执行净需求 = finishedSum
     */
    public BigDecimal getExecuteNetTotal() {
        return finishedSum != null ? finishedSum : BigDecimal.ZERO;
    }
}