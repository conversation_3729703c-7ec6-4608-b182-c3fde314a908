package cloud.demand.lab.modules.ziyan_longterm.wxg.utils;

import lombok.Data;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 半年工具类，目前还在测试
 */
public class HalfYearUtils {

    /**
     * 月份信息类
     */
    @Data
    public static class MonthInfo {
        public final YearMonth yearMonth;  // 年月对象
        public final String monthStr;      // yyyy-MM格式字符串
        public final String monthName;     // 月份英文名称
        public final int quarter;          // 所属季度(1-4)
        public final int daysInMonth;      // 当月天数
        public final LocalDate firstDay;   // 当月第一天
        public final LocalDate lastDay;    // 当月最后一天
        public final int days;
        public final int workingDays;      // 当月工作日数（排除周末）

        public MonthInfo(YearMonth yearMonth) {
            this.yearMonth = yearMonth;
            this.monthStr = yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            this.monthName = yearMonth.getMonth().name();
            this.quarter = calculateQuarter(yearMonth.getMonthValue());
            this.daysInMonth = yearMonth.lengthOfMonth();
            this.firstDay = yearMonth.atDay(1);
            this.lastDay = yearMonth.atEndOfMonth();
            this.days=yearMonth.lengthOfMonth();
            this.workingDays = calculateWorkingDays(yearMonth);
        }

        /**
         * 计算月份所属季度
         *
         * @param month 月份(1-12)
         * @return 季度(1-4)
         */
        private int calculateQuarter(int month) {
            return (month - 1) / 3 + 1;
        }

        /**
         * 计算当月工作日数（排除周末）
         *
         * @param yearMonth 年月对象
         * @return 工作日数量
         */
        private int calculateWorkingDays(YearMonth yearMonth) {
            LocalDate start = yearMonth.atDay(1);
            LocalDate end = yearMonth.atEndOfMonth();
            int workingDays = 0;

            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                DayOfWeek day = date.getDayOfWeek();
                if (day != DayOfWeek.SATURDAY && day != DayOfWeek.SUNDAY) {
                    workingDays++;
                }
            }
            return workingDays;
        }

        /**
         * 验证日期是否在当月范围内
         *
         * @param date 待验证日期
         * @return 是否在当月范围内
         */
        public boolean isDateInMonth(LocalDate date) {
            return !date.isBefore(firstDay) && !date.isAfter(lastDay);
        }

        /**
         * 获取日期进度百分比
         *
         * @param date 当前日期
         * @return 当月进度百分比(0.0-1.0)
         */
        public double getProgressPercentage(LocalDate date) {
            if (!isDateInMonth(date)) {
                throw new IllegalArgumentException("Date not in this month");
            }
            int dayOfMonth = date.getDayOfMonth();
            return (double) dayOfMonth / daysInMonth;
        }
    }

    /**
     * 半年度信息类
     */
    @Data
    public static class HalfYear {
        public LocalDate startTime;   // 半年度开始日期
        public LocalDate endTime;     // 半年度结束日期
        public Integer year;           // 年份
        public String halfYear;        // 半年度标识(H1/H2)
        public List<MonthInfo> monthList; // 包含的月份信息列表

        public HalfYear(Integer year, String halfYear) {
            this.year = year;
            this.halfYear = halfYear;

            // 计算开始日期、结束日期和月份列表
            if ("H1".equals(halfYear)) {
                this.startTime = LocalDate.of(year, 1, 1);
                this.endTime = LocalDate.of(year, 6, 30);
                // 生成MonthInfo对象列表
                this.monthList = IntStream.rangeClosed(1, 6)
                        .mapToObj(month -> new MonthInfo(YearMonth.of(year, month)))
                        .collect(Collectors.toList());
            } else if ("H2".equals(halfYear)) {
                this.startTime = LocalDate.of(year, 7, 1);
                this.endTime = LocalDate.of(year, 12, 31);
                // 生成MonthInfo对象列表
                this.monthList = IntStream.rangeClosed(7, 12)
                        .mapToObj(month -> new MonthInfo(YearMonth.of(year, month)))
                        .collect(Collectors.toList());
            } else {
                throw new IllegalArgumentException("Invalid halfYear: " + halfYear);
            }
        }
    }

    /**
     * 解析多种格式的日期输入
     * 支持格式：LocalDate, yyyy-MM, yyyy-MM-dd, yyyy-MM-dd HH:mm:ss, YearMonth
     *
     * @param input 日期输入对象
     * @return 解析后的LocalDate
     * @throws IllegalArgumentException 如果输入格式无效
     */
    private static LocalDate parseDate(Object input) {
        if (input == null) {
            throw new IllegalArgumentException("Input cannot be null");
        }

        if (input instanceof LocalDate) {
            return (LocalDate) input;
        } else if (input instanceof YearMonth) {
            return ((YearMonth) input).atDay(1);
        } else if (input instanceof String) {
            String dateStr = ((String) input).trim();

            try {
                // 尝试解析为yyyy-MM-dd格式
                return LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (DateTimeParseException e1) {
                try {
                    // 尝试解析为yyyy-MM格式
                    YearMonth ym = YearMonth.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM"));
                    return ym.atDay(1);
                } catch (DateTimeParseException e2) {
                    try {
                        // 尝试解析为yyyy-MM-dd HH:mm:ss格式
                        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toLocalDate();
                    } catch (DateTimeParseException e3) {
                        throw new IllegalArgumentException("Unsupported date format: " + dateStr + ". Expected yyyy-MM, yyyy-MM-dd, or yyyy-MM-dd HH:mm:ss");
                    }
                }
            }
        } else {
            throw new IllegalArgumentException("Unsupported input type: " + input.getClass().getSimpleName());
        }
    }

    /**
     * 获取指定日期所属的半年度信息
     *
     * @param date 查询日期
     * @return 半年度对象
     */
    public static HalfYear getHalfYear(Object date) {
        LocalDate localDate = parseDate(date);
        int year = localDate.getYear();
        int month = localDate.getMonthValue();
        String half = (month <= 6) ? "H1" : "H2";
        return new HalfYear(year, half);
    }

    /**
     * 根据年份和半年度标识获取半年度信息
     *
     * @param year 年份
     * @param halfYear 半年度标识(H1/H2)
     * @return 半年度对象
     */
    public static HalfYear getHalfYearByIdentifier(int year, String halfYear) {
        return new HalfYear(year, halfYear);
    }

    /**
     * 获取指定日期前N个半年度（包含当前半年度）
     *
     * @param date 基准日期
     * @param n 半年度数量
     * @param includeCurrent 是否包含当前半年度
     * @return 半年度列表（按时间倒序）
     */
    private static List<HalfYear> getPreviousHalfYears(LocalDate date, int n, boolean includeCurrent) {
        validateParameters(n);
        HalfYear current = getHalfYear(date);
        List<HalfYear> result = new ArrayList<>();

        // 如果需要包含当前半年度，将其添加到列表
        if (includeCurrent) {
            result.add(current);
            n--;  // 还需要再计算 n-1 个半年度
        }

        // 循环获取前n个半年度
        for (int i = 0; i < n; i++) {
            current = getLastHalfYear(current.getYear(), current.getHalfYear());  // 更新 current 到上一个半年度
            result.add(current);  // 将计算出来的半年度添加到结果列表
        }

        return result;
    }

    /**
     * 获取指定日期前N个半年度（支持多种日期格式）
     *
     * @param dateInput 基准日期（支持LocalDate, String, YearMonth）
     * @param n 半年度数量
     * @param includeCurrent 是否包含当前半年度
     * @return 半年度列表（按时间倒序）
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static List<HalfYear> getPreviousHalfYears(Object dateInput, int n, boolean includeCurrent) {
        LocalDate date = parseDate(dateInput);
        return getPreviousHalfYears(date, n, includeCurrent);
    }

    /**
     * 获取指定日期后N个半年度（包含当前半年度）
     *
     * @param date 基准日期
     * @param n 半年度数量
     * @param includeCurrent 是否包含当前半年度
     * @return 半年度列表（按时间正序）
     */
    private static List<HalfYear> getNextHalfYears(LocalDate date, int n, boolean includeCurrent) {
        validateParameters(n);
        HalfYear current = getHalfYear(date);
        List<HalfYear> result = new ArrayList<>();

        // 如果需要包括当前半年度，将其添加到结果列表
        if (includeCurrent) {
            result.add(current);
            n--;  // 还需要再计算 n-1 个半年度
        }

        // 循环获取接下来的 n 个半年度
        for (int i = 0; i < n; i++) {
            current = getNextHalfYear(current.getYear(), current.getHalfYear());  // 更新 current 到下一个半年度
            result.add(current);  // 将计算出来的半年度添加到结果列表
        }

        return result;
    }

    /**
     * 获取指定日期后N个半年度（支持多种日期格式）
     *
     * @param dateInput 基准日期（支持LocalDate, String, YearMonth）
     * @param n 半年度数量
     * @param includeCurrent 是否包含当前半年度
     * @return 半年度列表（按时间正序）
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static List<HalfYear> getNextHalfYears(Object dateInput, int n, boolean includeCurrent) {
        LocalDate date = parseDate(dateInput);
        return getNextHalfYears(date, n, includeCurrent);
    }

    /**
     * 获取指定日期范围内的所有半年度
     * @param startInput
     * @param endInput
     * @param includeStart
     * @param includeEnd
     * @return
     */
    public static List<HalfYear> getHalfYearsInRange(Object startInput, Object endInput, boolean includeStart, boolean includeEnd) {
        LocalDate start = parseDate(startInput);
        LocalDate end = parseDate(endInput);
        List<HalfYear> halfYears = new ArrayList<>();

        int startYear = start.getYear();
        int endYear = end.getYear();

        // 判断开始半年度
        String startHalf = (start.isBefore(LocalDate.of(startYear, 7, 1))) ? "H1" : "H2";
        HalfYear current = new HalfYear(startYear, startHalf);

        // 处理是否包含开始日期
        if (includeStart) {
            if (current.getStartTime().isBefore(start) || current.getStartTime().isEqual(start)) {
                halfYears.add(current);
            }
        } else {
            if (current.getStartTime().isAfter(start)) {
                halfYears.add(current);
            }
        }

        // 循环获取每个半年度
        while (startYear <= endYear) {
            String half = (current.getHalfYear().equals("H1")) ? "H2" : "H1"; // 切换半年度
            if (half.equals("H1")) {
                startYear++; // 如果是 H2，则切换到下一年
            }

            current = new HalfYear(startYear, half);
            // 处理是否包含结束日期
            if (includeEnd) {
                if (current.getEndTime().isAfter(end) || current.getEndTime().isEqual(end)) {
                    halfYears.add(current);
                    break;
                }else{
                    halfYears.add(current);
                }
            } else {
                if (current.getEndTime().isBefore(end)) {
                    halfYears.add(current);
                }
            }
        }

        return halfYears;
    }


    /**
     * 获取上一个半年
     * @param year
     * @param halfYear
     * @return
     */
    public static HalfYear getLastHalfYear(int year, String halfYear) {
        if (halfYear == null || (!halfYear.equals("H1") && !halfYear.equals("H2"))) {
            throw new IllegalArgumentException("Invalid halfYear: " + halfYear);
        }

        String previousHalfYear;
        int previousYear;

        // 如果当前是 H1，上一个半年度是上一年 H2
        if (halfYear.equals("H1")) {
            previousYear = year - 1;
            previousHalfYear = "H2";
        }
        // 如果当前是 H2，上一个半年度是当前年 H1
        else {
            previousYear = year;
            previousHalfYear = "H1";
        }

        return new HalfYear(previousYear, previousHalfYear);
    }

    /**
     * 获取下一个半年
     * @param year
     * @param halfYear
     * @return
     */
    public static HalfYear getNextHalfYear(int year, String halfYear) {
        if (halfYear == null || (!halfYear.equals("H1") && !halfYear.equals("H2"))) {
            throw new IllegalArgumentException("Invalid halfYear: " + halfYear);
        }

        String nextHalfYear;
        int nextYear;

        // 如果当前是 H1，下一个半年度是同一年 H2
        if (halfYear.equals("H1")) {
            nextYear = year;
            nextHalfYear = "H2";
        }
        // 如果当前是 H2，下一个半年度是下一年 H1
        else {
            nextYear = year + 1;
            nextHalfYear = "H1";
        }

        return new HalfYear(nextYear, nextHalfYear);
    }

    /**
     * 验证参数有效性
     *
     * @param n 半年度数量
     * @throws IllegalArgumentException 如果n为负数
     */
    private static void validateParameters(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("n must be non-negative");
        }
    }
}
