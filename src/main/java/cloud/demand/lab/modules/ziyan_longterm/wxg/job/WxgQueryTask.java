package cloud.demand.lab.modules.ziyan_longterm.wxg.job;

import cloud.demand.lab.modules.ziyan_longterm.wxg.service.ScheduledQueryService;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import task.run.exporter.anno.TaskRunLog;

import javax.annotation.Resource;
import java.time.LocalDate;

@Slf4j
@Service
public class WxgQueryTask {

    @Resource
    private ScheduledQueryService scheduledQueryService;

    /**
     * 工作日每小时刷新最后一天库存数据缓存（早上6点开始）
     * 缓存配置：20分钟过期，60分钟持续刷新
     *
     */
    @Scheduled(cron = "0 0 6-23 * * MON-FRI")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "WXG_CACHE", nameScript = "'refreshLastDayStockCache'", keyScript = "'java.time.LocalDate.now()'")
    public void refreshLastDayStockCache() {
        try {
            // 主动调用方法，触发缓存刷新
            scheduledQueryService.getLastDayStock();
            log.info("成功刷新最后一天库存数据缓存");
        } catch (Exception e) {
            log.error("刷新最后一天库存数据缓存失败", e);
        }
    }


    /**
     * 工作日每小时刷新需求总量缓存（早上6点开始）
     * 缓存配置：20分钟过期，60分钟持续刷新
     * 预热策略：预热最近5年数据（前2年、当前年、后2年）
     */
    @Scheduled(cron = "0 0 6-23 * * MON-FRI")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "WXG_CACHE", nameScript = "'refreshDemandTotalCache'", keyScript = "'java.time.LocalDate.now()'")
    public void refreshDemandTotalCache() {
        try {
            int baseYear = LocalDate.now().getYear();
            // 预热最近5年的数据（前2年、当前年、后2年）
            for (int i = -2; i <= 2; i++) {
                int currentYear = baseYear + i;
                // 主动调用方法，触发缓存刷新
                scheduledQueryService.getDemandTotal(currentYear);
                log.info("成功预热 {} 年的需求总量缓存", currentYear);
            }
            log.info("成功刷新wxg需求总量缓存（共预热5年数据）");
        } catch (Exception e) {
            log.error("刷新wxg需求总量缓存失败", e);
        }
    }

    /**
     * 工作日每小时刷新退回总量缓存（早上6点开始）
     * 缓存配置：20分钟过期，60分钟持续刷新
     * 预热策略：预热最近5年数据（前2年、当前年、后2年）
     */
    @Scheduled(cron = "0 0 6-23 * * MON-FRI")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "WXG_CACHE", nameScript = "'refreshReturnTotalCache'", keyScript = "'java.time.LocalDate.now()'")
    public void refreshReturnTotalCache() {
        try {
            int baseYear = LocalDate.now().getYear();
            // 预热最近5年的数据（前2年、当前年、后2年）
            for (int i = -2; i <= 2; i++) {
                int currentYear = baseYear + i;
                // 主动调用方法，触发缓存刷新
                scheduledQueryService.getReturnTotal(currentYear);
                log.info("成功预热 {} 年的退回总量缓存", currentYear);
            }
            log.info("成功刷新wxg退回总量缓存（共预热5年数据）");
        } catch (Exception e) {
            log.error("刷新wxg退回总量缓存失败", e);
        }
    }

    /**
     * 工作日每小时刷新buffer需求数据缓存（早上6点开始）
     * 缓存配置：20分钟过期，60分钟持续刷新
     * 预热策略：预热最近5年数据（前2年、当前年、后2年）
     */
    @Scheduled(cron = "0 0 6-23 * * MON-FRI")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "WXG_CACHE", nameScript = "'refreshDemandBufferCache'", keyScript = "'java.time.LocalDate.now()'")
    public void refreshDemandBufferCache() {
        try {
            int baseYear = LocalDate.now().getYear();
            // 预热最近5年的数据（前2年、当前年、后2年）
            for (int i = -2; i <= 2; i++) {
                int currentYear = baseYear + i;
                int thirdYear = currentYear + 2;
                // 主动调用方法，触发缓存刷新
                scheduledQueryService.getDemandBuffer(currentYear);
                log.info("成功预热 {} 年的buffer需求数据缓存", currentYear);
            }
            log.info("成功刷新wxg buffer需求数据缓存（共预热5年数据）");
        } catch (Exception e) {
            log.error("刷新wxg buffer需求数据缓存失败", e);
        }
    }


    /**
     * 工作日每小时刷新计划外退回数据缓存（早上6点开始）
     * 缓存配置：20分钟过期，60分钟持续刷新
     * 预热策略：预热最近5年数据（前2年、当前年、后2年）
     */
    @Scheduled(cron = "0 0 6-23 * * MON-FRI")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "WXG_CACHE", nameScript = "'refreshOutPlanReturnCache'", keyScript = "'java.time.LocalDate.now()'")
    public void refreshOutPlanReturnCache() {
        try {
            int baseYear = LocalDate.now().getYear();
            // 预热最近5年的数据（前2年、当前年、后2年）
            for (int i = -2; i <= 2; i++) {
                int currentYear = baseYear + i;
                // 主动调用方法，触发缓存刷新
                scheduledQueryService.getOutPlanReturnTotal(currentYear);
                log.info("成功预热 {} 年的outPlan退回数据缓存", currentYear);
            }
            log.info("成功刷新wxg outPlan退回数据缓存（共预热5年数据）");
        } catch (Exception e) {
            log.error("刷新wxg outPlan退回数据缓存失败", e);
        }
    }


    /**
     * 工作日每小时刷新buffer退回数据缓存（早上6点开始）
     * 缓存配置：20分钟过期，60分钟持续刷新
     * 预热策略：预热最近5年数据（前2年、当前年、后2年）
     */
    @Scheduled(cron = "0 0 6-23 * * MON-FRI")
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @TaskRunLog(namespace = "WXG_CACHE", nameScript = "'refreshReturnBufferCache'", keyScript = "'java.time.LocalDate.now()'")
    public void refreshReturnBufferCache() {
        try {
            int baseYear = LocalDate.now().getYear();
            // 预热最近5年的数据（前2年、当前年、后2年）
            for (int i = -2; i <= 2; i++) {
                int currentYear = baseYear + i;
                int thirdYear = currentYear + 2;
                // 主动调用方法，触发缓存刷新
                scheduledQueryService.getReturnBuffer(currentYear);
                log.info("成功预热 {} 年的buffer退回数据缓存", currentYear);
            }
            log.info("成功刷新wxg buffer退回数据缓存（共预热5年数据）");
        } catch (Exception e) {
            log.error("刷新wxg buffer退回数据缓存失败", e);
        }
    }
    
}
