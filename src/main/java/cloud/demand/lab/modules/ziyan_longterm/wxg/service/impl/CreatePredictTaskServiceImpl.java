package cloud.demand.lab.modules.ziyan_longterm.wxg.service.impl;

import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.MonthlyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.*;
import cloud.demand.lab.modules.ziyan_longterm.wxg.enums.Constants;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.CalculatePredictService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.CreatePredictTaskService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.WxgCvmSplitService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.utils.DateRangeUtils;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.CreatePredictTaskReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.CreatePredictTaskResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryCategoryForCreateResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.net.NetUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 创建预测任务服务实现类
 * 提供WXG中长期CVM预测任务的创建、运行和相关配置查询功能实现
 * 支持任务的生命周期管理、数据查询、预测计算、结果保存等完整流程
 * 集成多种预测算法和数据拆分功能
 */
@Service
@Slf4j
public class CreatePredictTaskServiceImpl implements CreatePredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DBHelper ckcubesDBHelper;

    @Resource
    private CalculatePredictService calculatePredictService;

    @Resource
    private RedisHelper redisHelper;

    @Resource
    private WxgCvmSplitService wxgCvmSplitService;

    /**
     * 创建预测任务
     * 根据请求参数创建新的预测任务，包括参数校验、方案检查、任务创建、预测执行等完整流程
     * @param req 创建预测任务的请求参数，包含方案ID、启用状态等
     * @return 创建预测任务的响应结果，包含任务ID和拆分版本ID
     * @throws RuntimeException 当方案ID为空或方案不存在时抛出异常
     */
    @Override
    @Transactional(value = "cdlabTransactionManager")
    public CreatePredictTaskResp createPredictTask(CreatePredictTaskReq req) {
        //1.参数校验
        if(req==null||req.getCategoryId()==null){
            throw new RuntimeException("方案id(categoryId)不能为空");
        }

        //2.检查方案是否存在
        ZiyanCvmLongtermPredictCategoryConfigDO categoryConfig= getCategoryConfig(req.getCategoryId());

        //3.创建预测任务
        ZiyanCvmLongtermPredictTaskDO task = createTask(categoryConfig, req.getCategoryId(), req.getIsEnable());

        //4.如果预测月份的方案已经isEnable，则覆盖它
        if (req.getIsEnable() != null && req.getIsEnable()) {
            WhereSQL whereSQL = new WhereSQL();
            whereSQL.and("is_enable = ?", true);
            whereSQL.and("category_id = ?", task.getCategoryId());
            whereSQL.and("predict_start = ?", task.getPredictStart());

            cdLabDbHelper.updateAll(ZiyanCvmLongtermPredictTaskDO.class, "is_enable=false",
                    whereSQL.getSQL(), whereSQL.getParams());
            task.setIsEnable(true);
        }

        //5.创建任务
        cdLabDbHelper.insert(task);

        //6.运行task预测（outputscale）
        doRunPredictTask(task.getId());

        ZiyanCvmLongtermPredictOutputSplitVersionDO splitVersion = cdLabDbHelper.getOne(ZiyanCvmLongtermPredictOutputSplitVersionDO.class,"where task_id=?",task.getId());

        CreatePredictTaskResp resp = new CreatePredictTaskResp();
        resp.setTaskId(task.getId());
        resp.setSplitVersionId(splitVersion.getId());
        return resp;
    }

    /**
     * 执行预测任务
     * 运行指定ID的预测任务，包括数据查询、预测计算、结果保存、拆分操作等完整流程
     * 使用分布式锁确保任务执行的唯一性，支持事务管理和异常处理
     * @param taskId 任务ID，用于标识要运行的预测任务
     * @throws RuntimeException 当预测计算失败时抛出异常
     */
    @Synchronized(keyScript = "args[0]", waitLockMillisecond = 100)
    @Transactional(value = "cdlabTransactionManager")
    public void doRunPredictTask(Long taskId) {
        long start = System.currentTimeMillis();
        log.info("开始运行自研WXG预测 task:{}", taskId);

        //1.判断任务状态，并设置任务状态为运行中
        ZiyanCvmLongtermPredictTaskDO task = getTaskAndSetStatus(taskId);

        try{
            //2.查询存量数据
            List<ZiyanCvmLongtermPredictInputScaleDO> scaleDO= queryWxgCvmInputScale(task);
            //保存存量数据
            saveWxgInputScale(scaleDO,task);
            //3.执行预测计算
            // 按年月分组，对cur_core进行累加，使用DTO接收结果
            List<MonthlyCoreDTO> monthlyResults = scaleDO.stream()
                    .collect(Collectors.groupingBy(
                            ZiyanCvmLongtermPredictInputScaleDO::getYearMonthStr,
                            Collectors.reducing(BigDecimal.ZERO,
                                    ZiyanCvmLongtermPredictInputScaleDO::getCurCore,
                                    BigDecimal::add)
                    ))
                    .entrySet().stream()
                    .map(entry -> new MonthlyCoreDTO(entry.getKey(), entry.getValue(),null))
                    .collect(Collectors.toList());
            List<ZiyanCvmLongtermPredictOutputScaleDO> outputScaleDO=calculatePredictService.calculatePredictResults(monthlyResults,task.getPredictStart(),task.getPredictEnd(),task);

            //4.保存预测结果
            saveWxgOutputScale(outputScaleDO,task);

            //5.发送Prophet算法异步预测消息
            //sendProphetPredictMessage(task.getId());

            //6.发送ARIMA算法异步预测消息
            //sendArimaPredictMessage(task.getId());

            //7.执行拆分操作
            List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitDOS = wxgCvmSplitService.splitData(scaleDO,outputScaleDO,task);

            //保存拆分数据
            saveWxgOutputScaleSplit(splitDOS,task);

            //8.更新预测状态为成功
            long cost = System.currentTimeMillis() - start;
            updateTaskStatusSuccess(taskId, (int) cost);
            log.info("wxg cvm 中长期预测任务执行成功, taskId:{}, cost:{}ms", taskId, cost);

        }catch (Exception e){
            log.error("wxg cvm 中长期预测任务执行失败, taskId:{}", taskId, e);
            updateTaskStatus(taskId, LongtermPredictTaskStatusEnum.PREDICT_FAIL, e.getMessage());
        }
    }

    /**
     * 保存拆分数据
     * 将预测结果的拆分数据保存到数据库，先删除旧数据再批量插入新数据
     * @param splitDOS 拆分数据列表，包含按地域和设备类型拆分的预测结果
     * @param taskDO 任务信息，用于获取任务ID
     */
    private void saveWxgOutputScaleSplit(List<ZiyanCvmLongtermPredictOutputScaleSplitDO> splitDOS, ZiyanCvmLongtermPredictTaskDO taskDO) {
        Long taskId=taskDO.getId();
        cdLabDbHelper.delete(ZiyanCvmLongtermPredictOutputScaleSplitDO.class,"where task_id= ?",taskId);
        int i = cdLabDbHelper.insertBatchWithoutReturnId(splitDOS);
        if(i!=splitDOS.size()){
            log.error("保存拆分数据失败，taskId: {}, 数据量: {}", taskId, splitDOS.size());
        }
        log.info("保存拆分数据成功，taskId: {}, 数据量: {}", taskId, splitDOS.size());
    }

    /**
     * 查询用于创建任务的方案列表及方案关键信息
     * 为【创建预测】弹框提供可选的方案列表和相关配置信息，包括预测时间范围、策略类型等
     * @param req 查询方案的请求参数，包含筛选条件
     * @return 方案列表响应结果，包含可用方案及其关键配置信息
     */
    @Override
    public QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req) {
        List<ZiyanCvmLongtermPredictCategoryConfigDO> categoryConfigs = cdLabDbHelper.getAll(ZiyanCvmLongtermPredictCategoryConfigDO.class);

        QueryCategoryForCreateResp resp = new QueryCategoryForCreateResp();
        resp.setCategoryList(categoryConfigs.stream().map(o -> {
            QueryCategoryForCreateResp.Item item = new QueryCategoryForCreateResp.Item();
            item.setCategoryId(o.getId());
            item.setCategoryName(o.getCategory());
            LocalDate startDate = DateRangeUtils.getPredictStartDate(o);
            LocalDate endDate = DateRangeUtils.getPredictEndDate(o);
            item.setPredictStart(com.pugwoo.wooutils.lang.DateUtils.format(startDate, "yyyy-MM"));
            item.setPredictEnd(com.pugwoo.wooutils.lang.DateUtils.format(endDate, "yyyy-MM"));
            //item.setInputArgDateRanges(DateRangeUtils.getDateRange(startDate, endDate, o.getIntervalMonth()));
            item.setStrategyTypes(java.util.Arrays.stream(cloud.demand.lab.modules.longterm.predict.enums.StrategyTypeEnum.values())
                    .map(QueryCategoryForCreateResp.StrategyType::from)
                    .collect(Collectors.toList()));
            return item;
        }).collect(Collectors.toList()));
        return resp;
    }

    /**
     * 保存预测结果
     * 将线性拟合算法的预测结果保存到数据库，先删除旧数据再批量插入新数据
     * @param outputScaleDO 预测结果数据列表，包含各月份的预测核心数
     * @param taskDO 任务信息，用于获取任务ID
     */
    private void saveWxgOutputScale(List<ZiyanCvmLongtermPredictOutputScaleDO> outputScaleDO, ZiyanCvmLongtermPredictTaskDO taskDO) {
        Long taskId=taskDO.getId();
        cdLabDbHelper.delete(ZiyanCvmLongtermPredictOutputScaleDO.class,"where task_id= ?",taskId);
        int i = cdLabDbHelper.insertBatchWithoutReturnId(outputScaleDO);
        if(i!=outputScaleDO.size()){
            log.error("保存预测数据失败，taskId: {}, 数据量: {}", taskId, outputScaleDO.size());
        }
        log.info("保存预测数据成功，taskId: {}, 数据量: {}", taskId, outputScaleDO.size());
    }

    /**
     * 保存存量数据
     * 将查询到的历史存量数据保存到数据库，作为预测计算的基础数据
     * @param scaleDO 存量数据列表，包含历史各月份的核心数数据
     * @param taskDO 任务信息，用于获取任务ID和关联数据
     */
    private void saveWxgInputScale(List<ZiyanCvmLongtermPredictInputScaleDO> scaleDO,ZiyanCvmLongtermPredictTaskDO taskDO) {
        Long taskId = taskDO.getId();
        cdLabDbHelper.delete(ZiyanCvmLongtermPredictInputScaleDO.class, "where task_id = ?", taskId);
        int i = cdLabDbHelper.insertBatchWithoutReturnId(scaleDO);
        if(i!=scaleDO.size()) {
            log.error("保存存量数据失败，taskId: {}, 数据量: {}", taskId, scaleDO.size());
        }
        log.info("保存存量数据成功，taskId: {}, 数据量: {}", taskId, scaleDO.size());
    }

    /**
     * 发送Prophet算法异步预测消息
     * 通过Redis队列发送异步消息，触发Prophet算法预测任务
     * 支持事务提交后发送，确保数据一致性
     * @param taskId 任务ID，用于标识预测任务
     */
    private void sendProphetPredictMessage(Long taskId) {
        try {
            log.info("准备发送Prophet算法异步预测消息，taskId: {}", taskId);
            Runnable sendMsg = () -> {
                try {
                    redisHelper.send(Constants.REDIS_QUEUE_WXG_PROPHET_PREDICT_TASK, taskId.toString(), 600);
                    log.info("Prophet算法异步预测消息发送成功，taskId: {}", taskId);
                } catch (Exception e) {
                    log.error("Prophet算法异步预测消息发送失败，taskId: {}", taskId, e);
                }
            };
            
            // 尝试在事务提交后发送消息
            boolean submit = cdLabDbHelper.executeAfterCommit(sendMsg);
            if (!submit) {
                // 如果无法在事务提交后执行，则立即执行
                sendMsg.run();
            }
        } catch (Exception e) {
            log.error("发送Prophet算法异步预测消息失败，taskId: {}", taskId, e);
            // 这里不抛出异常，避免影响主流程
        }
    }

    /**
     * 发送ARIMA算法异步预测消息
     * 通过Redis队列发送异步消息，触发ARIMA算法预测任务
     * 支持事务提交后发送，确保数据一致性
     * @param taskId 任务ID，用于标识预测任务
     */
    private void sendArimaPredictMessage(Long taskId) {
        try {
            log.info("准备发送ARIMA算法异步预测消息，taskId: {}", taskId);
            Runnable sendMsg = () -> {
                try {
                    redisHelper.send(Constants.REDIS_QUEUE_WXG_ARIMA_PREDICT_TASK, taskId.toString(), 600);
                    log.info("ARIMA算法异步预测消息发送成功，taskId: {}", taskId);
                } catch (Exception e) {
                    log.error("ARIMA算法异步预测消息发送失败，taskId: {}", taskId, e);
                }
            };
            
            // 尝试在事务提交后发送消息
            boolean submit = cdLabDbHelper.executeAfterCommit(sendMsg);
            if (!submit) {
                // 如果无法在事务提交后执行，则立即执行
                sendMsg.run();
            }
        } catch (Exception e) {
            log.error("发送ARIMA算法异步预测消息失败，taskId: {}", taskId, e);
            // 这里不抛出异常，避免影响主流程
        }
    }

//    /**
//     * 保存算法预测结果
//     * @param algorithmResults 算法预测结果列表
//     * @param taskDO 任务信息
//     */
//    private void saveWxgOutputScaleByAlgorithm(List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> algorithmResults, ZiyanCvmLongtermPredictTaskDO taskDO) {
//        if (algorithmResults == null || algorithmResults.isEmpty()) {
//            log.info("算法预测结果为空，跳过保存，taskId: {}", taskDO.getId());
//            return;
//        }
//
//        Long taskId = taskDO.getId();
//        // 删除该任务的旧算法预测数据
//        cdLabDbHelper.delete(ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO.class, "where task_id = ?", taskId);
//
//        // 批量插入新的算法预测数据
//        int i = cdLabDbHelper.insertBatchWithoutReturnId(algorithmResults);
//        if(i != algorithmResults.size()) {
//            log.error("保存算法预测数据失败，taskId: {}, 预期数据量: {}, 实际保存: {}", taskId, algorithmResults.size(), i);
//        } else {
//            log.info("保存算法预测数据成功，taskId: {}, 数据量: {}", taskId, algorithmResults.size());
//        }
//    }

    /**
     * 查询输入存量数据
     * @param task
     * @return
     */
    @SneakyThrows
    private List<ZiyanCvmLongtermPredictInputScaleDO> queryWxgCvmInputScale(ZiyanCvmLongtermPredictTaskDO task) {
        String inputSql = task.getInputSql();
        List<ZiyanCvmLongtermPredictInputScaleDO> raw = ckcubesDBHelper.getRaw(ZiyanCvmLongtermPredictInputScaleDO.class, inputSql);
        if(raw==null||raw.isEmpty())throw new BizException("查询存量数据为空");
        for (ZiyanCvmLongtermPredictInputScaleDO inputScaleDO : raw) {
            inputScaleDO.setBg("WXG");
            inputScaleDO.setTaskId(task.getId());
            inputScaleDO.setYearMonthStr(inputScaleDO.getStatTime().toString().substring(0,7));
        }
        return raw;
    }


    /**
     * 获取任务并设置状态为运行中
     */
    private ZiyanCvmLongtermPredictTaskDO getTaskAndSetStatus(Long taskId) {
        ZiyanCvmLongtermPredictTaskDO task = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
        if (task == null) {
            throw new BizException("wxg预测任务" + taskId + "不存在");
        }
        // 只有状态为NEW/FAIL的任务才能执行
        if (!(LongtermPredictTaskStatusEnum.NEW.getCode().equals(task.getTaskStatus())
                || LongtermPredictTaskStatusEnum.PREDICT_FAIL.getName().equals(task.getTaskStatus())
                || LongtermPredictTaskStatusEnum.SPLIT_FAIL.getName().equals(task.getTaskStatus()))) {
            throw new BizException("wxg预测任务" + taskId + "状态为" + task.getTaskStatus() + "，不能运行");
        }

        // 设置任务状态为运行中
        task.setTaskStatus(LongtermPredictTaskStatusEnum.RUNNING.getCode());
        task.setTaskStartTime(LocalDateTime.now());
        task.setErrMsg(""); // reset err msg
        try {
            task.setRunIp(StringTools.join(",", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            task.setRunIp("EXCEPTION:" + e.getMessage());
        }
        cdLabDbHelper.update(task);


        return task;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(Long taskId, LongtermPredictTaskStatusEnum status, String errMsg) {
        ZiyanCvmLongtermPredictTaskDO task = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
        if (task != null) {
            task.setTaskStatus(status.getCode());
            task.setTaskEndTime(LocalDateTime.now());
            if (errMsg != null && errMsg.length() > 500) {
                errMsg = errMsg.substring(0, 500); // 限制错误信息长度
            }
            task.setErrMsg(errMsg);
            cdLabDbHelper.update(task);
        }
    }

    /**
     * 更新任务状态为成功
     */
    private void updateTaskStatusSuccess(Long taskId, int costMs) {
        ZiyanCvmLongtermPredictTaskDO task = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
        if (task != null) {
            task.setTaskStatus(LongtermPredictTaskStatusEnum.SUCCESS.getCode());
            task.setTaskEndTime(LocalDateTime.now());
            task.setCostMs(costMs);
            task.setErrMsg(null);
            cdLabDbHelper.update(task);
        }
    }

    /**
     * 创建wxg CVM预测任务
     */
    private ZiyanCvmLongtermPredictTaskDO createTask(ZiyanCvmLongtermPredictCategoryConfigDO categoryConfig,
                                                     Long categoryId, Boolean isEnable) {
        ZiyanCvmLongtermPredictTaskDO task = new ZiyanCvmLongtermPredictTaskDO();
        task.setCategoryId(categoryId);
        task.setCategoryName(categoryConfig.getCategory());
        task.setIsEnable(isEnable != null && isEnable);
        task.setTaskStatus(LongtermPredictTaskStatusEnum.NEW.getCode());
        task.setCreator(LoginUtils.getUserNameWithSystem());
        task.setPredictStart(DateRangeUtils.getPredictStartDate(categoryConfig));
        task.setPredictEnd(DateRangeUtils.getPredictEndDate(categoryConfig));
        task.setConditionSql(categoryConfig.getWhereSql());
        task.setDimsName(categoryConfig.getDimsName());
        task.setScopeCustomer(categoryConfig.getScopeCustomer());
        task.setScopeResourcePool(categoryConfig.getScopeResourcePool());
        task.setScopeProduct(categoryConfig.getScopeProduct());
        task.setModelName(categoryConfig.getModelName());
        task.setBg(categoryConfig.getBg());
        task.setIntervalMonth(categoryConfig.getIntervalMonth());

        try {
            String sql = IOUtils.readClasspathResourceAsString("/sql/ziyan_longterm_predict/wxg/wxg_cvm_scale.sql");
            sql = sql.replace("${CONDITION}", categoryConfig.getWhereSql());
            task.setInputSql(sql);
        } catch (Exception ignored) {}

        return task;
    }

    private ZiyanCvmLongtermPredictCategoryConfigDO getCategoryConfig(Long categoryId) {
        if(categoryId==null){
            throw new RuntimeException("方案id(categoryId)不能为空");
        }
        ZiyanCvmLongtermPredictCategoryConfigDO categoryConfig= cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictCategoryConfigDO.class,categoryId);
        if(categoryConfig==null){
            throw new RuntimeException("方案不存在");
        }
        return categoryConfig;
    }

}
