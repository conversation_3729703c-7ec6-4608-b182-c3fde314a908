package cloud.demand.lab.modules.ziyan_longterm.wxg.service;

import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.HalfYearAccumulateReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.PredictTotalReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryPredictResultByTaskReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.queryScaleHistoryReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.*;

/**
 * 规模历史和预测服务接口
 * 提供历史数据查询、预测数据查询、需求量计算等功能
 * 支持按机型分组查询和多种时间维度的数据统计
 */
public interface ScaleHistoryAndPredictService {

    /**
     * 查询历史,按机型分组
     * @param req 查询请求参数
     * @return 查询结果
     */
    queryScaleHistoryResp queryScaleHistoryWithInstance(queryScaleHistoryReq req);

    /**
     * 查询历史
     * @param req 查询请求参数
     * @return 查询结果
     */
    queryScaleHistoryResp queryScaleHistory(queryScaleHistoryReq req);

    /**
     * 查询预测
     * @param req 查询请求参数
     * @return 查询结果
     */
    queryScalePredictResp queryScalePredict(queryScaleHistoryReq req);

    /**
     * 计算未来半年、一年、一年半的需求量
     * @param req 查询请求参数
     * @return 需求量计算结果
     */
    HalfYearAccumulateResp QueryHalfYearAccumulate(HalfYearAccumulateReq req);

    /**
     * 计算预测当年、次年、第三年的净增总量数据
     * @param req 查询请求参数
     * @return 年度净增量计算结果
     */
    PredictTotalResp QueryPredictTotal(PredictTotalReq req);

    /**
     * 根据任务ID查询预测结果，按年月-算法类型聚合
     * @param req 查询请求参数
     * @return 预测结果数据
     */
    QueryPredictResultByTaskResp queryPredictResultByAlgorithm(QueryPredictResultByTaskReq req);

    /**
     * 查询预测结果，按机型分组
     * @param req
     * @return
     */
    queryScalePredictResp queryScalePredictWithInstance(queryScaleHistoryReq req);
}
