package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import lombok.Data;

import java.util.List;

@Data
public class QueryCategoryAndTaskListResp {

    private List<Category> categoryList;

    @Data
    public static class Category {

        private Long categoryId;

        private String categoryName;

        /**属于模型预测的部分*/
        private String modelName;

        /**预测任务列表，时间逆序*/
        private List<Task> predictTaskList;

    }

    @Data
    public static class Task {

        private Long taskId;

        private String yearMonth;

        private String taskStatusCode;

        private String taskStatusName;

    }

}