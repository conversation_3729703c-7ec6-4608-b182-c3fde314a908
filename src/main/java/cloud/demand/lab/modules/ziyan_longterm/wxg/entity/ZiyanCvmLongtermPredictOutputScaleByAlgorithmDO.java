package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;


import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_output_scale_by_algorithm")
public class ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO extends BaseDO {

    /** 任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 算法名称，例如ARIMA、linregress、Prophet<br/>Column: [algorithm] */
    @Column(value = "algorithm")
    private String algorithm;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    /** 日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 年月表达式<br/>Column: [year_month_str] */
    @Column(value = "year_month_str")
    private String yearMonthStr;

    /** 预测出来的当前存量(单位:核)<br/>Column: [cur_core] */
    @Column(value = "cur_core")
    private BigDecimal curCore;

    /** 对比上个月的增量核心(单位:核)<br/>Column: [change_cur_core_from_last_month] */
    @Column(value = "change_cur_core_from_last_month")
    private BigDecimal changeCurCoreFromLastMonth;

}
