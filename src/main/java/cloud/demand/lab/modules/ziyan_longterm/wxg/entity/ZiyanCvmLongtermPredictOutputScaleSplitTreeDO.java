package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;


import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_output_scale_split_tree")
public class ZiyanCvmLongtermPredictOutputScaleSplitTreeDO extends BaseDO {

    /** 任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 拆分id<br/>Column: [split_version_id] */
    @Column(value = "split_version_id")
    private Long splitVersionId;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    /** 策略类型，激进中立保守<br/>Column: [strategy_type] */
    @Column(value = "strategy_type")
    private String strategyType;

    /** 日期<br/>Column: [date] */
    @Column(value = "date")
    private LocalDate date;

    @Column(value = "year")
    private Integer year;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "half_year")
    private Integer halfYear;

    /** 区域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 机型族<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例规格<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 预测出来的当前存量(单位:核)<br/>Column: [predict_scale] */
    @Column(value = "predict_scale")
    private BigDecimal predictScale;

    /**
     * 从ZiyanCvmLongtermPredictOutputScaleSplitDO创建TreeDO实例
     * 
     * @param splitDO 源数据对象
     * @return 新的TreeDO实例
     */
    public static ZiyanCvmLongtermPredictOutputScaleSplitTreeDO createFromSplitDO(ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO) {
        if (splitDO == null) {
            return null;
        }
        
        ZiyanCvmLongtermPredictOutputScaleSplitTreeDO treeDO = new ZiyanCvmLongtermPredictOutputScaleSplitTreeDO();
        
        // 设置业务字段
        treeDO.setTaskId(splitDO.getTaskId());
        treeDO.setSplitVersionId(splitDO.getSplitVersionId());
        treeDO.setBg(splitDO.getBg());
        treeDO.setStrategyType(splitDO.getStrategyType());
        treeDO.setDate(splitDO.getDate());
        treeDO.setYear(splitDO.getYear());
        treeDO.setYearMonthStr(splitDO.getYearMonthStr());
        treeDO.setQuarter(splitDO.getQuarter());
        treeDO.setHalfYear(splitDO.getHalfYear());
        treeDO.setRegionName(splitDO.getRegionName());
        treeDO.setInstanceFamily(splitDO.getInstanceFamily());
        treeDO.setInstanceType(splitDO.getInstanceType());
        treeDO.setDeviceType(splitDO.getDeviceType());
        treeDO.setPredictScale(splitDO.getPredictScale());
        
        // 注意：BaseDO的字段（如id、createTime、updateTime等）通常不需要复制
        // 因为TreeDO是一个新的实体，应该有自己的生命周期
        
        return treeDO;
    }

}