package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;


import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_output_scale_split")
public class ZiyanCvmLongtermPredictOutputScaleSplitDO extends BaseDO {

    /** 任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 拆分id<br/>Column: [split_version_id] */
    @Column(value = "split_version_id")
    private Long splitVersionId;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    /** 策略类型，激进中立保守<br/>Column: [strategy_type] */
    @Column(value = "strategy_type")
    private String strategyType;

    /** 日期<br/>Column: [date] */
    @Column(value = "date")
    private LocalDate date;

    @Column(value = "year")
    private Integer year;

    @Column(value = "year_month_str")
    private String yearMonthStr;

    @Column(value = "quarter")
    private Integer quarter;

    @Column(value = "half_year")
    private Integer halfYear;

    /** 区域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 机型族<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例规格<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 预测出来的当前存量(单位:核)<br/>Column: [predict_scale] */
    @Column(value = "predict_scale")
    private BigDecimal predictScale;

    /**
     * 生成业务维度的唯一键（不包含日期）
     */
    public String getKeyWithOutDateStr() {
        return String.join("@", 
            java.util.Optional.ofNullable(bg).orElse(""),
            java.util.Optional.ofNullable(regionName).orElse(""),
            java.util.Optional.ofNullable(instanceFamily).orElse(""),
            java.util.Optional.ofNullable(instanceType).orElse(""),
            java.util.Optional.ofNullable(deviceType).orElse("")
        );
    }

}