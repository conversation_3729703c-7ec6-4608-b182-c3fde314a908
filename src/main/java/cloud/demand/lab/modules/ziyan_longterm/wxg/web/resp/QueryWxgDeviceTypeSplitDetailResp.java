package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleSplitDO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * WXG设备类型拆分详情查询响应
 */
@Data
public class QueryWxgDeviceTypeSplitDetailResp {
    private List<String> displayKeyColumn = new ArrayList<>();       // 显示的列名（英文）
    private List<String> displayKeyColumnName = new ArrayList<>();  // 显示的列名（中文）
    private List<WxgStrategyTypeItem> strategyTypeItems = new ArrayList<>(); // 按策略类型分组的数据项


    /**
     * WXG策略类型项
     */
    @Data
    public static class WxgStrategyTypeItem {
        private String strategyType;        // 策略类型代码
        private String strategyTypeName;    // 策略类型名称
        private WxgDateColumList dateKey;   // 日期列信息
        private List<WxgScaleItem> items = new ArrayList<>();   // 具体数据项
    }

    /**
     * WXG日期列信息
     */
    @Data
    public static class WxgDateColumList {
        private List<String> quarterDateNotStrict = new ArrayList<>();  // 季度日期
        private List<String> halfYearDateNotStrict = new ArrayList<>(); // 半年日期
        private List<String> yearDateNotStrict = new ArrayList<>();     // 年度日期
        private List<String> monthDate = new ArrayList<>();             // 月度日期
        private List<String> quarterDate = new ArrayList<>();           // 季度日期（严格）
        private List<String> halfYearDate = new ArrayList<>();          // 半年日期（严格）
        private List<String> yearDate = new ArrayList<>();              // 年度日期（严格）
    }

    /**
     * WXG规模数据项
     */
    @Data
    public static class WxgScaleItem {
        private String bg;                  // 事业群
        private String regionName;          // 地域名称
        private String instanceFamily;      // 机型族
        private String instanceType;        // 实例类型
        private String deviceType;          // 设备类型

        // 时间维度的预测规模数据
        private List<BigDecimal> quarterPredictScale = new ArrayList<>();   // 季度预测规模
        private List<BigDecimal> halfYearPredictScale = new ArrayList<>();  // 半年预测规模
        private List<BigDecimal> yearPredictScale = new ArrayList<>();      // 年度预测规模
        private List<BigDecimal> monthPredictScale = new ArrayList<>();     // 月度预测规模

        /**
         * 从ZiyanCvmLongtermPredictOutputScaleSplitDO创建WxgScaleItem
         */
        public static WxgScaleItem from(ZiyanCvmLongtermPredictOutputScaleSplitDO splitDO) {
            WxgScaleItem item = new WxgScaleItem();
            item.setBg(splitDO.getBg());
            item.setRegionName(splitDO.getRegionName());
            item.setInstanceFamily(splitDO.getInstanceFamily());
            item.setInstanceType(splitDO.getInstanceType());
            item.setDeviceType(splitDO.getDeviceType());
            return item;
        }

        /**
         * 生成业务维度的唯一键（不包含日期）
         */
        public String getKeyWithOutDateStr() {
            return String.join("@",
                    Optional.ofNullable(bg).orElse(""),
                    Optional.ofNullable(regionName).orElse(""),
                    Optional.ofNullable(instanceFamily).orElse(""),
                    Optional.ofNullable(instanceType).orElse(""),
                    Optional.ofNullable(deviceType).orElse("")
            );
        }
    }


}
