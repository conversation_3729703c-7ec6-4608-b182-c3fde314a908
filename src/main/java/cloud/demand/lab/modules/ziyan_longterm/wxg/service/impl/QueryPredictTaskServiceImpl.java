package cloud.demand.lab.modules.ziyan_longterm.wxg.service.impl;

import cloud.demand.lab.modules.longterm.predict.enums.LongtermPredictTaskStatusEnum;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputSplitVersionDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictTaskDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.QueryPredictTaskService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryCategoryAndTaskListReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QuerySplitVersionReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryTaskInfoReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryCategoryAndTaskListResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QuerySplitVersionResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryTaskInfoResp;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 查询预测任务服务实现类
 * 提供WXG中长期预测任务的查询功能实现
 * 包括任务列表查询、任务详情查询、拆分版本查询等
 */
@Service
@Slf4j
public class QueryPredictTaskServiceImpl implements QueryPredictTaskService {

    @Resource
    private DBHelper cdLabDbHelper;

    /**
     * 查询已经存在的WXG预测任务列表
     * 根据查询条件获取WXG预测任务的分类和任务列表信息，按方案ID分组并排序
     * @param req 查询请求参数，包含筛选条件
     * @return 分类和任务列表响应结果，包含任务基本信息和分类信息
     */
    @Override
    public QueryCategoryAndTaskListResp queryCategoryAndTaskList(QueryCategoryAndTaskListReq req) {
        // 查询所有启用的WXG预测任务
        List<ZiyanCvmLongtermPredictTaskDO> tasks = cdLabDbHelper.getAll(ZiyanCvmLongtermPredictTaskDO.class, "where is_enable=1 and bg='WXG'");

        // 按方案ID分组
        Map<Long, List<ZiyanCvmLongtermPredictTaskDO>> categoryList = ListUtils.toMapList(tasks, o -> o.getCategoryId(), o -> o);

        QueryCategoryAndTaskListResp resp = new QueryCategoryAndTaskListResp();
        List<QueryCategoryAndTaskListResp.Category> categoryResult = new ArrayList<>();
        resp.setCategoryList(categoryResult);

        for (List<ZiyanCvmLongtermPredictTaskDO> t : categoryList.values()) {
            // 按预测开始时间倒序排列
            ListUtils.sortDescNullLast(t, ZiyanCvmLongtermPredictTaskDO::getPredictStart);

            QueryCategoryAndTaskListResp.Category category = new QueryCategoryAndTaskListResp.Category();
            category.setCategoryId(t.get(0).getCategoryId());
            category.setCategoryName(t.get(0).getCategoryName());
            category.setModelName(t.get(0).getModelName()); // WXG使用modelName字段

            List<QueryCategoryAndTaskListResp.Task> taskList = ListUtils.transform(t, o -> {
                QueryCategoryAndTaskListResp.Task task = new QueryCategoryAndTaskListResp.Task();
                task.setTaskId(o.getId());
                task.setYearMonth(DateUtils.format(o.getPredictStart(), "yyyy-MM"));
                task.setTaskStatusCode(o.getTaskStatus());
                task.setTaskStatusName(LongtermPredictTaskStatusEnum.getNameByCode(o.getTaskStatus()));
                return task;
            });
            category.setPredictTaskList(taskList);
            categoryResult.add(category);
        }

//        // 增加还没有预测任务的方案
//        List<ZiyanCvmLongtermPredictCategoryConfigDO> categories = cdLabDbHelper.getAll(ZiyanCvmLongtermPredictCategoryConfigDO.class);
//        for (ZiyanCvmLongtermPredictCategoryConfigDO category : categories) {
//            if (!categoryList.containsKey(category.getId())) {
//                QueryCategoryAndTaskListResp.Category categoryResp = new QueryCategoryAndTaskListResp.Category();
//                categoryResp.setCategoryId(category.getId());
//                categoryResp.setCategoryName(category.getCategory());
//                categoryResp.setModelPart(category.getModelName()); // WXG使用modelName字段
//                categoryResp.setPredictTaskList(new ArrayList<>());
//                resp.getCategoryList().add(categoryResp);
//            }
//        }

        // 按方案ID升序排列
        ListUtils.sortAscNullLast(categoryResult, QueryCategoryAndTaskListResp.Category::getCategoryId);
        return resp;
    }

    /**
     * 查询任务详细信息
     * 根据任务ID获取预测任务的详细配置和执行状态信息，包括时间范围、业务范围等
     * @param req 查询任务信息的请求参数，包含任务ID
     * @return 任务详细信息响应结果，包含任务配置、执行状态、预测公式等
     */
    @Override
    public QueryTaskInfoResp queryTaskInfo(QueryTaskInfoReq req) {
        ZiyanCvmLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, req.getTaskId());
        if (taskDO == null) {
            throw new RuntimeException("预测任务不存在");
        }

        QueryTaskInfoResp resp = new QueryTaskInfoResp();
        resp.setCreateTime(DateUtils.format(taskDO.getCreateTime()));
        resp.setPredictStart(taskDO.getPredictStart() != null ? taskDO.getPredictStart().toString() : null);
        resp.setPredictEnd(taskDO.getPredictEnd() != null ? taskDO.getPredictEnd().toString() : null);

        // 从方案配置中获取相关信息
        ZiyanCvmLongtermPredictCategoryConfigDO categoryConfig = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictCategoryConfigDO.class, taskDO.getCategoryId());
        if (categoryConfig != null) {
            resp.setDimsName(categoryConfig.getDimsName());
            resp.setScopeCustomer(categoryConfig.getScopeCustomer());
            resp.setScopeResourcePool(categoryConfig.getScopeResourcePool());
            resp.setScopeProduct(categoryConfig.getScopeProduct());
            resp.setTimeDim("月");
        }

        // WXG模块的parts和modelPart处理逻辑（根据实际业务需求调整）
        resp.setModelName(taskDO.getModelName());

        // 处理出计算公式（根据WXG业务逻辑调整）
        StringBuilder formula = new StringBuilder("预测核心数=");
        formula.append("线性拟合算法[");
        formula.append("基于历史季度数据计算增长趋势，按季度推预测");
        formula.append("]");
        resp.setModelFormula(formula.toString());

        return resp;
    }

    /**
     * 查询WXG拆分版本信息
     * 根据任务ID和拆分版本ID获取拆分配置的详细信息，验证版本有效性
     * @param req 查询请求参数，包含taskId（任务ID）和splitVersionId（拆分版本ID）
     * @return 拆分版本信息响应结果，包含拆分规则、比例配置等详细信息
     * @throws RuntimeException 当任务不存在、已失效或拆分版本不匹配时抛出异常
     */
    @Override
    public QuerySplitVersionResp querySplitVersion(QuerySplitVersionReq req) {
        QuerySplitVersionResp resp = new QuerySplitVersionResp();
        Long taskId = req.getTaskId();
        ZiyanCvmLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
        if (taskDO==null||Boolean.FALSE.equals(taskDO.getIsEnable())){
            throw new RuntimeException("预测任务不存在或已失效");
        }
        ZiyanCvmLongtermPredictOutputSplitVersionDO one = cdLabDbHelper.getOne(ZiyanCvmLongtermPredictOutputSplitVersionDO.class, "where task_id=? ", taskId);
        Long splitVersionId = req.getSplitVersionId();
        if(!one.getId().equals(splitVersionId)){
            throw new RuntimeException("拆分版本错误，taskId启用拆分版本：" + one.getId() + "，请求拆分版本：" + splitVersionId);
        }
        ZiyanCvmLongtermPredictOutputSplitVersionDO splitVersion = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictOutputSplitVersionDO.class,splitVersionId);
        if(splitVersion==null){
            throw new RuntimeException("拆分版本不存在");
        }
        if(!splitVersion.getBg().equals("WXG")){
            throw new RuntimeException("拆分版本不属于WXG");
        }
        resp.setSplitVersionDO(splitVersion);
        return resp;
    }

}
