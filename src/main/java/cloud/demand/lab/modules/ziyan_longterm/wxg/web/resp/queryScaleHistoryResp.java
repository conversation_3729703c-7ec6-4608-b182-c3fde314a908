package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class queryScaleHistoryResp {

    // 数据集合
    private List<Item> data;


    // 年增速数据
    private List<IncreaseRateItem> increaseRateData=new ArrayList<>();
    
    // 线性拟合参数
    private BigDecimal slope;        // 斜率 a
    private BigDecimal intercept;    // 截距 b  
    private BigDecimal r2;           // 决定系数 r²

    @Data
    public static class Item{
        private String date;

        private String instanceFamily;

        private BigDecimal totalCore;

        private BigDecimal rate;

        private BigDecimal increaseFromLastMonth;
    }

    //年增速
    @Data
    public static class IncreaseRateItem{
        private String startDate;
        private String endDate;
        private BigDecimal increaseRate;
    }


}
