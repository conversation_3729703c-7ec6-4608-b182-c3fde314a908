package cloud.demand.lab.modules.ziyan_longterm.wxg.web;

import cloud.demand.lab.modules.ziyan_longterm.wxg.service.CreatePredictTaskService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.CreatePredictTaskReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.CreatePredictTaskResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryCategoryForCreateResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 自研WXG的CVM中长期预测
 */
@JsonrpcController("/ziyan_longterm_wxg")
@Slf4j
public class WxgCvmCreatePredictTaskController {

    @Resource
    private CreatePredictTaskService createPredictTaskService;

    /**
     * 创建预测任务
     * @param req 创建预测任务请求参数
     * @return 创建预测任务响应结果
     */
    @RequestMapping
    public CreatePredictTaskResp createPredictTask(@JsonrpcParam CreatePredictTaskReq req) {
        return createPredictTaskService.createPredictTask(req);
    }

    /**
     * 查询用于创建任务的方案列表及方案关键信息，【创建预测】弹框使用
     */
    @RequestMapping
    public QueryCategoryForCreateResp queryCategoryForCreate(@JsonrpcParam QueryCategoryForCreateReq req) {
        return createPredictTaskService.queryCategoryForCreate(req);
    }
}
