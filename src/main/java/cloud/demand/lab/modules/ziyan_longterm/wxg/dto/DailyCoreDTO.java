package cloud.demand.lab.modules.ziyan_longterm.wxg.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 日核心数据DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyCoreDTO {
    
    /**
     * 日期
     */
    @Column("day")
    private LocalDate day;
    
    /**
     * 核心数总和
     */
    @Column("sum_core")
    private BigDecimal sumCore;
}