package cloud.demand.lab.modules.ziyan_longterm.wxg.job;

import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DailyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictTaskDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.enums.Constants;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.CalculatePredictService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.RedisMsg;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 负责WXG ARIMA算法预测的异步任务处理
 */
@Slf4j
@Service
public class WxgArimaPredictTask {

    @Resource
    private RedisHelper redisHelper;
    
    @Resource
    private DBHelper cdLabDbHelper;
    
    @Resource
    private DBHelper ckcubesDBHelper;
    
    @Resource
    private CalculatePredictService calculatePredictService;

    /**
     * 处理来自消息队列中的ARIMA算法预测任务
     * 为什么用定时任务，因为生产的任务节点和web节点分离，确保任务执行异常不影响web节点
     */
    @Scheduled(fixedDelay = 1L)
    public void fromRedisMq() {
        RedisMsg msg = redisHelper.receive(Constants.REDIS_QUEUE_WXG_ARIMA_PREDICT_TASK);
        if (msg != null) {
            Long taskId = NumberUtils.parseLong(msg.getMsg());
            try {
                if (taskId != null) {
                    ZiyanCvmLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(ZiyanCvmLongtermPredictTaskDO.class, taskId);
                    if (taskDO == null) {
                        log.error("wxg arima task id {} not exist, ignore task", taskId);
                        return;
                    }
                    
                    log.info("开始执行ARIMA算法异步预测任务，taskId: {}", taskId);
                    executeWxgArimaPredict(taskDO);
                    log.info("ARIMA算法异步预测任务执行完成，taskId: {}", taskId);
                }
            } catch (Exception e) {
                log.error("run wxg arima predict task fail, taskId:{}", taskId, e);
            } finally {
                redisHelper.ack(Constants.REDIS_QUEUE_WXG_ARIMA_PREDICT_TASK, msg.getUuid()); // 只处理一次
            }
        }
    }

    /**
     * 执行ARIMA算法预测
     * @param taskDO 任务信息
     */
    public void executeWxgArimaPredict(ZiyanCvmLongtermPredictTaskDO taskDO) {
        try {
            // 1. 直接查询日数据用于ARIMA预测
            List<DailyCoreDTO> dailyResults = getDailyResultsFromCubes(taskDO.getId());
            
            if (dailyResults.isEmpty()) {
                log.warn("任务{}的日数据为空，跳过ARIMA预测", taskDO.getId());
                return;
            }
            
            // 2. 使用ARIMA算法进行日维度预测
            List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> arimaResults = 
                calculatePredictService.predictWithArimaDaily(dailyResults, taskDO.getPredictStart(), taskDO.getPredictEnd(), taskDO);
            
            // 3. 保存ARIMA算法预测结果
            saveWxgOutputScaleByAlgorithm(arimaResults, taskDO);
            
        } catch (Exception e) {
            log.error("执行ARIMA算法预测失败，taskId: {}", taskDO.getId(), e);
            throw e;
        }
    }

    /**
     * 从cubes数据库直接查询日数据用于ARIMA算法
     * @param taskId 任务ID
     * @return 日核心数据列表
     */
    @SneakyThrows
    private List<DailyCoreDTO> getDailyResultsFromCubes(Long taskId) {
        try {
            // 1. 读取日数据查询SQL
            String dailySql = IOUtils.readClasspathResourceAsString("/sql/ziyan_longterm_predict/wxg/wxg_cvm_daily_scale.sql");
            
            // 2. 从cubes数据库查询日数据
            List<DailyCoreDTO> dailyResults = ckcubesDBHelper.getRaw(DailyCoreDTO.class, dailySql);
            
            if (dailyResults == null || dailyResults.isEmpty()) {
                log.warn("从cubes数据库查询到的日数据为空，taskId: {}", taskId);
                return ListUtils.of();
            }
            
            // 3. 按日期排序
            dailyResults.sort(Comparator.comparing(DailyCoreDTO::getDay));
            
            log.info("从cubes数据库查询到{}条日数据，taskId: {}, 数据范围: {} - {}", 
                    dailyResults.size(), taskId, 
                    dailyResults.get(0).getDay(), 
                    dailyResults.get(dailyResults.size() - 1).getDay());
            
            return dailyResults;
            
        } catch (Exception e) {
            log.error("从cubes数据库查询日数据失败，taskId: {}", taskId, e);
            throw e;
        }
    }

    /**
     * 保存ARIMA算法预测结果
     * @param algorithmResults ARIMA算法预测结果列表
     * @param taskDO 任务信息
     */
    private void saveWxgOutputScaleByAlgorithm(List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> algorithmResults, ZiyanCvmLongtermPredictTaskDO taskDO) {
        if (algorithmResults == null || algorithmResults.isEmpty()) {
            log.info("ARIMA算法预测结果为空，跳过保存，taskId: {}", taskDO.getId());
            return;
        }
        
        Long taskId = taskDO.getId();
        // 删除该任务的旧ARIMA算法预测数据
        cdLabDbHelper.delete(ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO.class, "where task_id = ? and algorithm = 'ARIMA'", taskId);
        
        // 批量插入新的ARIMA算法预测数据
        int i = cdLabDbHelper.insertBatchWithoutReturnId(algorithmResults);
        if(i != algorithmResults.size()) {
            log.error("保存ARIMA算法预测数据失败，taskId: {}, 预期数据量: {}, 实际保存: {}", taskId, algorithmResults.size(), i);
        } else {
            log.info("保存ARIMA算法预测数据成功，taskId: {}, 数据量: {}", taskId, algorithmResults.size());
        }
    }


}