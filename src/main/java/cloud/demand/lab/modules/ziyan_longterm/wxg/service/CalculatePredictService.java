package cloud.demand.lab.modules.ziyan_longterm.wxg.service;

import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DailyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.MonthlyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictTaskDO;

import java.time.LocalDate;
import java.util.List;

/**
 * 预测计算服务接口
 * 提供多种算法的预测计算功能，包括线性拟合、Prophet算法、ARIMA算法等
 * 支持基于月度数据和日数据的预测计算
 */
public interface CalculatePredictService {

    /**
     * 计算线性拟合预测结果
     * 基于历史月度数据，使用线性拟合算法计算指定时间范围内的预测结果
     * @param monthlyResults 月度核心数据列表，作为预测的基础数据
     * @param predictStart 预测开始日期
     * @param predictEnd 预测结束日期
     * @param taskDO 预测任务信息，包含任务配置和参数
     * @return 线性拟合预测结果列表
     */
    List<ZiyanCvmLongtermPredictOutputScaleDO> calculatePredictResults(List<MonthlyCoreDTO> monthlyResults, LocalDate predictStart, LocalDate predictEnd, ZiyanCvmLongtermPredictTaskDO taskDO);

    /**
     * 使用Prophet算法对月度结果进行预测
     * @param monthlyResults 月度核心数据
     * @param predictStart 预测开始时间
     * @param predictEnd 预测结束时间
     * @param taskDO 任务信息
     * @return Prophet算法预测结果列表
     */
    List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictWithProphet(List<MonthlyCoreDTO> monthlyResults, LocalDate predictStart, LocalDate predictEnd, ZiyanCvmLongtermPredictTaskDO taskDO);

    /**
     * 使用Prophet算法对日数据进行预测
     * @param dailyResults 日核心数据
     * @param predictStart 预测开始时间
     * @param predictEnd 预测结束时间
     * @param taskDO 任务信息
     * @return Prophet算法预测结果列表
     */
    List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictWithProphetDaily(List<DailyCoreDTO> dailyResults, LocalDate predictStart, LocalDate predictEnd, ZiyanCvmLongtermPredictTaskDO taskDO);

    /**
     * 使用ARIMA算法对日数据进行预测
     * @param dailyResults 日核心数据
     * @param predictStart 预测开始时间
     * @param predictEnd 预测结束时间
     * @param taskDO 任务信息
     * @return ARIMA算法预测结果列表
     */
    List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictWithArimaDaily(List<DailyCoreDTO> dailyResults, LocalDate predictStart, LocalDate predictEnd, ZiyanCvmLongtermPredictTaskDO taskDO);

}
