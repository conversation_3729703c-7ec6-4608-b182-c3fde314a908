package cloud.demand.lab.modules.ziyan_longterm.wxg.algorithm;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.List;

/**
 * OLS (Ordinary Least Squares) 最小二乘法算法工具类
 * 提供线性回归相关的计算方法
 */
public class OLS {

    /**
     * 计算OLS斜率（只返回斜率）
     * 适用于简单的斜率计算场景
     * 
     * @param xs x值列表（通常为时间序列：1, 2, 3, ...）
     * @param ys y值列表（对应的数值）
     * @return 斜率值
     */
    public static BigDecimal calculateSlope(List<Integer> xs, List<Double> ys) {
        if (xs == null || ys == null || xs.size() != ys.size() || xs.size() < 2) {
            return BigDecimal.ZERO;
        }

        double xBar = xs.stream().mapToDouble(i -> i).average().orElse(0d);
        double yBar = ys.stream().mapToDouble(v -> v).average().orElse(0d);

        double numerator = 0d, denominator = 0d;
        for (int i = 0; i < xs.size(); i++) {
            double dx = xs.get(i) - xBar;
            numerator += dx * (ys.get(i) - yBar);
            denominator += dx * dx;
        }

        if (denominator == 0d) {
            return BigDecimal.ZERO;
        }
        
        return new BigDecimal(numerator / denominator, MathContext.DECIMAL64);
    }

    /**
     * 计算完整的线性回归结果（斜率、截距、决定系数）
     * 适用于需要完整回归分析的场景
     * 
     * @param xs x值列表（通常为时间序列：1, 2, 3, ...）
     * @param ys y值列表（对应的数值）
     * @return 线性回归结果对象
     */
    public static LinearRegressionResult calculateLinearRegression(List<Integer> xs, List<Double> ys) {
        if (xs == null || ys == null || xs.size() != ys.size() || xs.size() < 2) {
            return new LinearRegressionResult(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 计算均值
        double xBar = xs.stream().mapToDouble(i -> i).average().orElse(0d);
        double yBar = ys.stream().mapToDouble(v -> v).average().orElse(0d);

        // 计算斜率和截距
        double numerator = 0d, denominator = 0d;
        for (int i = 0; i < xs.size(); i++) {
            double dx = xs.get(i) - xBar;
            numerator += dx * (ys.get(i) - yBar);
            denominator += dx * dx;
        }

        BigDecimal slope = BigDecimal.ZERO;
        BigDecimal intercept = BigDecimal.ZERO;
        BigDecimal r2 = BigDecimal.ZERO;

        if (denominator != 0d) {
            // 计算斜率 a
            slope = new BigDecimal(numerator / denominator, MathContext.DECIMAL64);
            
            // 计算截距 b = yBar - slope * xBar
            intercept = new BigDecimal(yBar - slope.doubleValue() * xBar, MathContext.DECIMAL64);

            // 计算决定系数 r²
            double totalSumSquares = 0d; // 总平方和
            double residualSumSquares = 0d; // 残差平方和
            
            for (int i = 0; i < xs.size(); i++) {
                double actualY = ys.get(i);
                double predictedY = slope.doubleValue() * xs.get(i) + intercept.doubleValue();
                
                totalSumSquares += Math.pow(actualY - yBar, 2);
                residualSumSquares += Math.pow(actualY - predictedY, 2);
            }
            
            if (totalSumSquares != 0d) {
                double r2Value = 1 - (residualSumSquares / totalSumSquares);
                r2 = new BigDecimal(Math.max(0, r2Value), MathContext.DECIMAL64); // 确保r²不为负数
            }
        }

        return new LinearRegressionResult(slope, intercept, r2);
    }

    /**
     * 线性回归结果类
     * 包含斜率、截距和决定系数
     */
    public static class LinearRegressionResult {
        private final BigDecimal slope;      // 斜率 a
        private final BigDecimal intercept;  // 截距 b
        private final BigDecimal r2;         // 决定系数 r²

        public LinearRegressionResult(BigDecimal slope, BigDecimal intercept, BigDecimal r2) {
            this.slope = slope;
            this.intercept = intercept;
            this.r2 = r2;
        }

        public BigDecimal getSlope() {
            return slope;
        }

        public BigDecimal getIntercept() {
            return intercept;
        }

        public BigDecimal getR2() {
            return r2;
        }

        @Override
        public String toString() {
            return String.format("LinearRegressionResult{slope=%s, intercept=%s, r2=%s}", 
                    slope, intercept, r2);
        }
    }
}