package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;


import lombok.Data;

import java.math.BigDecimal;
import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_input_scale")
public class ZiyanCvmLongtermPredictInputScaleDO extends BaseDO {

    /** 任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    /** 日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 年月表达式<br/>Column: [year_month_str] */
    @Column(value = "year_month_str")
    private String yearMonthStr;

    /** 机型族<br/>Column: [instance_family] */
    @Column(value = "instance_family")
    private String instanceFamily;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 当前存量(单位：核)<br/>Column: [cur_core] */
    @Column(value = "cur_core")
    private BigDecimal curCore;

}