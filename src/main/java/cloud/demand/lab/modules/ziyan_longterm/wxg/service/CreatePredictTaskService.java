package cloud.demand.lab.modules.ziyan_longterm.wxg.service;

import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.CreatePredictTaskReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.QueryCategoryForCreateReq;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.CreatePredictTaskResp;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.QueryCategoryForCreateResp;

/**
 * 创建预测任务服务接口
 * 提供预测任务的创建、运行和相关配置查询功能
 * 支持任务的生命周期管理和方案配置查询
 */
public interface CreatePredictTaskService {

    /**
     * 创建预测任务
     * 根据请求参数创建新的预测任务，包括任务配置、预测参数等信息
     * @param req 创建预测任务的请求参数，包含任务名称、预测时间范围、算法类型等
     * @return 创建预测任务的响应结果，包含任务ID和创建状态
     */
    CreatePredictTaskResp createPredictTask(CreatePredictTaskReq req);

    /**
     * 运行预测任务
     * 执行指定ID的预测任务，启动预测计算流程
     * @param taskId 任务ID，用于标识要运行的预测任务
     */
    public void doRunPredictTask(Long taskId);

    /**
     * 查询用于创建任务的方案列表及方案关键信息
     * 为【创建预测】弹框提供可选的方案列表和相关配置信息
     * @param req 查询方案的请求参数，包含筛选条件
     * @return 方案列表响应结果，包含可用方案及其关键配置信息
     */
    QueryCategoryForCreateResp queryCategoryForCreate(QueryCategoryForCreateReq req);
}
