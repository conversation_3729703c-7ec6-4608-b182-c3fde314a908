package cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PredictTotalResp {

    /**
     * 按年份分组的净增数据
     */
    private List<WxgYearItem> wxgYearItems;

    @Data
    public static class WxgYearItem {
        private Integer year;

        /**
         * 该年份下不同策略类型的净增量数据
         */
        private List<WxgStrategyItem> wxgStrategyItems;
    }

    @Data
    public static class WxgStrategyItem {

        private String strategyType;

        /**
         * 模型预测：净需求量
         */
        private BigDecimal increasePredictTotal;

        /**
         * 业务提报:净需求量（总量=净需求-退回）
         */
        private BigDecimal demandTotal;

        /**
         * 业务提报：需求
         */
        private BigDecimal demandNetTotal;

        /**
         * 业务提报：退回
         */
        private BigDecimal demandReturnTotal;


        /**
         * 业务提报总量进度
         */
        private BigDecimal demandTotalProgressRatio;

        /**
         * 需求执行：存量相减量（存量最后一个月1日数据-当年1月1日数据
         */
        private BigDecimal increasePredictTotalProgress;

        /**
         * 需求执行：存量相减进度（相减的量/预测总量）
         */
        private BigDecimal increasePredictTotalProgressRatio;


        /**
         * 需求执行：存量相减最新数据（被减数）
         */
        private BigDecimal latestStockValue;

        /**
         * 需求执行：当年1月1日数据（减数）
         */
        private BigDecimal currentYearStockValue;

        /**
         * 需求执行：存量相减最新数据对应的日期
         */
        private String latestStockDate;

        /**
         * 需求执行：存量相减当年1月1日数据对应的日期
         */
        private String currentYearStockDate;


        /**
         * 需求执行需求-退回总量（总量=净需求-退回）
         */
        private BigDecimal ExecuteTotal;

        /**
         * 需求执行需求-退回净需求
         */
        private BigDecimal ExecuteNetTotal;

        /**
         * 需求执行需求-退回退回
         */
        private BigDecimal ExecuteReturnTotal;

        /**
         * 需求-退回执行进度
         */
        private BigDecimal executeTotalProgressRatio;

        /**
         * buffer需求-退回
         */
        private BigDecimal bufferTotal;

        /**
         * buffer需求
         */
        private BigDecimal bufferDemand;

        /**
         * buffer退回
         */
        private BigDecimal bufferReturn;

    }

}



