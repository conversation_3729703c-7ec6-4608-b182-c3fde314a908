package cloud.demand.lab.modules.ziyan_longterm.wxg.web;

import cloud.demand.lab.modules.ziyan_longterm.wxg.service.ScaleHistoryAndPredictService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.WxgCvmSplitService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.req.*;
import cloud.demand.lab.modules.ziyan_longterm.wxg.web.resp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

/**
 * 存量历史和预测值
 */
@JsonrpcController("/ziyan_longterm_wxg")
@Slf4j
public class WxgCvmScaleHistoryAndPredictController {

    @Resource
    private ScaleHistoryAndPredictService scaleHistoryAndPredictService;

    @Resource
    private WxgCvmSplitService wxgCvmSplitService;

    /**
     * 查询存量历史，按机型分配
     * @param req 查询请求参数
     * @return 查询结果
     */
    @RequestMapping
    public queryScaleHistoryResp queryScaleHistoryWithInstance(@JsonrpcParam queryScaleHistoryReq req) {
        return scaleHistoryAndPredictService.queryScaleHistoryWithInstance(req);
    }

    /**
     * 查询预测柱状图，按机型分配
     * @param req
     * @return
     */
    @RequestMapping
    public queryScalePredictResp queryScalePredictWithInstance(@JsonrpcParam queryScaleHistoryReq req) {
        return scaleHistoryAndPredictService.queryScalePredictWithInstance(req);
    }

    @RequestMapping
    public queryScaleHistoryResp queryScaleHistory(@JsonrpcParam queryScaleHistoryReq req) {
        return scaleHistoryAndPredictService.queryScaleHistory(req);
    }

    /**
     * 查询预测
     * @param req 查询请求参数
     * @return 查询结果
     */
    @RequestMapping
    public queryScalePredictResp queryScalePredict(@JsonrpcParam queryScaleHistoryReq req) {
        return scaleHistoryAndPredictService.queryScalePredict(req);
    }

    /**
     * 计算未来半年、一年、一年半的需求量
     * @param req 查询请求参数
     * @return 需求量计算结果
     */
    @RequestMapping
    public HalfYearAccumulateResp QueryHalfYearAccumulate(@JsonrpcParam HalfYearAccumulateReq req) {
        return scaleHistoryAndPredictService.QueryHalfYearAccumulate(req);
    }

    /**
     * 计算预测当年、次年、第三年的净增总量数据
     * @param req 查询请求参数
     * @return 年度净增量计算结果
     */
    @RequestMapping
    public PredictTotalResp QueryPredictTotal(@JsonrpcParam PredictTotalReq req) {
        return scaleHistoryAndPredictService.QueryPredictTotal(req);
    }

    /**
     * 根据任务ID查询预测结果，按年月-算法类型聚合
     * @param req 查询请求参数
     * @return 预测结果数据
     */
    @RequestMapping
    public QueryPredictResultByTaskResp queryPredictResultByAlgorithm(@JsonrpcParam QueryPredictResultByTaskReq req) {
        return scaleHistoryAndPredictService.queryPredictResultByAlgorithm(req);
    }

    @RequestMapping
    public QueryWxgDeviceTypeSplitDetailResp queryWxgDeviceTypeSplitDetail(@JsonrpcParam QueryWxgDeviceTypeSplitDetailReq req) {
        return wxgCvmSplitService.queryWxgDeviceTypeSplitDetail(req);
    }
}
