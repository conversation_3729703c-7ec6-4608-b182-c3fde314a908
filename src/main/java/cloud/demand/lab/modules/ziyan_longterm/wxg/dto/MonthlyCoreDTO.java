package cloud.demand.lab.modules.ziyan_longterm.wxg.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 按年月分组的Core累加结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonthlyCoreDTO {

    /**
     * 年月，格式：YYYY-MM
     */
    private String yearMonth;

    /**
     * 累加的总Core数
     */
    private BigDecimal totalCore;

    /**
     * 预测的Core数
     */
    private BigDecimal predictCore;
}