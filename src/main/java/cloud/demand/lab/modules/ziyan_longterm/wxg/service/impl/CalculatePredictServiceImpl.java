package cloud.demand.lab.modules.ziyan_longterm.wxg.service.impl;

import cloud.demand.lab.modules.ziyan_longterm.wxg.algorithm.*;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DailyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.MonthlyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictOutputScaleDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.entity.ZiyanCvmLongtermPredictTaskDO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.service.CalculatePredictService;
import cloud.demand.lab.modules.ziyan_longterm.wxg.utils.QuarterUtils;
import cloud.demand.lab.modules.ziyan_longterm.wxg.utils.QuarterUtils.Quarter;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预测计算服务实现类
 * 提供多种算法的预测计算功能实现，包括线性拟合、Prophet算法、ARIMA算法等
 * 支持基于月度数据和日数据的预测计算，使用滚动窗口和季度拟合策略
 */
@Service
@Slf4j
public class CalculatePredictServiceImpl implements CalculatePredictService {

    /**
     * 按滚动过去一年资源增长趋势预测
     * eg1：26年Q1每月资源预测用24年1月-12月增长率预估，每月 82万核心。
     * eg2：26年Q2每月资源预测用24年4月-25年3月增长率预估，每月 121.5万核心。
     * eg3：26年Q3每月资源预测用24年7月-25年6月增长率预估，每月 * 万核心。
     * @param monthlyResults
     * @param predictStart
     * @param predictEnd
     * @return
     */
    public List<ZiyanCvmLongtermPredictOutputScaleDO> calculatePredictResults(List<MonthlyCoreDTO> monthlyResults, LocalDate predictStart, LocalDate predictEnd, ZiyanCvmLongtermPredictTaskDO taskDO) {

        //1.获取需要预测的所有季度
        List<QuarterUtils.Quarter> quartersInRange = QuarterUtils.getQuartersInRange(predictStart, predictEnd, true, true);

        //2.对于需要预测的每个Q，计算出每个月净增核心数（斜率a)
            //将预测的月份加入monthlyResults，作为新的存量为后续预测做存量累加
        Map<Quarter, BigDecimal> quarterBigDecimalMap = calculateQuarterlySlopes(monthlyResults, quartersInRange);

        //3.对于需要预测的每个Q，从monthlyResults中最后一个月开始进行增量累加，直到预测结束
        List<ZiyanCvmLongtermPredictOutputScaleDO> predictResults = new ArrayList<>();

        // 获取最后一个月的存量作为预测起点
        BigDecimal lastMonthCore = BigDecimal.ZERO;
        if (!monthlyResults.isEmpty()) {
            // 按年月排序，获取最后一个月的存量
            monthlyResults.sort(Comparator.comparing(MonthlyCoreDTO::getYearMonth));
            lastMonthCore = monthlyResults.get(monthlyResults.size() - 1).getTotalCore();
        }

        // 生成预测期间的所有月份
        YearMonth currentMonth = YearMonth.from(predictStart);
        YearMonth endMonth = YearMonth.from(predictEnd);
        BigDecimal currentCore = lastMonthCore;

        while (!currentMonth.isAfter(endMonth)) {
            // 确定当前月份所属的季度
            Quarter currentQuarter = QuarterUtils.getQuarter(currentMonth.atDay(1));

            // 获取该季度的斜率（每月增长量）
            BigDecimal monthlyGrowth = quarterBigDecimalMap.getOrDefault(currentQuarter, BigDecimal.ZERO);

            // 计算当前月的预测值：前一月存量 + 月增长量
            currentCore = currentCore.add(monthlyGrowth);

            // 创建预测结果对象
            ZiyanCvmLongtermPredictOutputScaleDO predictResult = new ZiyanCvmLongtermPredictOutputScaleDO();
            predictResult.setStatTime(currentMonth.atDay(1)); // 设置为当月第一天
            predictResult.setYearMonthStr(currentMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            predictResult.setCurCore(currentCore);
            predictResult.setBg("WXG"); // 设置事业群
            predictResult.setStrategyType("MIDDLE"); // 策略类型(目前只支持MIDDLE）
            predictResult.setTaskId(taskDO.getId());
            predictResult.setChangeCurCoreFromLastMonth(monthlyGrowth);
            predictResults.add(predictResult);

            // 移动到下一个月
            currentMonth = currentMonth.plusMonths(1);
        }

        // 返回所有预测结果
        return predictResults;
    }


    /**
     * 计算每个季度的线性拟合斜率 a（单位：每月新增Core）。
     * 规则：Y-Q 的拟合窗口 = 从 (Y-2, Q起始月) 开始的连续12个月【仅用真实存量】做 OLS。
     * 关键新增：若某季度窗口的“窗口结束月” > 最后真实月份，则从该季度起，全部季度斜率固定为最近一次成功拟合得到的斜率（若没有则为0）。
     */
    public static Map<Quarter, BigDecimal> calculateQuarterlySlopes(
            List<MonthlyCoreDTO> monthlyResults,
            List<Quarter> quarters) {

        if (quarters == null || quarters.isEmpty()) return Collections.emptyMap();
        if (monthlyResults == null || monthlyResults.isEmpty())
            throw new IllegalArgumentException("monthlyResults 不能为空");

        final DateTimeFormatter YM = DateTimeFormatter.ofPattern("yyyy-MM");

        // 历史累计：YYYY-MM -> totalCore（只含“真实存量”）
        Map<YearMonth, BigDecimal> actualCum = new HashMap<>();
        for (MonthlyCoreDTO dto : monthlyResults) {
            if (dto == null || dto.getYearMonth() == null || dto.getTotalCore() == null) continue;
            actualCum.put(YearMonth.parse(dto.getYearMonth(), YM), dto.getTotalCore());
        }
        if (actualCum.isEmpty()) throw new IllegalArgumentException("历史累计数据为空");

        // “最后一个有真实存量的月份”
        YearMonth lastActualYM = actualCum.keySet().stream().max(Comparator.naturalOrder()).get();

        Map<Quarter, BigDecimal> out = new LinkedHashMap<>();

        boolean freeze = false;                 // 一旦触发，后续季度都用 frozenSlope
        BigDecimal frozenSlope = null;          // 最近一次成功拟合得到的斜率

        for (Quarter q : quarters) {
            int startMonth = quarterStartMonth(q.getQuarter());              // Q1->1, Q2->4, Q3->7, Q4->10
            YearMonth winStart = YearMonth.of(q.getYear() - 2, startMonth);  // 窗口起点
            YearMonth winEnd   = winStart.plusMonths(11);                    // 窗口终点 = 起点+11

            BigDecimal slope;

            // 触发冻结条件：窗口终点超出最后真实月份 → 不再拟合，直接沿用 frozenSlope
            if (freeze || winEnd.isAfter(lastActualYM)) {
                slope = (frozenSlope != null) ? frozenSlope : BigDecimal.ZERO;
                freeze = true; // 从此以后全部季度沿用该斜率
            } else {
                // 窗口完整落在真实数据范围内：用窗口内已有月份做 OLS（需要≥2个点）
                List<Integer> xs = new ArrayList<>(12); // 1..12
                List<Double> ys = new ArrayList<>(12);  // 对应累计值

                YearMonth ym = winStart;
                for (int i = 1; i <= 12; i++, ym = ym.plusMonths(1)) {
                    BigDecimal val = actualCum.get(ym);
                    if (val != null) {
                        xs.add(i);
                        ys.add(val.doubleValue());
                    }
                }

                if (xs.size() >= 2) {
                    slope = olsSlope(xs, ys);   // 成功拟合
                    frozenSlope = slope;        // 更新“最近一次可用斜率”
                } else {
                    // 虽在范围内但点数不足：本季度斜率=0，但不更新 frozenSlope
                    slope = BigDecimal.ZERO;
                }
            }

            out.put(q, slope);
        }

        return out;
    }
    /* ======== 小而美的私有函数 ======== */

    // OLS 斜率（标准最小二乘，x 为 1..N，y 为累计值）
    private static BigDecimal olsSlope(List<Integer> xs, List<Double> ys) {
        return OLS.calculateSlope(xs, ys);
    }

    // 季度起始月映射
    private static int quarterStartMonth(String q) {
        switch (q) {
            case "Q1": return 1;
            case "Q2": return 4;
            case "Q3": return 7;
            case "Q4": return 10;
            default: throw new IllegalArgumentException("Invalid quarter: " + q);
        }
    }

    /**
     * 使用Prophet算法对月度结果进行预测
     * @param monthlyResults 月度核心数据
     * @param predictStart 预测开始时间
     * @param predictEnd 预测结束时间
     * @param taskDO 任务信息
     * @return Prophet算法预测结果列表
     */
    public List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictWithProphet(
            List<MonthlyCoreDTO> monthlyResults, 
            LocalDate predictStart, 
            LocalDate predictEnd, 
            ZiyanCvmLongtermPredictTaskDO taskDO) {
        
        log.info("开始使用Prophet算法进行预测，taskId: {}", taskDO.getId());
        
        try {
            // 1. 将monthlyResults转换为DateNumDTO格式
            List<DateNumDTO> historicalData = monthlyResults.stream()
                    .sorted(Comparator.comparing(MonthlyCoreDTO::getYearMonth))
                    .map(dto -> new DateNumDTO(dto.getYearMonth() + "-01", dto.getTotalCore()))
                    .collect(Collectors.toList());
            
            if (historicalData.isEmpty()) {
                log.warn("历史数据为空，无法进行Prophet预测，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 2. 计算需要预测的月份数量
            YearMonth startMonth = YearMonth.from(predictStart);
            YearMonth endMonth = YearMonth.from(predictEnd);
            int monthsToPredict = (int) startMonth.until(endMonth.plusMonths(1), java.time.temporal.ChronoUnit.MONTHS);
            
            if (monthsToPredict <= 0) {
                log.warn("预测月份数量为0，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 3. 调用Prophet算法进行预测
            PredictResult predictResult = PROPHET.predict(historicalData, monthsToPredict);
            
            if (predictResult == null || predictResult.getData() == null || predictResult.getData().isEmpty()) {
                log.warn("Prophet预测结果为空，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 4. 将预测结果转换为数据库实体
            List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> results = new ArrayList<>();
            YearMonth currentMonth = startMonth;
            
            for (int i = 0; i < predictResult.getData().size() && !currentMonth.isAfter(endMonth); i++) {
                DateNumDTO prediction = predictResult.getData().get(i);
                
                ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO result = new ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO();
                result.setTaskId(taskDO.getId());
                result.setAlgorithm("PROPHET");
                result.setBg("WXG");
                result.setStatTime(currentMonth.atDay(1));
                result.setYearMonthStr(currentMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
                result.setCurCore(prediction.getValue());
                
                // 计算与上个月的增量
                if (i == 0 && !historicalData.isEmpty()) {
                    // 第一个预测月份，与历史数据最后一个月比较
                    DateNumDTO lastHistorical = historicalData.get(historicalData.size() - 1);
                    result.setChangeCurCoreFromLastMonth(prediction.getValue().subtract(lastHistorical.getValue()));
                } else if (i > 0) {
                    // 后续月份，与前一个预测月份比较
                    DateNumDTO previousPrediction = predictResult.getData().get(i - 1);
                    result.setChangeCurCoreFromLastMonth(prediction.getValue().subtract(previousPrediction.getValue()));
                } else {
                    result.setChangeCurCoreFromLastMonth(BigDecimal.ZERO);
                }
                
                results.add(result);
                currentMonth = currentMonth.plusMonths(1);
            }
            
            log.info("Prophet算法预测完成，taskId: {}, 预测结果数量: {}", taskDO.getId(), results.size());
            return results;
            
        } catch (Exception e) {
            log.error("Prophet算法预测失败，taskId: {}", taskDO.getId(), e);
            throw new RuntimeException("Prophet算法预测失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用Prophet算法对日数据进行预测
     * @param dailyResults 日核心数据
     * @param predictStart 预测开始时间
     * @param predictEnd 预测结束时间
     * @param taskDO 任务信息
     * @return Prophet算法预测结果列表
     */
    public List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictWithProphetDaily(
            List<DailyCoreDTO> dailyResults, 
            LocalDate predictStart, 
            LocalDate predictEnd, 
            ZiyanCvmLongtermPredictTaskDO taskDO) {
        
        log.info("开始使用Prophet算法进行日维度预测，taskId: {}", taskDO.getId());
        
        try {
            // 1. 将dailyResults转换为DateNumDTO格式
            List<DateNumDTO> historicalData = dailyResults.stream()
                    .sorted(Comparator.comparing(DailyCoreDTO::getDay))
                    .map(dto -> new DateNumDTO(dto.getDay().toString(), dto.getSumCore()))
                    .collect(Collectors.toList());
            
            if (historicalData.isEmpty()) {
                log.warn("历史日数据为空，无法进行Prophet预测，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 2. 计算需要预测的天数（包含起始和结束日期）
            long daysToPredict = java.time.temporal.ChronoUnit.DAYS.between(predictStart, predictEnd) + 1;
            
            if (daysToPredict <= 0) {
                log.warn("预测天数为0，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            log.info("Prophet日维度预测参数，taskId: {}, 预测开始日期: {}, 预测结束日期: {}, 预测天数: {}", 
                    taskDO.getId(), predictStart, predictEnd, daysToPredict);
            
            // 3. 调用Prophet算法进行预测
            PredictResult predictResult = PROPHET.predict(historicalData, (int) daysToPredict);
            
            if (predictResult == null || predictResult.getData() == null || predictResult.getData().isEmpty()) {
                log.warn("Prophet日维度预测结果为空，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 4. 将预测结果转换为数据库实体（按日存储）
            List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> results = new ArrayList<>();
            LocalDate currentDate = predictStart;
            
            for (int i = 0; i < predictResult.getData().size() && !currentDate.isAfter(predictEnd); i++) {
                DateNumDTO prediction = predictResult.getData().get(i);
                
                ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO result = new ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO();
                result.setTaskId(taskDO.getId());
                result.setAlgorithm("PROPHET");
                result.setBg("WXG");
                result.setStatTime(currentDate);
                result.setYearMonthStr(currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                result.setCurCore(prediction.getValue());
                
                // 计算与上一天的增量
                if (i == 0 && !historicalData.isEmpty()) {
                    // 第一个预测日，与历史数据最后一天比较
                    DateNumDTO lastHistorical = historicalData.get(historicalData.size() - 1);
                    result.setChangeCurCoreFromLastMonth(prediction.getValue().subtract(lastHistorical.getValue()));
                } else if (i > 0) {
                    // 后续日期，与前一个预测日比较
                    DateNumDTO previousPrediction = predictResult.getData().get(i - 1);
                    result.setChangeCurCoreFromLastMonth(prediction.getValue().subtract(previousPrediction.getValue()));
                } else {
                    result.setChangeCurCoreFromLastMonth(BigDecimal.ZERO);
                }
                
                results.add(result);
                currentDate = currentDate.plusDays(1);
            }
            
            log.info("Prophet日维度预测完成，taskId: {}, 预测结果数量: {}", taskDO.getId(), results.size());
            return results;
            
        } catch (Exception e) {
            log.error("Prophet日维度预测失败，taskId: {}", taskDO.getId(), e);
            throw new RuntimeException("Prophet日维度预测失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用ARIMA算法对日数据进行预测
     * @param dailyResults 日核心数据
     * @param predictStart 预测开始时间
     * @param predictEnd 预测结束时间
     * @param taskDO 任务信息
     * @return ARIMA算法预测结果列表
     */
    public List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> predictWithArimaDaily(
            List<DailyCoreDTO> dailyResults, 
            LocalDate predictStart, 
            LocalDate predictEnd, 
            ZiyanCvmLongtermPredictTaskDO taskDO) {
        
        log.info("开始使用ARIMA算法进行日维度预测，taskId: {}", taskDO.getId());
        
        try {
            // 1. 将dailyResults转换为DateNumDTO格式
            List<DateNumDTO> historicalData = dailyResults.stream()
                    .sorted(Comparator.comparing(DailyCoreDTO::getDay))
                    .map(dto -> new DateNumDTO(dto.getDay().toString(), dto.getSumCore()))
                    .collect(Collectors.toList());
            
            if (historicalData.isEmpty()) {
                log.warn("历史日数据为空，无法进行ARIMA预测，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 2. 计算需要预测的天数（包含起始和结束日期）
            long daysToPredict = java.time.temporal.ChronoUnit.DAYS.between(predictStart, predictEnd) + 1;
            
            if (daysToPredict <= 0) {
                log.warn("预测天数为0，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            log.info("ARIMA日维度预测参数，taskId: {}, 预测开始日期: {}, 预测结束日期: {}, 预测天数: {}", 
                    taskDO.getId(), predictStart, predictEnd, daysToPredict);
            
            // 3. 设置ARIMA参数，使用自动选参（-1表示自动选参）
            List<Integer> arimaParams = ListUtils.of(2,2,2);
            
            // 4. 调用ARIMA算法进行预测
            PredictResult predictResult = ARIMA.predict(historicalData, (int) daysToPredict, arimaParams);
            
            if (predictResult == null || predictResult.getData() == null || predictResult.getData().isEmpty()) {
                log.warn("ARIMA日维度预测结果为空，taskId: {}", taskDO.getId());
                return new ArrayList<>();
            }
            
            // 5. 将预测结果转换为数据库实体（按日存储）
            List<ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO> results = new ArrayList<>();
            LocalDate currentDate = predictStart;
            
            for (int i = 0; i < predictResult.getData().size() && !currentDate.isAfter(predictEnd); i++) {
                DateNumDTO prediction = predictResult.getData().get(i);
                
                ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO result = new ZiyanCvmLongtermPredictOutputScaleByAlgorithmDO();
                result.setTaskId(taskDO.getId());
                result.setAlgorithm("ARIMA");
                result.setBg("WXG");
                result.setStatTime(currentDate);
                result.setYearMonthStr(currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                result.setCurCore(prediction.getValue());
                
                // 计算与上一天的增量
                if (i == 0 && !historicalData.isEmpty()) {
                    // 第一个预测日，与历史数据最后一天比较
                    DateNumDTO lastHistorical = historicalData.get(historicalData.size() - 1);
                    result.setChangeCurCoreFromLastMonth(prediction.getValue().subtract(lastHistorical.getValue()));
                } else if (i > 0) {
                    // 后续日期，与前一个预测日比较
                    DateNumDTO previousPrediction = predictResult.getData().get(i - 1);
                    result.setChangeCurCoreFromLastMonth(prediction.getValue().subtract(previousPrediction.getValue()));
                } else {
                    result.setChangeCurCoreFromLastMonth(BigDecimal.ZERO);
                }
                
                results.add(result);
                currentDate = currentDate.plusDays(1);
            }
            
            log.info("ARIMA日维度预测完成，taskId: {}, 预测结果数量: {}", taskDO.getId(), results.size());
            return results;
            
        } catch (Exception e) {
            log.error("ARIMA日维度预测失败，taskId: {}", taskDO.getId(), e);
            throw new RuntimeException("ARIMA日维度预测失败: " + e.getMessage(), e);
        }
    }
}
