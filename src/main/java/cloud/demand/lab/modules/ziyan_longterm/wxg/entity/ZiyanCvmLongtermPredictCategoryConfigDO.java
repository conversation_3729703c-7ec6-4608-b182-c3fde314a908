package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;

import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_category_config")
public class ZiyanCvmLongtermPredictCategoryConfigDO extends BaseDO {

    /** 方案名称<br/>Column: [category] */
    @Column(value = "category")
    private String category;

    /** 预测时间颗粒度，6=半年<br/>Column: [interval_month] */
    @Column(value = "interval_month")
    private Integer intervalMonth;

    /** 预测粒度<br/>Column: [dims_name] */
    @Column(value = "dims_name")
    private String dimsName;

    /** 客户范围<br/>Column: [scope_customer] */
    @Column(value = "scope_customer")
    private String scopeCustomer;

    /** 资源池<br/>Column: [scope_resource_pool] */
    @Column(value = "scope_resource_pool")
    private String scopeResourcePool;

    /** 产品范围<br/>Column: [scope_product] */
    @Column(value = "scope_product")
    private String scopeProduct;

    /** 模型名称<br/>Column: [model_name] */
    @Column(value = "model_name")
    private String modelName;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    /** 输入原始规模数据的表，这里仅用于标识，不会实际参与sql<br/>Column: [table_name] */
    @Column(value = "table_name")
    private String tableName;

    /** 查询规模数据条件sql，会参与实际sql<br/>Column: [where_sql] */
    @Column(value = "where_sql")
    private String whereSql;

    /** 预测开始时间，CUR_MONTH表示当月<br/>Column: [predict_start] */
    @Column(value = "predict_start")
    private String predictStart;

    /** 预测结束时间，填yyyy-MM<br/>Column: [predict_end] */
    @Column(value = "predict_end")
    private String predictEnd;

}