package cloud.demand.lab.modules.ziyan_longterm.wxg.utils;

import lombok.Data;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 季度工具类,目前还在测试
 */
public class QuarterUtils {

    /**
     * 月份信息类
     */
    @Data
    public static class MonthInfo {
        public final YearMonth yearMonth;  // 年月对象
        public final String monthStr;      // yyyy-MM格式字符串
        public final String monthName;     // 月份英文名称
        public final int quarter;          // 所属季度(1-4)
        public final int daysInMonth;      // 当月天数
        public final LocalDate firstDay;   // 当月第一天
        public final LocalDate lastDay;    // 当月最后一天
        public final int days;
        public final int workingDays;      // 当月工作日数（排除周末）

        public MonthInfo(YearMonth yearMonth) {
            this.yearMonth = yearMonth;
            this.monthStr = yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            this.monthName = yearMonth.getMonth().name();
            this.quarter = calculateQuarter(yearMonth.getMonthValue());
            this.daysInMonth = yearMonth.lengthOfMonth();
            this.firstDay = yearMonth.atDay(1);
            this.lastDay = yearMonth.atEndOfMonth();
            this.days=yearMonth.lengthOfMonth();
            this.workingDays = calculateWorkingDays(yearMonth);
        }

        /**
         * 计算月份所属季度
         *
         * @param month 月份(1-12)
         * @return 季度(1-4)
         */
        private int calculateQuarter(int month) {
            return (month - 1) / 3 + 1;
        }

        /**
         * 计算当月工作日数（排除周末）
         *
         * @param yearMonth 年月对象
         * @return 工作日数量
         */
        private int calculateWorkingDays(YearMonth yearMonth) {
            LocalDate start = yearMonth.atDay(1);
            LocalDate end = yearMonth.atEndOfMonth();
            int workingDays = 0;

            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                DayOfWeek day = date.getDayOfWeek();
                if (day != DayOfWeek.SATURDAY && day != DayOfWeek.SUNDAY) {
                    workingDays++;
                }
            }
            return workingDays;
        }

        /**
         * 验证日期是否在当月范围内
         *
         * @param date 待验证日期
         * @return 是否在当月范围内
         */
        public boolean isDateInMonth(LocalDate date) {
            return !date.isBefore(firstDay) && !date.isAfter(lastDay);
        }

        /**
         * 获取日期进度百分比
         *
         * @param date 当前日期
         * @return 当月进度百分比(0.0-1.0)
         */
        public double getProgressPercentage(LocalDate date) {
            if (!isDateInMonth(date)) {
                throw new IllegalArgumentException("Date not in this month");
            }
            int dayOfMonth = date.getDayOfMonth();
            return (double) dayOfMonth / daysInMonth;
        }
    }

    /**
     * 季度信息类
     */
    @Data
    public static class Quarter {
        public final LocalDate startTime;   // 季度开始日期
        public final LocalDate endTime;     // 季度结束日期
        public final int year;               // 年份
        public final String quarter;         // 季度标识(Q1-Q4)
        public final List<MonthInfo> monthList; // 包含的月份列表

        public Quarter(int year, String quarter) {
            this.year = year;
            this.quarter = quarter;

            // 计算季度开始日期、结束日期和月份列表
            switch (quarter) {
                case "Q1":
                    this.startTime = LocalDate.of(year, 1, 1);
                    this.endTime = LocalDate.of(year, 3, 31);
                    this.monthList = IntStream.rangeClosed(1, 3)
                            .mapToObj(month -> new QuarterUtils.MonthInfo(YearMonth.of(year, month)))
                            .collect(Collectors.toList());
                    break;
                case "Q2":
                    this.startTime = LocalDate.of(year, 4, 1);
                    this.endTime = LocalDate.of(year, 6, 30);
                    this.monthList = IntStream.rangeClosed(4, 6)
                            .mapToObj(month -> new QuarterUtils.MonthInfo(YearMonth.of(year, month)))
                            .collect(Collectors.toList());
                    break;
                case "Q3":
                    this.startTime = LocalDate.of(year, 7, 1);
                    this.endTime = LocalDate.of(year, 9, 30);
                    this.monthList = IntStream.rangeClosed(7, 9)
                            .mapToObj(month -> new QuarterUtils.MonthInfo(YearMonth.of(year, month)))
                            .collect(Collectors.toList());
                    break;
                case "Q4":
                    this.startTime = LocalDate.of(year, 10, 1);
                    this.endTime = LocalDate.of(year, 12, 31);
                    this.monthList = IntStream.rangeClosed(10, 12)
                            .mapToObj(month -> new QuarterUtils.MonthInfo(YearMonth.of(year, month)))
                            .collect(Collectors.toList());
                    break;
                default:
                    throw new IllegalArgumentException("Invalid quarter: " + quarter);
            }
        }


        /**
         * 判断当前季度是否在另一个季度之前
         *
         * @param other 另一个季度
         * @return 是否在当前季度之前
         */
        public boolean isBefore(Quarter other) {
            return this.year < other.year ||
                  (this.year == other.year &&
                   this.quarter.compareTo(other.quarter) < 0);
        }

        /**
         * 判断当前季度是否在另一个季度之后
         *
         * @param other 另一个季度
         * @return 是否在当前季度之后
         */
        public boolean isAfter(Quarter other) {
            return this.year > other.year ||
                  (this.year == other.year &&
                   this.quarter.compareTo(other.quarter) > 0);
        }
    }

    /**
     * 解析多种格式的日期输入
     * 支持格式：LocalDate, yyyy-MM, yyyy-MM-dd, yyyy-MM-dd HH:mm:ss, YearMonth
     *
     * @param input 日期输入对象
     * @return 解析后的LocalDate
     * @throws IllegalArgumentException 如果输入格式无效
     */
    private static LocalDate parseDate(Object input) {
        if (input == null) {
            throw new IllegalArgumentException("Input cannot be null");
        }
        if (input instanceof LocalDate) {
            return (LocalDate) input;
        } else if (input instanceof YearMonth) {
            return ((YearMonth) input).atDay(1);
        } else if (input instanceof String) {
            String dateStr = ((String) input).trim();

            try {
                // 尝试解析为yyyy-MM-dd格式
                return LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (DateTimeParseException e1) {
                try {
                    // 尝试解析为yyyy-MM格式
                    YearMonth ym = YearMonth.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM"));
                    return ym.atDay(1);
                } catch (DateTimeParseException e2) {
                    try {
                        // 尝试解析为yyyy-MM-dd HH:mm:ss格式
                        return LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toLocalDate();
                    } catch (DateTimeParseException e3) {
                        throw new IllegalArgumentException("Unsupported date format: " + dateStr + ". Expected yyyy-MM, yyyy-MM-dd, or yyyy-MM-dd HH:mm:ss");
                    }
                }
            }
        } else {
            throw new IllegalArgumentException("Unsupported input type: " + input.getClass().getSimpleName());
        }
    }


    /**
     * 获取指定日期所属的季度信息
     *
     * @param object 查询日期
     * @return 季度对象
     */
    public static Quarter getQuarter(Object object) {
        LocalDate date = parseDate(object);
        int year = date.getYear();
        int month = date.getMonthValue();

        String quarter;
        if (month <= 3) quarter = "Q1";
        else if (month <= 6) quarter = "Q2";
        else if (month <= 9) quarter = "Q3";
        else quarter = "Q4";

        return new Quarter(year, quarter);
    }


    /**
     * 根据年份和季度标识获取季度信息
     *
     * @param year 年份
     * @param quarter 季度标识(Q1-Q4)
     * @return 季度对象
     */
    public static Quarter getQuarterByIdentifier(int year, String quarter) {
        return new Quarter(year, quarter);
    }

    /**
     * 获取指定日期范围内的所有季度
     *
     * @param startInput 起始日期（支持多种格式）
     * @param endInput 结束日期（支持多种格式）
     * @param includeStart 是否包含开始日期所在的季度
     * @param includeEnd 是否包含结束日期所在的季度
     * @return 季度列表（按时间正序）
     */
    public static List<Quarter> getQuartersInRange(Object startInput, Object endInput, boolean includeStart, boolean includeEnd) {
        LocalDate start = parseDate(startInput);  // 解析开始日期
        LocalDate end = parseDate(endInput);      // 解析结束日期
        List<Quarter> quarters = new ArrayList<>();

        int startYear = start.getYear();
        int endYear = end.getYear();

        // 判断开始季度
        String startQuarter = getQuarter(start).getQuarter();  // 获取开始日期对应的季度
        Quarter current = new Quarter(startYear, startQuarter);

        // 处理是否包含开始日期所在的季度
        if (includeStart) {
            if (current.getStartTime().isBefore(start) || current.getStartTime().isEqual(start)) {
                quarters.add(current);
            }
        } else {
            if (current.getStartTime().isAfter(start)) {
                quarters.add(current);
            }
        }

        // 循环获取每个季度
        while (startYear <= endYear) {
            String nextQuarter = (current.getQuarter().equals("Q1")) ? "Q2" : (current.getQuarter().equals("Q2")) ? "Q3" : (current.getQuarter().equals("Q3")) ? "Q4" : "Q1";
            if (nextQuarter.equals("Q1")) {
                startYear++;  // 如果是 Q4，切换到下一年
            }

            current = new Quarter(startYear, nextQuarter);

            // 处理是否包含结束日期所在的季度
            if (includeEnd) {
                if (current.getEndTime().isAfter(end) || current.getEndTime().isEqual(end)) {
                    quarters.add(current);
                    break;  // 结束处理
                } else {
                    quarters.add(current);
                }
            } else {
                if (current.getEndTime().isBefore(end)) {
                    quarters.add(current);
                }
            }
        }

        return quarters;
    }


    /**
     * 获取指定日期前N个季度（包含当前季度）
     *
     * @param date 基准日期
     * @param n 季度数量
     * @param includeCurrent 是否包含当前季度
     * @return 季度列表（按时间倒序）
     */
    private static List<Quarter> getPreviousQuarters(LocalDate date, int n, boolean includeCurrent) {
        validateParameters(n);  // 校验参数 n 是否有效
        Quarter current = getQuarter(date);  // 获取当前季度
        List<Quarter> result = new ArrayList<>();

        // 如果需要包括当前季度，将其添加到结果列表
        if (includeCurrent) {
            result.add(current);
            n--;  // 如果包含当前季度，就减少要计算的季度数量
        }

        // 循环获取前 n 个季度
        for (int i = 0; i < n; i++) {
            current = getLastQuarter(current.getYear(), current.getQuarter());  // 获取上一个季度
            result.add(current);  // 将计算出来的季度添加到结果列表
        }

        return result;
    }

    /**
     * 获取指定日期前N个季度（支持多种日期格式）
     *
     * @param dateInput 基准日期（支持LocalDate, String, YearMonth）
     * @param n 季度数量
     * @param includeCurrent 是否包含当前季度
     * @return 季度列表（按时间倒序）
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static List<Quarter> getPreviousQuarters(Object dateInput, int n, boolean includeCurrent) {
        LocalDate date = parseDate(dateInput);
        return getPreviousQuarters(date, n, includeCurrent);
    }

    /**
     * 获取指定日期后N个季度（包含当前季度）
     *
     * @param date 基准日期
     * @param n 季度数量
     * @param includeCurrent 是否包含当前季度
     * @return 季度列表（按时间正序）
     */
    private static List<Quarter> getNextQuarters(LocalDate date, int n, boolean includeCurrent) {
        validateParameters(n);  // 验证参数 n 是否有效
        Quarter current = getQuarter(date);  // 获取当前季度
        List<Quarter> result = new ArrayList<>();

        // 如果需要包括当前季度，将其添加到结果列表
        if (includeCurrent) {
            result.add(current);
            n--;  // 如果包含当前季度，就减少要计算的季度数量
        }

        // 循环获取接下来的 n 个季度
        for (int i = 0; i < n; i++) {
            current = getNextQuarter(current.getYear(), current.getQuarter());  // 获取下一个季度
            result.add(current);  // 将计算出来的季度添加到结果列表
        }

        return result;
    }

    /**
     * 获取指定日期后N个季度（支持多种日期格式）
     *
     * @param dateInput 基准日期（支持LocalDate, String, YearMonth）
     * @param n 季度数量
     * @param includeCurrent 是否包含当前季度
     * @return 季度列表（按时间正序）
     * @throws IllegalArgumentException 如果输入格式无效
     */
    public static List<Quarter> getNextQuarters(Object dateInput, int n, boolean includeCurrent) {
        LocalDate date = parseDate(dateInput);
        return getNextQuarters(date, n, includeCurrent);
    }


    /**
     * 获取上一个季度
     * @param year 当前年份
     * @param quarter 当前季度（Q1, Q2, Q3, Q4）
     * @return 上一个季度的 Quarter 对象
     */
    public static Quarter getLastQuarter(int year, String quarter) {
        if (quarter == null || (!quarter.equals("Q1") && !quarter.equals("Q2") && !quarter.equals("Q3") && !quarter.equals("Q4"))) {
            throw new IllegalArgumentException("Invalid quarter: " + quarter);
        }

        String previousQuarter;
        int previousYear;

        // 如果当前是 Q1，上一个季度应该是上一年 Q4
        if (quarter.equals("Q1")) {
            previousYear = year - 1;
            previousQuarter = "Q4";
        }
        // 如果当前是 Q2，Q3 或 Q4，直接计算前一个季度
        else {
            previousYear = year;
            previousQuarter = "Q" + (Integer.parseInt(quarter.substring(1)) - 1);  // 计算前一个季度
        }

        return new Quarter(previousYear, previousQuarter);
    }

    /**
     * 获取下一个季度
     * @param year 当前年份
     * @param quarter 当前季度（Q1, Q2, Q3, Q4）
     * @return 下一个季度的 Quarter 对象
     */
    public static Quarter getNextQuarter(int year, String quarter) {
        if (quarter == null || (!quarter.equals("Q1") && !quarter.equals("Q2") && !quarter.equals("Q3") && !quarter.equals("Q4"))) {
            throw new IllegalArgumentException("Invalid quarter: " + quarter);
        }

        String nextQuarter;
        int nextYear;

        // 如果当前是 Q4，下一个季度应该是下一年 Q1
        if (quarter.equals("Q4")) {
            nextYear = year + 1;
            nextQuarter = "Q1";
        }
        // 如果当前是 Q1, Q2, Q3，直接计算下一个季度
        else {
            nextYear = year;
            nextQuarter = "Q" + (Integer.parseInt(quarter.substring(1)) + 1);  // 计算下一个季度
        }

        return new Quarter(nextYear, nextQuarter);
    }

    /**
     * 验证参数有效性
     *
     * @param n 季度数量
     * @throws IllegalArgumentException 如果n为负数
     */
    private static void validateParameters(int n) {
        if (n < 0) {
            throw new IllegalArgumentException("n must be non-negative");
        }
    }
}
