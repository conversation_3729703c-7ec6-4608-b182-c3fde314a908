package cloud.demand.lab.modules.ziyan_longterm.wxg.entity;

import cloud.demand.lab.common.entity.BaseDO;
import lombok.Data;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import java.time.LocalDateTime;


import lombok.Data;
import lombok.ToString;

import cloud.demand.lab.common.entity.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@Table("ziyan_cvm_longterm_predict_task")
public class ZiyanCvmLongtermPredictTaskDO extends BaseDO {

    /** 方案id<br/>Column: [category_id] */
    @Column(value = "category_id")
    private Long categoryId;

    /** 方案名称<br/>Column: [category_name] */
    @Column(value = "category_name")
    private String categoryName;

    /** 是否启用<br/>Column: [is_enable] */
    @Column(value = "is_enable")
    private Boolean isEnable;

    /** 模型名称<br/>Column: [model_name] */
    @Column(value = "model_name")
    private String modelName;

    /** 任务状态<br/>Column: [task_status] */
    @Column(value = "task_status")
    private String taskStatus;

    /** 创建者<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    /** 预测开始日期，一般以月末日期为代表<br/>Column: [predict_start] */
    @Column(value = "predict_start")
    private LocalDate predictStart;

    /** 预测结束日期，一般以月末日期为代表<br/>Column: [predict_end] */
    @Column(value = "predict_end")
    private LocalDate predictEnd;

    /** 条件sql，一般情况下是不含时间范围的，它仅是业务范围<br/>Column: [condition_sql] */
    @Column(value = "condition_sql")
    private String conditionSql;

    /** 实际产生输入的sql<br/>Column: [input_sql] */
    @Column(value = "input_sql")
    private String inputSql;

    /** 输入数据是否使用缓存，0不使用（即等于强制更新），1使用（默认）<br/>Column: [use_cache] */
    @Column(value = "use_cache")
    private Boolean useCache;

    /** 规模输入是否实际用了缓存<br/>Column: [input_actual_use_cache] */
    @Column(value = "input_actual_use_cache")
    private Boolean inputActualUseCache;

    /** 预测时间颗粒度，6=半年<br/>Column: [interval_month] */
    @Column(value = "interval_month")
    private Integer intervalMonth;

    /** 预测粒度<br/>Column: [dims_name] */
    @Column(value = "dims_name")
    private String dimsName;

    /** 客户范围<br/>Column: [scope_customer] */
    @Column(value = "scope_customer")
    private String scopeCustomer;

    /** 资源池<br/>Column: [scope_resource_pool] */
    @Column(value = "scope_resource_pool")
    private String scopeResourcePool;

    /** 产品范围<br/>Column: [scope_product] */
    @Column(value = "scope_product")
    private String scopeProduct;

    /** bg事业群<br/>Column: [bg] */
    @Column(value = "bg")
    private String bg;

    /** 任务运行失败的错误信息<br/>Column: [err_msg] */
    @Column(value = "err_msg")
    private String errMsg;

    /** 任务运行开始时间<br/>Column: [task_start_time] */
    @Column(value = "task_start_time")
    private LocalDateTime taskStartTime;

    /** 任务运行结束时间<br/>Column: [task_end_time] */
    @Column(value = "task_end_time")
    private LocalDateTime taskEndTime;

    /** 预测任务执行（不含拆分）的毫秒数，只有成功跑完才写入数据，失败不写<br/>Column: [cost_ms] */
    @Column(value = "cost_ms")
    private Integer costMs;

    /** 拆分的耗时，只有成功跑完才写入数据，失败不写，只记录第一次默认拆分的耗时<br/>Column: [split_cost_ms] */
    @Column(value = "split_cost_ms")
    private Integer splitCostMs;

    /** 运行任务的ip地址<br/>Column: [run_ip] */
    @Column(value = "run_ip")
    private String runIp;

    /** 任务备注<br/>Column: [run_note] */
    @Column(value = "run_note")
    private String runNote;

}