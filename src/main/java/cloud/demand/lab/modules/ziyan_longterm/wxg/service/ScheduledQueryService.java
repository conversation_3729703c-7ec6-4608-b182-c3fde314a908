package cloud.demand.lab.modules.ziyan_longterm.wxg.service;

import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DailyCoreDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.DemandTotalDTO;
import cloud.demand.lab.modules.ziyan_longterm.wxg.dto.ReturnTotalDTO;

import java.util.List;

/**
 * 定时查询服务接口
 * 提供各种定时任务相关的数据查询功能
 */
public interface ScheduledQueryService {

    /**
     * 获取最后一天的库存数据
     * @return 最后一天的核心库存数据
     */
    DailyCoreDTO getLastDayStock();

    /**
     * 获取指定年份的需求总量数据
     * @param currentYear 查询年份
     * @return 需求总量数据列表
     */
    List<DemandTotalDTO> getDemandTotal(Integer currentYear);

    /**
     * 获取指定年份的退还总量数据
     * @param currentYear 查询年份
     * @return 退还总量数据列表
     */
    List<ReturnTotalDTO> getReturnTotal(Integer currentYear);

    /**
     * 获取指定年份的需求缓冲数据
     * @param currentYear 查询年份
     * @return 需求缓冲数据列表
     */
    List<DemandTotalDTO> getDemandBuffer(Integer currentYear);

    /**
     * 获取指定年份的退还缓冲数据
     * @param currentYear 查询年份
     * @return 退还缓冲数据列表
     */
    List<ReturnTotalDTO> getReturnBuffer(Integer currentYear);

    /**
     * 获取指定年份的计划外退还总量数据
     * @param currentYear 查询年份
     * @return 计划外退还总量数据列表
     */
    List<ReturnTotalDTO> getOutPlanReturnTotal(Integer currentYear);
}
