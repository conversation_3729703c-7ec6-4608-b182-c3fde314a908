package cloud.demand.lab.modules.ziyan_longterm.wxg.algorithm;

import cloud.demand.lab.common.config.DynamicProperties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.nutz.lang.Lang;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class PROPHET {

    public static PredictResult predict(List<DateNumDTO> data, int n) {
        try {
            List<DateNumDTO> result = invokeProphet(data, n);
            return new PredictResult(result);
        } catch (Throwable e) {
            log.error("forecast call fail", e);
            throw e;
        }
    }

    /**
     * 调用prophet
     */
    @SneakyThrows
    private static List<DateNumDTO> invokeProphet(List<DateNumDTO> data, int n) {
        String prophetUrl = DynamicProperties.getCrpUrl() + "/cloud-demand-arima/prophet";
        String url = String.format("%s?n=%d",prophetUrl, n);

        OkHttpClient client = new OkHttpClient();
        MediaType mediaType = MediaType.parse("text/plain");
        String collect = data.stream()
                .map((o) -> o.getDate() + "," + o.getValue())
                .collect(Collectors.joining("\n"));
        RequestBody body = RequestBody.create(mediaType, "Date,Value\n" + collect);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "text/plain")
                .build();

        Response response = null;
        try {
            response = client.newCall(request).execute();
            while (response.code() == 502) {
                response = client.newCall(request).execute();
            }
        } catch (Exception e) {
            response = client.newCall(request).execute();
        }

        String retsString = Objects.requireNonNull(response.body()).string();

        String[] split = retsString.split("\n");
        
        // 过滤空行和无效行
        List<String> filteredData = new ArrayList<>();
        for (String line : split) {
            if (line != null && !line.trim().isEmpty() && !line.trim().equals("")) {
                filteredData.add(line.trim());
            }
        }
        
        log.info("Prophet预测返回数据处理，原始行数: {}, 过滤后行数: {}, 期望行数: {}, taskUrl: {}", 
                split.length, filteredData.size(), n, url);

        if (filteredData.size() != n) {
            throw Lang.makeThrow("prophet返回行数不一致,返回：%s\\n, url： %s\\n, 参数：\\n%s，原始行数：%d，过滤后行数：%d，期望行数：%d", 
                    retsString, url, "Date,Value\\n" + collect, split.length, filteredData.size(), n);
        }
        
        List<String> retData = filteredData;
        try {
            return retData.stream().map((o) -> {
                        String[] tmp = o.split("\\s+");
                        return new DateNumDTO(tmp[0], new BigDecimal(tmp[1]));
                    })
                    .map((o) -> {
                        if (o.getValue().compareTo(BigDecimal.ZERO) < 0) { // 目前预测的序列都是>=0，因此小于0的会被处理为0
                            return new DateNumDTO(o.getDate(), BigDecimal.ZERO);
                        }
                        return o;
                    }).collect(Collectors.toList());
        } catch (Exception e) {
            throw Lang.makeThrow(String.valueOf(retData));
        }
    }

}