package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.common.utils.LoginUtils;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictInputScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutScaleDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutSplitVersionDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictTaskDO;
import cloud.demand.lab.modules.longterm.cos.service.CosSplitService;
import cloud.demand.lab.modules.longterm.cos.vo.CosLongtermPredictOutPurchaseSplitVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CosSplitServiceImpl implements CosSplitService {

    @Resource
    private DBHelper cdLabDbHelper;

    @Override
    @Transactional(value = "cdlabTransactionManager")
    public Long split(Long taskId) {
        // 1. 查询任务信息并创建拆分版本
        CosLongtermPredictTaskDO taskDO = cdLabDbHelper.getByKey(CosLongtermPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new BizException("预测任务不存在，taskId: " + taskId);
        }
        log.info("开始拆分任务，taskId: {}, categoryName: {}", taskId, taskDO.getCategoryName());
        
        // 创建拆分版本
        CosLongtermPredictOutSplitVersionDO splitVersionDO = createSplitVersion(taskDO);
        Long splitVersionId = splitVersionDO.getId();
        
        log.info("创建拆分版本成功，splitVersionId: {}", splitVersionId);

        // 2. 计算从当前月份开始到当前半年结束、以及后续每半年的增量
        List<CosLongtermPredictOutPurchaseSplitVO> purchaseSplitList = calculatePurchaseSplit(taskId);

        // 3. 按Prophet算法的月份占比拆分月份的增量


        // 4. 按照最近1年的地域占比拆分


        // 5. 写入拆分结果表中

        return splitVersionId;
    }

    private CosLongtermPredictOutSplitVersionDO createSplitVersion(CosLongtermPredictTaskDO taskDO) {
        CosLongtermPredictOutSplitVersionDO splitVersionDO = new CosLongtermPredictOutSplitVersionDO();
        splitVersionDO.setTaskId(taskDO.getId());
        splitVersionDO.setName("默认拆分版本_" + LocalDateTime.now().toString().substring(0, 19));
        splitVersionDO.setNote("");
        splitVersionDO.setCreator(LoginUtils.getUserName());

        cdLabDbHelper.insert(splitVersionDO);
        return splitVersionDO;
    }

    /**
     * 计算从当前月份开始到当前半年结束、以及后续每半年的增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> calculatePurchaseSplit(Long taskId) {
        // 1）找到cos_longterm_predict_input_scale中当前任务的最新的date，月末日期
        LocalDate inputDateLatest = getInputDateLatest(taskId);

        // 2）通过cos_longterm_predict_input_scale找到inputDateLatest这一天的存量规模
        Map<String, BigDecimal> baseScaleMap = getBaseScale(taskId, inputDateLatest);

        // 3）对于cos_longterm_predict_out_scale中当前任务的预测结果，按strategy_type分开进行
        return calculateIncrements(taskId, inputDateLatest, baseScaleMap);
    }

    /**
     * 找到cos_longterm_predict_input_scale中当前任务的最新的date
     * 如果date是每月最后一天，那么就取这一天；否则取这个天的上一个月的最后一天
     */
    private LocalDate getInputDateLatest(Long taskId) {
        // 查询当前任务的最新日期
        LocalDate latestDate = cdLabDbHelper.getRawOne(LocalDate.class,
            "SELECT MAX(date) FROM cos_longterm_predict_input_scale WHERE task_id = ?", taskId);
        if (latestDate == null) {
            throw new BizException("未找到任务的输入数据，taskId: " + taskId);
        }

        LocalDate lastDayOfMonth = latestDate.with(TemporalAdjusters.lastDayOfMonth());
        if (latestDate.equals(lastDayOfMonth)) {
            return latestDate;
        } else {
            return latestDate.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        }
    }

    /**
     * 获取inputDateLatest这一天的is_out_customer=1和=0的cur_scale存量规模
     */
    private Map<String, BigDecimal> getBaseScale(Long taskId, LocalDate inputDateLatest) {
        List<CosLongtermPredictInputScaleDO> inputScaleList = cdLabDbHelper.getAll(
            CosLongtermPredictInputScaleDO.class,
            "WHERE task_id = ? AND date = ? AND is_out_customer IN (0, 1)", taskId, inputDateLatest);
        if (ListUtils.isEmpty(inputScaleList)) {
            throw new BizException("未找到基准日期的存量数据，taskId: " + taskId + ", date: " + inputDateLatest);
        }

        // 按is_out_customer分组，构建key为"is_out_customer"的Map
        Map<String, BigDecimal> baseScaleMap = new HashMap<>();
        for (CosLongtermPredictInputScaleDO inputScale : inputScaleList) {
            String key = String.valueOf(inputScale.getIsOutCustomer());
            baseScaleMap.put(key, inputScale.getCurScale());
        }

        return baseScaleMap;
    }

    /**
     * 计算预测结果的净增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> calculateIncrements(Long taskId, LocalDate inputDateLatest,
                                                                          Map<String, BigDecimal> baseScaleMap) {
        List<CosLongtermPredictOutPurchaseSplitVO> result = new ArrayList<>();

        // 查询预测结果数据
        List<CosLongtermPredictOutScaleDO> predictScaleList = cdLabDbHelper.getAll(
            CosLongtermPredictOutScaleDO.class, "where task_id = ?", taskId);
        if (ListUtils.isEmpty(predictScaleList)) {
            log.warn("未找到预测结果数据，taskId: {}", taskId);
            return result;
        }

        // 按strategy_type和is_out_customer分组
        Map<String, List<CosLongtermPredictOutScaleDO>> groupedData = new HashMap<>();
        for (CosLongtermPredictOutScaleDO predictScale : predictScaleList) {
            String groupKey = predictScale.getStrategyType() + "_" + predictScale.getIsOutCustomer();
            groupedData.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(predictScale);
        }

        // 对每个组合进行处理
        for (Map.Entry<String, List<CosLongtermPredictOutScaleDO>> entry : groupedData.entrySet()) {
            String groupKey = entry.getKey();
            List<CosLongtermPredictOutScaleDO> groupData = entry.getValue();

            String[] keyParts = groupKey.split("_");
            String strategyType = keyParts[0];
            Boolean isOutCustomer = Boolean.valueOf(keyParts[1]);

            // 计算该组合的增量
            List<CosLongtermPredictOutPurchaseSplitVO> groupResult = calculateGroupIncrements(
                taskId, strategyType, isOutCustomer, groupData, inputDateLatest, baseScaleMap);
            result.addAll(groupResult);
        }

        return result;
    }

    /**
     * 计算单个strategy_type和is_out_customer组合的增量
     */
    private List<CosLongtermPredictOutPurchaseSplitVO> calculateGroupIncrements(Long taskId, String strategyType,
                                                                               Boolean isOutCustomer,
                                                                               List<CosLongtermPredictOutScaleDO> groupData,
                                                                               LocalDate inputDateLatest,
                                                                               Map<String, BigDecimal> baseScaleMap) {
        List<CosLongtermPredictOutPurchaseSplitVO> result = new ArrayList<>();

        // 按日期排序
        ListUtils.sortAscNullLast(groupData, CosLongtermPredictOutScaleDO::getDate);

        // 获取基准存量
        String baseKey = String.valueOf(isOutCustomer ? 1 : 0);
        BigDecimal previousScale = baseScaleMap.getOrDefault(baseKey, BigDecimal.ZERO);
        LocalDate previousDate = inputDateLatest;

        // 逐个计算净增量
        for (CosLongtermPredictOutScaleDO predictScale : groupData) {
            LocalDate currentDate = predictScale.getDate();
            BigDecimal currentScale = predictScale.getPredictScale();

            // 计算净增量 = 当前预测存量 - 上一个日期的存量
            BigDecimal increment = currentScale.subtract(previousScale);

            // 构造CosLongtermPredictOutPurchaseSplitVO
            CosLongtermPredictOutPurchaseSplitVO splitVO = createPurchaseSplitVO(
                taskId, strategyType, isOutCustomer, previousDate, currentDate, increment);

            // 设置过程信息
            splitVO.setPreviousScale(previousScale);
            splitVO.setCurrentScale(currentScale);

            result.add(splitVO);

            // 更新为下一次计算的基准
            previousScale = currentScale;
            previousDate = currentDate;
        }

        return result;
    }

    /**
     * 创建CosLongtermPredictOutPurchaseSplitVO对象
     */
    private CosLongtermPredictOutPurchaseSplitVO createPurchaseSplitVO(Long taskId, String strategyType,
                                                                      Boolean isOutCustomer, LocalDate startDate,
                                                                      LocalDate endDate, BigDecimal increment) {
        CosLongtermPredictOutPurchaseSplitVO splitVO = new CosLongtermPredictOutPurchaseSplitVO();

        // 设置基本信息
        splitVO.setTaskId(taskId);
        splitVO.setStrategyType(strategyType);
        splitVO.setIsOutCustomer(isOutCustomer);
        splitVO.setDate(endDate); // 以结束日期作为记录日期
        splitVO.setPurchaseStorage(increment); // 净增量作为采购存储量

        // splitDO的4个日期关联字段暂不设置

        // 设置过程信息字段
        splitVO.setStartDate(startDate);
        splitVO.setEndDate(endDate);

        // 设置过程信息到splitLog中
        String splitLog = String.format("计算净增量[计算期间:%s至%s, 净增量:%sPB, 策略类型:%s, 外部客户:%s] ",
                startDate, endDate, increment, strategyType, isOutCustomer);
        splitVO.setSplitLog(splitLog);

        return splitVO;
    }
}
