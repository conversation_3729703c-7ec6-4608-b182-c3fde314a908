package cloud.demand.lab.modules.longterm.cos.vo;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictOutPurchaseSplitDO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class CosLongtermPredictOutPurchaseSplitVO extends CosLongtermPredictOutPurchaseSplitDO {

    /** 开始时间（用于记录计算过程信息） */
    private LocalDate startDate;

    /** 结束时间（用于记录计算过程信息） */
    private LocalDate endDate;

    /** 上期存量（用于记录计算过程信息） */
    private BigDecimal previousScale;

    /** 当期存量（用于记录计算过程信息） */
    private BigDecimal currentScale;

}
